<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mainView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/md_grey_100"
    tools:context=".SettingsActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:elevation="0dp">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/toolbarLayout"
            android:layout_width="match_parent"
            android:layout_height="180dp"
            app:title="Settings"
            android:background="@color/md_white_1000"
            app:collapsedTitleTextAppearance="@style/AT4SEND.AppTheme.TextAppearance.Title.Collapsed"
            app:expandedTitleTextAppearance="@style/AT4SEND.AppTheme.TextAppearance.Title.Expanded"
            app:layout_scrollFlags="exitUntilCollapsed|scroll"
            android:fitsSystemWindows="true"
            app:expandedTitleMarginStart="16dp">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                app:contentInsetStartWithNavigation="0dp"
                app:layout_collapseMode="pin"/>

        </com.google.android.material.appbar.CollapsingToolbarLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/md_grey_300"/>
    </com.google.android.material.appbar.AppBarLayout>
    <FrameLayout
        android:id="@+id/settings"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

    </FrameLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>