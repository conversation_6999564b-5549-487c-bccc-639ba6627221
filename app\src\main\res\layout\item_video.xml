<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/itemView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:clickable="true"
        android:focusable="true"
        android:foreground="?attr/selectableItemBackground">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginEnd="16dp">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/videoTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                tools:text="Video Title"
                android:textSize="17sp"
                android:textAppearance="@style/AT4SEND.AppTheme.TextAppearance.Subtitle"
                android:textColor="@color/md_blue_700"
                android:textFontWeight="500"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/videoShortDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                tools:text="Video Short description of technology with space for 4 lines of description. Video Short description of technology with space for 4 lines of description. Video Short description of technology with space for 4 lines of description."
                android:maxLines="4"
                android:ellipsize="end"
                />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginTop="20dp">

                <View
                    android:layout_width="8dp"
                    android:layout_height="1dp"
                    android:background="@color/md_grey_600"
                    android:layout_marginEnd="5dp"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/videoCreator"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    tools:text="Paul Whittington"
                    android:textSize="12sp"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:textStyle="italic"
                    />

            </LinearLayout>

        </LinearLayout>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/selectionIcon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            app:srcCompat="@drawable/ic_yoututbe"
            app:tint="@color/md_red_800"/>
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginStart="16dp"
        android:background="@color/md_grey_200"/>

</LinearLayout>