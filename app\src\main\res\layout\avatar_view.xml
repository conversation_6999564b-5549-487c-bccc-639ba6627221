<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/text_avatar_name"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_avatar_text"
        android:gravity="center"
        tools:text="FS"
        android:textAllCaps="true"
        android:textColor="@android:color/white"
        android:textSize="12sp"
        />

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:cardCornerRadius="0dp">
        
        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/round_img_avatar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"/>
            
    </androidx.cardview.widget.CardView>

</merge>
