  Context android.content  Intent android.content  ACTION_SEND android.content.Intent  EXTRA_EMAIL android.content.Intent  
EXTRA_SUBJECT android.content.Intent  
EXTRA_TEXT android.content.Intent  Intent android.content.Intent  android android.content.Intent  apply android.content.Intent  arrayOf android.content.Intent  
createChooser android.content.Intent  
getANDROID android.content.Intent  getAPPLY android.content.Intent  
getARRAYOf android.content.Intent  
getAndroid android.content.Intent  getApply android.content.Intent  
getArrayOf android.content.Intent  getTYPE android.content.Intent  getType android.content.Intent  putExtra android.content.Intent  setType android.content.Intent  type android.content.Intent  Bundle 
android.os  Html android.text  Spanned android.text  clear android.text.Editable  toString android.text.Editable  FROM_HTML_MODE_LEGACY android.text.Html  fromHtml android.text.Html  LayoutInflater android.view  View android.view  	ViewGroup android.view  inflate android.view.LayoutInflater  findViewById android.view.View  setOnClickListener android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  Toast android.widget  setOnClickListener android.widget.Button  setOnClickListener android.widget.TextView  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  setOnClickListener )androidx.appcompat.widget.AppCompatButton  Pair androidx.core.util  Fragment androidx.fragment.app  Bundle androidx.fragment.app.Fragment  	Exception androidx.fragment.app.Fragment  Intent androidx.fragment.app.Fragment  LayoutInflater androidx.fragment.app.Fragment  MaterialButton androidx.fragment.app.Fragment  R androidx.fragment.app.Fragment  TextInputEditText androidx.fragment.app.Fragment  Toast androidx.fragment.app.Fragment  View androidx.fragment.app.Fragment  	ViewGroup androidx.fragment.app.Fragment  android androidx.fragment.app.Fragment  apply androidx.fragment.app.Fragment  arrayOf androidx.fragment.app.Fragment  buildString androidx.fragment.app.Fragment  findNavController androidx.fragment.app.Fragment  isEmpty androidx.fragment.app.Fragment  
isNotEmpty androidx.fragment.app.Fragment  
onViewCreated androidx.fragment.app.Fragment  sendFeedback androidx.fragment.app.Fragment  
startActivity androidx.fragment.app.Fragment  trim androidx.fragment.app.Fragment  
NavController androidx.navigation  navigate !androidx.navigation.NavController  findNavController androidx.navigation.fragment  	Exception !com.eduability.at4send2.fragments  Intent !com.eduability.at4send2.fragments  R !com.eduability.at4send2.fragments  Toast !com.eduability.at4send2.fragments  TrainingFeedbackFragment !com.eduability.at4send2.fragments  TrainingFragment !com.eduability.at4send2.fragments  android !com.eduability.at4send2.fragments  apply !com.eduability.at4send2.fragments  arrayOf !com.eduability.at4send2.fragments  buildString !com.eduability.at4send2.fragments  findNavController !com.eduability.at4send2.fragments  isEmpty !com.eduability.at4send2.fragments  
isNotEmpty !com.eduability.at4send2.fragments  trim !com.eduability.at4send2.fragments  Bundle :com.eduability.at4send2.fragments.TrainingFeedbackFragment  	Exception :com.eduability.at4send2.fragments.TrainingFeedbackFragment  Intent :com.eduability.at4send2.fragments.TrainingFeedbackFragment  LayoutInflater :com.eduability.at4send2.fragments.TrainingFeedbackFragment  MaterialButton :com.eduability.at4send2.fragments.TrainingFeedbackFragment  R :com.eduability.at4send2.fragments.TrainingFeedbackFragment  TextInputEditText :com.eduability.at4send2.fragments.TrainingFeedbackFragment  Toast :com.eduability.at4send2.fragments.TrainingFeedbackFragment  View :com.eduability.at4send2.fragments.TrainingFeedbackFragment  	ViewGroup :com.eduability.at4send2.fragments.TrainingFeedbackFragment  android :com.eduability.at4send2.fragments.TrainingFeedbackFragment  apply :com.eduability.at4send2.fragments.TrainingFeedbackFragment  arrayOf :com.eduability.at4send2.fragments.TrainingFeedbackFragment  buildString :com.eduability.at4send2.fragments.TrainingFeedbackFragment  context :com.eduability.at4send2.fragments.TrainingFeedbackFragment  
getANDROID :com.eduability.at4send2.fragments.TrainingFeedbackFragment  getAPPLY :com.eduability.at4send2.fragments.TrainingFeedbackFragment  
getARRAYOf :com.eduability.at4send2.fragments.TrainingFeedbackFragment  
getAndroid :com.eduability.at4send2.fragments.TrainingFeedbackFragment  getApply :com.eduability.at4send2.fragments.TrainingFeedbackFragment  
getArrayOf :com.eduability.at4send2.fragments.TrainingFeedbackFragment  getBUILDString :com.eduability.at4send2.fragments.TrainingFeedbackFragment  getBuildString :com.eduability.at4send2.fragments.TrainingFeedbackFragment  
getCONTEXT :com.eduability.at4send2.fragments.TrainingFeedbackFragment  
getContext :com.eduability.at4send2.fragments.TrainingFeedbackFragment  
getISEmpty :com.eduability.at4send2.fragments.TrainingFeedbackFragment  
getISNotEmpty :com.eduability.at4send2.fragments.TrainingFeedbackFragment  
getIsEmpty :com.eduability.at4send2.fragments.TrainingFeedbackFragment  
getIsNotEmpty :com.eduability.at4send2.fragments.TrainingFeedbackFragment  getTRIM :com.eduability.at4send2.fragments.TrainingFeedbackFragment  getTrim :com.eduability.at4send2.fragments.TrainingFeedbackFragment  improvementsInput :com.eduability.at4send2.fragments.TrainingFeedbackFragment  isEmpty :com.eduability.at4send2.fragments.TrainingFeedbackFragment  
isNotEmpty :com.eduability.at4send2.fragments.TrainingFeedbackFragment  
likesInput :com.eduability.at4send2.fragments.TrainingFeedbackFragment  sendFeedback :com.eduability.at4send2.fragments.TrainingFeedbackFragment  
setContext :com.eduability.at4send2.fragments.TrainingFeedbackFragment  
startActivity :com.eduability.at4send2.fragments.TrainingFeedbackFragment  submitButton :com.eduability.at4send2.fragments.TrainingFeedbackFragment  trim :com.eduability.at4send2.fragments.TrainingFeedbackFragment  Bundle 2com.eduability.at4send2.fragments.TrainingFragment  LayoutInflater 2com.eduability.at4send2.fragments.TrainingFragment  R 2com.eduability.at4send2.fragments.TrainingFragment  View 2com.eduability.at4send2.fragments.TrainingFragment  	ViewGroup 2com.eduability.at4send2.fragments.TrainingFragment  findNavController 2com.eduability.at4send2.fragments.TrainingFragment  getFINDNavController 2com.eduability.at4send2.fragments.TrainingFragment  getFindNavController 2com.eduability.at4send2.fragments.TrainingFragment  MaterialButton "com.google.android.material.button  setOnClickListener 1com.google.android.material.button.MaterialButton  TextInputEditText %com.google.android.material.textfield  getTEXT 7com.google.android.material.textfield.TextInputEditText  getText 7com.google.android.material.textfield.TextInputEditText  setText 7com.google.android.material.textfield.TextInputEditText  text 7com.google.android.material.textfield.TextInputEditText  Answer 	java.lang  AssistiveTechnologyArticle 	java.lang  AssistiveTechnologyTraining 	java.lang  AssistiveTechnologyVideo 	java.lang  IllegalStateException 	java.lang  Intent 	java.lang  NoSuchFieldException 	java.lang  Pair 	java.lang  QuizQuestionBank 	java.lang  R 	java.lang  
StringBuilder 	java.lang  Toast 	java.lang  android 	java.lang  apply 	java.lang  arrayOf 	java.lang  buildString 	java.lang  	hashMapOf 	java.lang  isEmpty 	java.lang  
isNotEmpty 	java.lang  let 	java.lang  listOf 	java.lang  to 	java.lang  trim 	java.lang  append java.lang.StringBuilder  
getISNotEmpty java.lang.StringBuilder  
getIsNotEmpty java.lang.StringBuilder  
isNotEmpty java.lang.StringBuilder  HashMap 	java.util  get java.util.AbstractMap  get java.util.HashMap  Answer kotlin  Array kotlin  AssistiveTechnologyArticle kotlin  AssistiveTechnologyTraining kotlin  AssistiveTechnologyVideo kotlin  Boolean kotlin  	Exception kotlin  	Function1 kotlin  HashMap kotlin  IllegalStateException kotlin  Int kotlin  Intent kotlin  NoSuchFieldException kotlin  Nothing kotlin  Pair kotlin  QuizQuestionBank kotlin  R kotlin  String kotlin  Toast kotlin  Unit kotlin  android kotlin  apply kotlin  arrayOf kotlin  buildString kotlin  	hashMapOf kotlin  isEmpty kotlin  
isNotEmpty kotlin  let kotlin  listOf kotlin  to kotlin  trim kotlin  
getISEmpty 
kotlin.String  
getISNotEmpty 
kotlin.String  
getIsEmpty 
kotlin.String  
getIsNotEmpty 
kotlin.String  getTO 
kotlin.String  getTRIM 
kotlin.String  getTo 
kotlin.String  getTrim 
kotlin.String  isEmpty 
kotlin.String  
isNotEmpty 
kotlin.String  Answer kotlin.annotation  AssistiveTechnologyArticle kotlin.annotation  AssistiveTechnologyTraining kotlin.annotation  AssistiveTechnologyVideo kotlin.annotation  	Exception kotlin.annotation  HashMap kotlin.annotation  IllegalStateException kotlin.annotation  Intent kotlin.annotation  NoSuchFieldException kotlin.annotation  Pair kotlin.annotation  QuizQuestionBank kotlin.annotation  R kotlin.annotation  Toast kotlin.annotation  android kotlin.annotation  apply kotlin.annotation  arrayOf kotlin.annotation  buildString kotlin.annotation  	hashMapOf kotlin.annotation  isEmpty kotlin.annotation  
isNotEmpty kotlin.annotation  let kotlin.annotation  listOf kotlin.annotation  to kotlin.annotation  trim kotlin.annotation  Answer kotlin.collections  AssistiveTechnologyArticle kotlin.collections  AssistiveTechnologyTraining kotlin.collections  AssistiveTechnologyVideo kotlin.collections  	Exception kotlin.collections  HashMap kotlin.collections  IllegalStateException kotlin.collections  Intent kotlin.collections  List kotlin.collections  MutableList kotlin.collections  NoSuchFieldException kotlin.collections  Pair kotlin.collections  QuizQuestionBank kotlin.collections  R kotlin.collections  Toast kotlin.collections  android kotlin.collections  apply kotlin.collections  arrayOf kotlin.collections  buildString kotlin.collections  	hashMapOf kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  let kotlin.collections  listOf kotlin.collections  to kotlin.collections  trim kotlin.collections  getLET kotlin.collections.List  getLet kotlin.collections.List  Answer kotlin.comparisons  AssistiveTechnologyArticle kotlin.comparisons  AssistiveTechnologyTraining kotlin.comparisons  AssistiveTechnologyVideo kotlin.comparisons  	Exception kotlin.comparisons  HashMap kotlin.comparisons  IllegalStateException kotlin.comparisons  Intent kotlin.comparisons  NoSuchFieldException kotlin.comparisons  Pair kotlin.comparisons  QuizQuestionBank kotlin.comparisons  R kotlin.comparisons  Toast kotlin.comparisons  android kotlin.comparisons  apply kotlin.comparisons  arrayOf kotlin.comparisons  buildString kotlin.comparisons  	hashMapOf kotlin.comparisons  isEmpty kotlin.comparisons  
isNotEmpty kotlin.comparisons  let kotlin.comparisons  listOf kotlin.comparisons  to kotlin.comparisons  trim kotlin.comparisons  Answer 	kotlin.io  AssistiveTechnologyArticle 	kotlin.io  AssistiveTechnologyTraining 	kotlin.io  AssistiveTechnologyVideo 	kotlin.io  	Exception 	kotlin.io  HashMap 	kotlin.io  IllegalStateException 	kotlin.io  Intent 	kotlin.io  NoSuchFieldException 	kotlin.io  Pair 	kotlin.io  QuizQuestionBank 	kotlin.io  R 	kotlin.io  Toast 	kotlin.io  android 	kotlin.io  apply 	kotlin.io  arrayOf 	kotlin.io  buildString 	kotlin.io  	hashMapOf 	kotlin.io  isEmpty 	kotlin.io  
isNotEmpty 	kotlin.io  let 	kotlin.io  listOf 	kotlin.io  to 	kotlin.io  trim 	kotlin.io  Answer 
kotlin.jvm  AssistiveTechnologyArticle 
kotlin.jvm  AssistiveTechnologyTraining 
kotlin.jvm  AssistiveTechnologyVideo 
kotlin.jvm  	Exception 
kotlin.jvm  HashMap 
kotlin.jvm  IllegalStateException 
kotlin.jvm  Intent 
kotlin.jvm  NoSuchFieldException 
kotlin.jvm  Pair 
kotlin.jvm  QuizQuestionBank 
kotlin.jvm  R 
kotlin.jvm  Toast 
kotlin.jvm  android 
kotlin.jvm  apply 
kotlin.jvm  arrayOf 
kotlin.jvm  buildString 
kotlin.jvm  	hashMapOf 
kotlin.jvm  isEmpty 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  let 
kotlin.jvm  listOf 
kotlin.jvm  to 
kotlin.jvm  trim 
kotlin.jvm  Answer 
kotlin.ranges  AssistiveTechnologyArticle 
kotlin.ranges  AssistiveTechnologyTraining 
kotlin.ranges  AssistiveTechnologyVideo 
kotlin.ranges  	Exception 
kotlin.ranges  HashMap 
kotlin.ranges  IllegalStateException 
kotlin.ranges  Intent 
kotlin.ranges  NoSuchFieldException 
kotlin.ranges  Pair 
kotlin.ranges  QuizQuestionBank 
kotlin.ranges  R 
kotlin.ranges  Toast 
kotlin.ranges  android 
kotlin.ranges  apply 
kotlin.ranges  arrayOf 
kotlin.ranges  buildString 
kotlin.ranges  	hashMapOf 
kotlin.ranges  isEmpty 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  let 
kotlin.ranges  listOf 
kotlin.ranges  to 
kotlin.ranges  trim 
kotlin.ranges  Answer kotlin.sequences  AssistiveTechnologyArticle kotlin.sequences  AssistiveTechnologyTraining kotlin.sequences  AssistiveTechnologyVideo kotlin.sequences  	Exception kotlin.sequences  HashMap kotlin.sequences  IllegalStateException kotlin.sequences  Intent kotlin.sequences  NoSuchFieldException kotlin.sequences  Pair kotlin.sequences  QuizQuestionBank kotlin.sequences  R kotlin.sequences  Toast kotlin.sequences  android kotlin.sequences  apply kotlin.sequences  arrayOf kotlin.sequences  buildString kotlin.sequences  	hashMapOf kotlin.sequences  isEmpty kotlin.sequences  
isNotEmpty kotlin.sequences  let kotlin.sequences  listOf kotlin.sequences  to kotlin.sequences  trim kotlin.sequences  Answer kotlin.text  AssistiveTechnologyArticle kotlin.text  AssistiveTechnologyTraining kotlin.text  AssistiveTechnologyVideo kotlin.text  	Exception kotlin.text  HashMap kotlin.text  IllegalStateException kotlin.text  Intent kotlin.text  NoSuchFieldException kotlin.text  Pair kotlin.text  QuizQuestionBank kotlin.text  R kotlin.text  Toast kotlin.text  android kotlin.text  apply kotlin.text  arrayOf kotlin.text  buildString kotlin.text  	hashMapOf kotlin.text  isEmpty kotlin.text  
isNotEmpty kotlin.text  let kotlin.text  listOf kotlin.text  to kotlin.text  trim kotlin.text  R #uk.ac.bournemouthuniversity.at4send  id %uk.ac.bournemouthuniversity.at4send.R  layout %uk.ac.bournemouthuniversity.at4send.R  improvementsInput (uk.ac.bournemouthuniversity.at4send.R.id  
likesInput (uk.ac.bournemouthuniversity.at4send.R.id  submitButton (uk.ac.bournemouthuniversity.at4send.R.id  trainingFeedbackFragment (uk.ac.bournemouthuniversity.at4send.R.id  training_feedback_card (uk.ac.bournemouthuniversity.at4send.R.id  fragment_training ,uk.ac.bournemouthuniversity.at4send.R.layout  fragment_training_feedback ,uk.ac.bournemouthuniversity.at4send.R.layout  Answer -uk.ac.bournemouthuniversity.at4send.data.quiz  HashMap -uk.ac.bournemouthuniversity.at4send.data.quiz  IllegalStateException -uk.ac.bournemouthuniversity.at4send.data.quiz  List -uk.ac.bournemouthuniversity.at4send.data.quiz  NoSuchFieldException -uk.ac.bournemouthuniversity.at4send.data.quiz  Pair -uk.ac.bournemouthuniversity.at4send.data.quiz  
QuizDataStore -uk.ac.bournemouthuniversity.at4send.data.quiz  QuizQuestionBank -uk.ac.bournemouthuniversity.at4send.data.quiz  String -uk.ac.bournemouthuniversity.at4send.data.quiz  	hashMapOf -uk.ac.bournemouthuniversity.at4send.data.quiz  let -uk.ac.bournemouthuniversity.at4send.data.quiz  listOf -uk.ac.bournemouthuniversity.at4send.data.quiz  to -uk.ac.bournemouthuniversity.at4send.data.quiz  Answer ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  HashMap ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  IllegalStateException ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  KTSSQuiz ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  List ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  NoSuchFieldException ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  Pair ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  Question ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  QuizQuestionBank ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  String ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  aacDevicesQuiz ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  altKeyboardQuiz ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  altMiceQuiz ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  androidQuiz ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  curriculumQuiz ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  dyslexiaQuiz ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  ecsQuiz ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  etQuiz ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  getHASHMapOf ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  getHashMapOf ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  getLET ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  	getLISTOf ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  getLet ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  	getListOf ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  getTO ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  getTo ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  	hashMapOf ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  iOSQuiz ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  let ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  listOf ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  quizzes ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  screenReadersQuiz ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  switchesQuiz ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  
tabletQuiz ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  to ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  
visualQuiz ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  windowsQuiz ;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore  AssistiveTechnologyArticle 1uk.ac.bournemouthuniversity.at4send.data.training  AssistiveTechnologyTraining 1uk.ac.bournemouthuniversity.at4send.data.training  AssistiveTechnologyVideo 1uk.ac.bournemouthuniversity.at4send.data.training  	DataStore 1uk.ac.bournemouthuniversity.at4send.data.training  Pair 1uk.ac.bournemouthuniversity.at4send.data.training  listOf 1uk.ac.bournemouthuniversity.at4send.data.training  AssistiveTechnologyArticle ;uk.ac.bournemouthuniversity.at4send.data.training.DataStore  AssistiveTechnologyTraining ;uk.ac.bournemouthuniversity.at4send.data.training.DataStore  AssistiveTechnologyVideo ;uk.ac.bournemouthuniversity.at4send.data.training.DataStore  Pair ;uk.ac.bournemouthuniversity.at4send.data.training.DataStore  	getLISTOf ;uk.ac.bournemouthuniversity.at4send.data.training.DataStore  	getListOf ;uk.ac.bournemouthuniversity.at4send.data.training.DataStore  listOf ;uk.ac.bournemouthuniversity.at4send.data.training.DataStore  AssistiveTechnologyArticle 3uk.ac.bournemouthuniversity.at4send.models.training  AssistiveTechnologyTraining 3uk.ac.bournemouthuniversity.at4send.models.training  AssistiveTechnologyVideo 3uk.ac.bournemouthuniversity.at4send.models.training  Answer /uk.ac.bournemouthuniversity.at4send.quiz.models  Question /uk.ac.bournemouthuniversity.at4send.quiz.models  QuizQuestionBank /uk.ac.bournemouthuniversity.at4send.quiz.models  getQUIZQuestionBank @uk.ac.bournemouthuniversity.at4send.quiz.models.QuizQuestionBank  getQuizQuestionBank @uk.ac.bournemouthuniversity.at4send.quiz.models.QuizQuestionBank  quizQuestionBank @uk.ac.bournemouthuniversity.at4send.quiz.models.QuizQuestionBank  setQuizQuestionBank @uk.ac.bournemouthuniversity.at4send.quiz.models.QuizQuestionBank  createFeedbackEmail Huk.ac.bournemouthuniversity.at4send.email.templates.EmailTemplateManager  getReplyToEmail 8uk.ac.bournemouthuniversity.at4send.email.SendGridConfig  EmailRequest 0uk.ac.bournemouthuniversity.at4send.email.models  
EmailCallback >uk.ac.bournemouthuniversity.at4send.email.SendGridEmailService  EmailTemplateManager 3uk.ac.bournemouthuniversity.at4send.email.templates  shutdown >uk.ac.bournemouthuniversity.at4send.email.SendGridEmailService  SendGridEmailService )uk.ac.bournemouthuniversity.at4send.email  sendEmailAsync >uk.ac.bournemouthuniversity.at4send.email.SendGridEmailService  SendGridConfig )uk.ac.bournemouthuniversity.at4send.email  isConfigured 8uk.ac.bournemouthuniversity.at4send.email.SendGridConfig  
EmailResponse 0uk.ac.bournemouthuniversity.at4send.email.models  
runOnUiThread android.app.Activity  
runOnUiThread android.content.Context  
runOnUiThread android.content.ContextWrapper  
runOnUiThread  android.view.ContextThemeWrapper  LENGTH_LONG android.widget.Toast  
runOnUiThread #androidx.activity.ComponentActivity  
runOnUiThread #androidx.core.app.ComponentActivity  FragmentActivity androidx.fragment.app  
EmailResponse androidx.fragment.app.Fragment  EmailTemplateManager androidx.fragment.app.Fragment  SendGridConfig androidx.fragment.app.Fragment  SendGridEmailService androidx.fragment.app.Fragment  String androidx.fragment.app.Fragment  activity androidx.fragment.app.Fragment  clearInputs androidx.fragment.app.Fragment  context androidx.fragment.app.Fragment  emailService androidx.fragment.app.Fragment  initializeSendGridConfig androidx.fragment.app.Fragment  
isInitialized androidx.fragment.app.Fragment  	onDestroy androidx.fragment.app.Fragment  requireContext androidx.fragment.app.Fragment  resetSubmitButton androidx.fragment.app.Fragment  sendFeedbackViaIntent androidx.fragment.app.Fragment  sendFeedbackViaSendGrid androidx.fragment.app.Fragment  
runOnUiThread &androidx.fragment.app.FragmentActivity  EmailTemplateManager !com.eduability.at4send2.fragments  SendGridConfig !com.eduability.at4send2.fragments  SendGridEmailService !com.eduability.at4send2.fragments  String !com.eduability.at4send2.fragments  activity !com.eduability.at4send2.fragments  clearInputs !com.eduability.at4send2.fragments  context !com.eduability.at4send2.fragments  
isInitialized !com.eduability.at4send2.fragments  resetSubmitButton !com.eduability.at4send2.fragments  sendFeedbackViaIntent !com.eduability.at4send2.fragments  
EmailResponse :com.eduability.at4send2.fragments.TrainingFeedbackFragment  EmailTemplateManager :com.eduability.at4send2.fragments.TrainingFeedbackFragment  SendGridConfig :com.eduability.at4send2.fragments.TrainingFeedbackFragment  SendGridEmailService :com.eduability.at4send2.fragments.TrainingFeedbackFragment  String :com.eduability.at4send2.fragments.TrainingFeedbackFragment  activity :com.eduability.at4send2.fragments.TrainingFeedbackFragment  clearInputs :com.eduability.at4send2.fragments.TrainingFeedbackFragment  emailService :com.eduability.at4send2.fragments.TrainingFeedbackFragment  emailTemplateManager :com.eduability.at4send2.fragments.TrainingFeedbackFragment  getACTIVITY :com.eduability.at4send2.fragments.TrainingFeedbackFragment  getActivity :com.eduability.at4send2.fragments.TrainingFeedbackFragment  initializeSendGridConfig :com.eduability.at4send2.fragments.TrainingFeedbackFragment  
isInitialized :com.eduability.at4send2.fragments.TrainingFeedbackFragment  requireContext :com.eduability.at4send2.fragments.TrainingFeedbackFragment  resetSubmitButton :com.eduability.at4send2.fragments.TrainingFeedbackFragment  sendFeedbackViaIntent :com.eduability.at4send2.fragments.TrainingFeedbackFragment  sendFeedbackViaSendGrid :com.eduability.at4send2.fragments.TrainingFeedbackFragment  sendGridConfig :com.eduability.at4send2.fragments.TrainingFeedbackFragment  setActivity :com.eduability.at4send2.fragments.TrainingFeedbackFragment  getACTIVITY ecom.eduability.at4send2.fragments.TrainingFeedbackFragment.sendFeedbackViaSendGrid.<no name provided>  getActivity ecom.eduability.at4send2.fragments.TrainingFeedbackFragment.sendFeedbackViaSendGrid.<no name provided>  getCLEARInputs ecom.eduability.at4send2.fragments.TrainingFeedbackFragment.sendFeedbackViaSendGrid.<no name provided>  
getCONTEXT ecom.eduability.at4send2.fragments.TrainingFeedbackFragment.sendFeedbackViaSendGrid.<no name provided>  getClearInputs ecom.eduability.at4send2.fragments.TrainingFeedbackFragment.sendFeedbackViaSendGrid.<no name provided>  
getContext ecom.eduability.at4send2.fragments.TrainingFeedbackFragment.sendFeedbackViaSendGrid.<no name provided>  getRESETSubmitButton ecom.eduability.at4send2.fragments.TrainingFeedbackFragment.sendFeedbackViaSendGrid.<no name provided>  getResetSubmitButton ecom.eduability.at4send2.fragments.TrainingFeedbackFragment.sendFeedbackViaSendGrid.<no name provided>  getSENDFeedbackViaIntent ecom.eduability.at4send2.fragments.TrainingFeedbackFragment.sendFeedbackViaSendGrid.<no name provided>  getSendFeedbackViaIntent ecom.eduability.at4send2.fragments.TrainingFeedbackFragment.sendFeedbackViaSendGrid.<no name provided>  getISEnabled 1com.google.android.material.button.MaterialButton  getIsEnabled 1com.google.android.material.button.MaterialButton  getTEXT 1com.google.android.material.button.MaterialButton  getText 1com.google.android.material.button.MaterialButton  	isEnabled 1com.google.android.material.button.MaterialButton  
setEnabled 1com.google.android.material.button.MaterialButton  setText 1com.google.android.material.button.MaterialButton  text 1com.google.android.material.button.MaterialButton  EmailTemplateManager 	java.lang  	Exception 	java.lang  SendGridConfig 	java.lang  SendGridEmailService 	java.lang  activity 	java.lang  clearInputs 	java.lang  context 	java.lang  
isInitialized 	java.lang  resetSubmitButton 	java.lang  sendFeedbackViaIntent 	java.lang  message java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  CharSequence kotlin  EmailTemplateManager kotlin  	Function0 kotlin  SendGridConfig kotlin  SendGridEmailService kotlin  activity kotlin  clearInputs kotlin  context kotlin  
isInitialized kotlin  resetSubmitButton kotlin  sendFeedbackViaIntent kotlin  EmailTemplateManager kotlin.annotation  SendGridConfig kotlin.annotation  SendGridEmailService kotlin.annotation  activity kotlin.annotation  clearInputs kotlin.annotation  context kotlin.annotation  
isInitialized kotlin.annotation  resetSubmitButton kotlin.annotation  sendFeedbackViaIntent kotlin.annotation  EmailTemplateManager kotlin.collections  SendGridConfig kotlin.collections  SendGridEmailService kotlin.collections  activity kotlin.collections  clearInputs kotlin.collections  context kotlin.collections  
isInitialized kotlin.collections  resetSubmitButton kotlin.collections  sendFeedbackViaIntent kotlin.collections  EmailTemplateManager kotlin.comparisons  SendGridConfig kotlin.comparisons  SendGridEmailService kotlin.comparisons  activity kotlin.comparisons  clearInputs kotlin.comparisons  context kotlin.comparisons  
isInitialized kotlin.comparisons  resetSubmitButton kotlin.comparisons  sendFeedbackViaIntent kotlin.comparisons  EmailTemplateManager 	kotlin.io  SendGridConfig 	kotlin.io  SendGridEmailService 	kotlin.io  activity 	kotlin.io  clearInputs 	kotlin.io  context 	kotlin.io  
isInitialized 	kotlin.io  resetSubmitButton 	kotlin.io  sendFeedbackViaIntent 	kotlin.io  EmailTemplateManager 
kotlin.jvm  SendGridConfig 
kotlin.jvm  SendGridEmailService 
kotlin.jvm  activity 
kotlin.jvm  clearInputs 
kotlin.jvm  context 
kotlin.jvm  
isInitialized 
kotlin.jvm  resetSubmitButton 
kotlin.jvm  sendFeedbackViaIntent 
kotlin.jvm  EmailTemplateManager 
kotlin.ranges  SendGridConfig 
kotlin.ranges  SendGridEmailService 
kotlin.ranges  activity 
kotlin.ranges  clearInputs 
kotlin.ranges  context 
kotlin.ranges  
isInitialized 
kotlin.ranges  resetSubmitButton 
kotlin.ranges  sendFeedbackViaIntent 
kotlin.ranges  KMutableProperty0 kotlin.reflect  getISInitialized  kotlin.reflect.KMutableProperty0  getIsInitialized  kotlin.reflect.KMutableProperty0  
isInitialized  kotlin.reflect.KMutableProperty0  EmailTemplateManager kotlin.sequences  SendGridConfig kotlin.sequences  SendGridEmailService kotlin.sequences  activity kotlin.sequences  clearInputs kotlin.sequences  context kotlin.sequences  
isInitialized kotlin.sequences  resetSubmitButton kotlin.sequences  sendFeedbackViaIntent kotlin.sequences  EmailTemplateManager kotlin.text  SendGridConfig kotlin.text  SendGridEmailService kotlin.text  activity kotlin.text  clearInputs kotlin.text  context kotlin.text  
isInitialized kotlin.text  resetSubmitButton kotlin.text  sendFeedbackViaIntent kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            