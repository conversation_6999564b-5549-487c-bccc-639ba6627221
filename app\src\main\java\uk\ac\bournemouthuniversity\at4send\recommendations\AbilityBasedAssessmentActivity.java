package uk.ac.bournemouthuniversity.at4send.recommendations;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import uk.ac.bournemouthuniversity.at4send.BaseActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import androidx.core.app.ActivityOptionsCompat;
import androidx.core.app.ActivityCompat;

import androidx.activity.OnBackPressedCallback;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.TextView;

import com.afollestad.materialdialogs.MaterialDialog;
import com.google.android.material.snackbar.Snackbar;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.adapters.AbilitiesAdapter;
import uk.ac.bournemouthuniversity.at4send.api.AT4SENDAPIClient;
import uk.ac.bournemouthuniversity.at4send.api.AT4SENDAPIInterface;
import uk.ac.bournemouthuniversity.at4send.extensions.helpers.SnackbarHelper;
import uk.ac.bournemouthuniversity.at4send.extensions.ui.Dialogs;
import uk.ac.bournemouthuniversity.at4send.extensions.utils.ArrayListAnySize;
import uk.ac.bournemouthuniversity.at4send.extensions.utils.Network;
import uk.ac.bournemouthuniversity.at4send.extensions.views.indicatorseekbar.IndicatorSeekBar;
import uk.ac.bournemouthuniversity.at4send.utils.ReadAloudUtils;
import uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils;
import uk.ac.bournemouthuniversity.at4send.utils.ContrastUtils;
import uk.ac.bournemouthuniversity.at4send.models.Ability;
import uk.ac.bournemouthuniversity.at4send.models.AbilityGroup;
import uk.ac.bournemouthuniversity.at4send.models.response.AbilitiesGroupResponse;
import uk.ac.bournemouthuniversity.at4send.technologies.AssistiveTechnologyListActivity;

public class AbilityBasedAssessmentActivity extends BaseActivity {
    private static final String TAG = "AbilityAssessment";

    private List<Ability> abilityList = new ArrayListAnySize<>();
    private AbilitiesAdapter mAdapter;

    private CoordinatorLayout mMainView;

    private FirebaseCrashlytics crashlytics = FirebaseCrashlytics.getInstance();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        getOnBackPressedDispatcher().addCallback(this, new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                finish();
                overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_right);
            }
        });
        
        setContentView(R.layout.activity_ability_based_assessment);

        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        ActionBar ab = getSupportActionBar();
        if (ab != null) {
            ab.setDisplayHomeAsUpEnabled(true);
        }

        mAdapter = new AbilitiesAdapter(this, abilityList);

        RecyclerView mainRecyclerView = findViewById(R.id.mainRecyclerView);
        mainRecyclerView.setAdapter(mAdapter);
        mainRecyclerView.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));

        mMainView = findViewById(R.id.mainView);

        if(!Network.isNetworkAvailable(this)){
            new MaterialDialog(AbilityBasedAssessmentActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                    .title(null, "Network Access Required")
                    .message(null, "An active network connection is required for this function. Please connect to the internet and try again.", null)
                    .cancelable(false)
                    .positiveButton(null, "Dismiss", materialDialog -> {
                        materialDialog.dismiss();
                        finish();
                        return null;
                    }).show();

            return;
        }

        retrieveAbilitiesList();
        
        // Apply high contrast if enabled
        if (ContrastUtils.isHighContrastEnabled(this)) {
            applyHighContrastToUI();
        }

        // Add read-aloud buttons to instructional text
        TextView instructionsText = findViewById(R.id.assessment_instructions);
        if (instructionsText != null) {
            ReadAloudUtils.addReadAloudButton(this, instructionsText);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        
        // Reapply high contrast when returning to activity
        if (ContrastUtils.isHighContrastEnabled(this)) {
            applyHighContrastToUI();
        }
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        int itemId = item.getItemId();

        if(itemId == android.R.id.home){
            onBackPressed();
            return true;
        }

        return super.onOptionsItemSelected(item);
    }

    @Override
    public void finish() {
        super.finish();
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_right);
    }

    private void retrieveAbilitiesList(){
        MaterialDialog progressDialog = Dialogs.generateProgressDialog(this, "Loading...");
        progressDialog.show();

        AT4SENDAPIInterface apiService =
                AT4SENDAPIClient.getClient().create(AT4SENDAPIInterface.class);

        Call<AbilitiesGroupResponse> call = apiService.getAbilitiesWithGroups(true);
        call.enqueue(new Callback<AbilitiesGroupResponse>() {
            @Override
            public void onResponse(@NotNull Call<AbilitiesGroupResponse>call, @NotNull Response<AbilitiesGroupResponse> response) {
                if(response.body() != null) {
                    List<AbilityGroup> abilityGroups = response.body().getAbilityGroups();

                    if (abilityGroups != null) {
                        Log.d(TAG, "Number of ability groups received: " + abilityGroups.size());
                        populateAbilitiesList(abilityGroups);
                    }
                }else{
                    new MaterialDialog(AbilityBasedAssessmentActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                            .title(null, "Error Occurred")
                            .message(null, "An error occurred whilst retrieving the ability list. Please try again later.", null)
                            .cancelable(false)
                            .positiveButton(null, "Dismiss", materialDialog -> {
                                materialDialog.dismiss();
                                finish();
                                return null;
                            }).show();
                }

                progressDialog.dismiss();
            }

            @Override
            public void onFailure(@NotNull Call<AbilitiesGroupResponse>call, @NotNull Throwable t) {
                // Log error here since request failed
                Log.e(TAG, t.toString());
                crashlytics.recordException(t);

                progressDialog.dismiss();

                new MaterialDialog(AbilityBasedAssessmentActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                        .title(null, "Error Occurred")
                        .message(null, "An error occurred whilst retrieving the ability list. Please try again later.", null)
                        .cancelable(false)
                        .positiveButton(null, "Dismiss", materialDialog -> {
                            materialDialog.dismiss();
                            finish();
                            return null;
                        }).show();
            }
        });
    }

    private void populateAbilitiesList(List<AbilityGroup> abilityGroups){
        abilityList.clear();
        mAdapter.notifyDataSetChanged();

        for(AbilityGroup group : abilityGroups){
            abilityList.add(new Ability(group.getGroupName(), Ability.VIEW_TYPE_HEAD));
            abilityList.addAll(group.getGroupAbilities());
        }

        mAdapter.notifyDataSetChanged();
    }

    public void submitAssessment(View view){
        boolean validated = true;

        Intent i = new Intent(this, AssistiveTechnologyListActivity.class);
        ArrayList<Integer> ids = new ArrayList<>();
        ArrayList<Integer> ratings = new ArrayList<>();

        for(Ability ability : abilityList){
            if(ability.getAbilityID() != Ability.NO_ID) {
                if(ability.getAbilityRating() == Ability.NO_RATING){
                    validated = false;
                    break;
                }
                ids.add(ability.getAbilityID());
                ratings.add(ability.getAbilityRating());
            }
        }

        if(validated) {
            i.putIntegerArrayListExtra(AssistiveTechnologyListActivity.EXTRA_ABILITY_IDS, ids);
            i.putIntegerArrayListExtra(AssistiveTechnologyListActivity.EXTRA_ABILITY_RATINGS, ratings);
            startActivity(i);
        }else{
            Snackbar sB  = Snackbar.make(mMainView, "Please complete all ability ratings before proceeding.", Snackbar.LENGTH_LONG);
            SnackbarHelper.configSnackbar(this, sB);
            sB.show();
        }
    }

    public void returnHome(View view) {
        finish();
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_right);
    }

    /**
     * Applies high contrast settings to the UI elements
     */
    private void applyHighContrastToUI() {
        // Apply contrast to the main view hierarchy
        ContrastUtils.applyContrastToViewHierarchy(findViewById(android.R.id.content));
        
        // Apply contrast to toolbar
        Toolbar toolbar = findViewById(R.id.toolbar);
        if (toolbar != null) {
            toolbar.setTitleTextColor(TextSizeUtils.getTextColor(this).getDefaultColor());
            if (toolbar.getNavigationIcon() != null) {
                toolbar.getNavigationIcon().setColorFilter(
                    TextSizeUtils.getTextColor(this).getDefaultColor(),
                    android.graphics.PorterDuff.Mode.SRC_IN
                );
            }
        }
        
        // Apply contrast to RecyclerView items
        RecyclerView recyclerView = findViewById(R.id.mainRecyclerView);
        if (recyclerView != null) {
            // Apply to visible items
            for (int i = 0; i < recyclerView.getChildCount(); i++) {
                View itemView = recyclerView.getChildAt(i);
                if (itemView != null) {
                    ContrastUtils.applyContrastToViewHierarchy(itemView);
                    
                    // Find and apply contrast to any help icons (?) in the item
                    applyContrastToHelpIcons(itemView);
                    
                    // Find and apply contrast to any IndicatorSeekBar in the item
                    IndicatorSeekBar seekBar = itemView.findViewById(R.id.rating_seek_bar);
                    if (seekBar != null) {
                        // Apply high contrast to the tick texts
                        seekBar.tickTextsColor(TextSizeUtils.getTextColor(this).getDefaultColor());
                    }
                }
            }
            
            // Force adapter to refresh all items
            if (mAdapter != null) {
                mAdapter.notifyDataSetChanged();
            }
        }
        
        // Find all buttons in the layout and apply contrast
        ViewGroup rootView = (ViewGroup) findViewById(android.R.id.content);
        findAndApplyContrastToButtons(rootView);
        
        // Find and apply contrast to all help icons (?) in the activity
        applyContrastToHelpIcons(findViewById(android.R.id.content));

        // Apply contrast to read-aloud buttons
        ViewGroup contentView = findViewById(android.R.id.content);
        if (contentView != null) {
            for (int i = 0; i < contentView.getChildCount(); i++) {
                View child = contentView.getChildAt(i);
                if (child instanceof ViewGroup) {
                    View readAloudButton = child.findViewById(R.id.read_aloud_button);
                    if (readAloudButton instanceof ImageButton) {
                        ((ImageButton) readAloudButton).setColorFilter(
                            TextSizeUtils.getTextColor(this).getDefaultColor(),
                            android.graphics.PorterDuff.Mode.SRC_IN
                        );
                    }
                }
            }
        }
    }

    /**
     * Recursively searches for and applies contrast to buttons
     */
    private void findAndApplyContrastToButtons(ViewGroup viewGroup) {
        if (viewGroup == null) return;
        
        for (int i = 0; i < viewGroup.getChildCount(); i++) {
            View child = viewGroup.getChildAt(i);
            
            if (child instanceof Button || 
                child instanceof com.google.android.material.button.MaterialButton) {
                // Apply contrast to the button
                if (child instanceof Button) {
                    ((Button) child).setTextColor(TextSizeUtils.getTextColor(this).getDefaultColor());
                } else if (child instanceof com.google.android.material.button.MaterialButton) {
                    ((com.google.android.material.button.MaterialButton) child).setTextColor(
                        TextSizeUtils.getTextColor(this).getDefaultColor()
                    );
                }
                
                child.setBackgroundTintList(TextSizeUtils.getBackgroundColor(this));
            } else if (child instanceof ViewGroup) {
                findAndApplyContrastToButtons((ViewGroup) child);
            }
        }
    }

    /**
     * Recursively searches for and applies contrast to help icons (?)
     */
    private void applyContrastToHelpIcons(View view) {
        if (view == null) return;
        
        // Apply contrast to ImageView elements that might be help icons
        if (view instanceof ImageView) {
            ImageView imageView = (ImageView) view;
            
            // Check if this is likely a help icon by its ID or tag
            boolean isHelpIcon = false;
            
            // Check by ID
            if (view.getId() != View.NO_ID) {
                try {
                    String resourceName = getResources().getResourceEntryName(view.getId());
                    if (resourceName != null && 
                        (resourceName.contains("help") || 
                         resourceName.contains("info") || 
                         resourceName.contains("question"))) {
                        isHelpIcon = true;
                    }
                } catch (Exception e) {
                    // Resource name not found, continue with other checks
                }
            }
            
            // Check by tag
            if (!isHelpIcon && view.getTag() != null) {
                String tag = view.getTag().toString();
                if (tag.contains("help") || tag.contains("info") || tag.contains("question")) {
                    isHelpIcon = true;
                }
            }
            
            // Apply contrast if it's a help icon or apply to all ImageViews as fallback
            imageView.setColorFilter(
                TextSizeUtils.getTextColor(this).getDefaultColor(),
                android.graphics.PorterDuff.Mode.SRC_IN
            );
        }
        
        // Recursively apply to child views
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            for (int i = 0; i < viewGroup.getChildCount(); i++) {
                applyContrastToHelpIcons(viewGroup.getChildAt(i));
            }
        }
    }
}

