package uk.ac.bournemouthuniversity.at4send;

import android.os.Bundle;
import android.view.View;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import uk.ac.bournemouthuniversity.at4send.utils.ContrastUtils;
import uk.ac.bournemouthuniversity.at4send.utils.ButtonSizeUtils;

public class BaseFragment extends Fragment {
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        // Apply button size to all views
        ButtonSizeUtils.applyButtonSizeToView(view);
        
        if (ContrastUtils.isHighContrastEnabled(requireContext())) {
            // Apply contrast settings after content view is set
            ContrastUtils.applyContrastToViewHierarchy(view);
        }
    }
}
