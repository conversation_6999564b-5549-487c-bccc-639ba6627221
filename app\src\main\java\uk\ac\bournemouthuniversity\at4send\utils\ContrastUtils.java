package uk.ac.bournemouthuniversity.at4send.utils;

import static uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils.getBackgroundColor;
import static uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils.getTextColor;

import android.app.Dialog;
import android.content.res.ColorStateList;
import android.graphics.drawable.ColorDrawable;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Button;
import android.widget.EditText;
import android.content.Context;
import android.content.SharedPreferences;
import androidx.preference.PreferenceManager;
import androidx.recyclerview.widget.RecyclerView;

import com.afollestad.materialdialogs.MaterialDialog;

import java.lang.reflect.Field;

public class ContrastUtils {

    private static final String HIGH_CONTRAST_KEY = "high_contrast";

    public static void applyContrastToViewHierarchy(View root) {
        if (root == null) return;
        
        Context context = root.getContext();
        
        // Apply contrast to the root view - set both background color and tint
        root.setBackgroundColor(getBackgroundColor(context).getDefaultColor());
        root.setBackgroundTintList(getBackgroundColor(context));
        
        // Handle specific view types
        if (root instanceof TextView) {
            ((TextView) root).setTextColor(getTextColor(context).getDefaultColor());
        }
        if (root instanceof Button) {
            Button button = (Button) root;
            button.setTextColor(getTextColor(context).getDefaultColor());
            button.setBackgroundColor(getBackgroundColor(context).getDefaultColor());
            button.setBackgroundTintList(getBackgroundColor(context));
        }
        if (root instanceof EditText) {
            EditText editText = (EditText) root;
            editText.setTextColor(getTextColor(context).getDefaultColor());
            editText.setHintTextColor(getTextColor(context).withAlpha(128).getDefaultColor());
            editText.setBackgroundColor(getBackgroundColor(context).getDefaultColor());
        }
        
        // Handle ListView items specifically
        if (root instanceof ListView) {
            ListView listView = (ListView) root;
            listView.setBackgroundColor(getBackgroundColor(context).getDefaultColor());
            
            // Apply to visible children
            for (int i = 0; i < listView.getChildCount(); i++) {
                View child = listView.getChildAt(i);
                child.setBackgroundColor(getBackgroundColor(context).getDefaultColor());
                applyContrastToViewHierarchy(child);
            }
        }
        
        // Recursively apply to child views
        if (root instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) root;
            for (int i = 0; i < viewGroup.getChildCount(); i++) {
                applyContrastToViewHierarchy(viewGroup.getChildAt(i));
            }
        }
    }

    public static boolean isHighContrastEnabled(Context context) {
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(context);
        return prefs.getBoolean(HIGH_CONTRAST_KEY, false);
    }

    /**
     * Apply high contrast settings to a dialog
     * @param dialog The dialog to apply contrast to
     */
    public static void applyContrastToDialog(Dialog dialog) {
        if (dialog == null) return;
        
        Context context = dialog.getContext();
        
        // Apply to dialog window
        Window window = dialog.getWindow();
        if (window != null) {
            // Set background color directly
            window.setBackgroundDrawable(new ColorDrawable(getBackgroundColor(context).getDefaultColor()));
            
            // Apply to all views in the dialog
            View decorView = window.getDecorView();
            
            // Set the root view background color
            decorView.setBackgroundColor(getBackgroundColor(context).getDefaultColor());
            
            // Apply to all child views
            applyContrastToViewHierarchy(decorView);
        }
    }

    /**
     * Apply high contrast settings to a MaterialDialog
     * @param dialog The MaterialDialog to apply contrast to
     */
    public static void applyContrastToDialog(MaterialDialog dialog) {
        if (dialog == null) return;
        
        // Get the dialog's view
        View dialogView = dialog.getView();
        if (dialogView != null) {
            // Apply contrast to the dialog view and its children
            applyContrastToViewHierarchy(dialogView);
            
            // Find and apply contrast to title and content views
            // Note: We're using view traversal instead of direct resource IDs
            // since the exact resource IDs may vary between MaterialDialog versions
            if (dialogView instanceof ViewGroup) {
                ViewGroup viewGroup = (ViewGroup) dialogView;
                for (int i = 0; i < viewGroup.getChildCount(); i++) {
                    View child = viewGroup.getChildAt(i);
                    
                    // Apply contrast to all TextView elements
                    if (child instanceof TextView) {
                        ((TextView) child).setTextColor(getTextColor(dialog.getContext()).getDefaultColor());
                    }
                    
                    // Apply contrast to all Button elements
                    if (child instanceof Button) {
                        Button button = (Button) child;
                        button.setTextColor(getTextColor(dialog.getContext()).getDefaultColor());
                        button.setBackgroundTintList(getBackgroundColor(dialog.getContext()));
                    }
                    
                    // Recursively apply to child ViewGroups
                    if (child instanceof ViewGroup) {
                        applyContrastToViewHierarchy(child);
                    }
                }
            }
            
            // Apply to RecyclerView if present (for list dialogs)
            RecyclerView recyclerView = findRecyclerViewInViewGroup(dialogView);
            if (recyclerView != null) {
                for (int i = 0; i < recyclerView.getChildCount(); i++) {
                    View child = recyclerView.getChildAt(i);
                    applyContrastToViewHierarchy(child);
                }
            }
        }
    }

    /**
     * Helper method to find a RecyclerView in a ViewGroup hierarchy
     */
    private static RecyclerView findRecyclerViewInViewGroup(View view) {
        if (view instanceof RecyclerView) {
            return (RecyclerView) view;
        }
        
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            for (int i = 0; i < viewGroup.getChildCount(); i++) {
                RecyclerView found = findRecyclerViewInViewGroup(viewGroup.getChildAt(i));
                if (found != null) {
                    return found;
                }
            }
        }
        
        return null;
    }
}
