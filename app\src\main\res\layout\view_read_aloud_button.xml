<?xml version="1.0" encoding="utf-8"?>
<ImageButton xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/read_aloud_button"
    android:layout_width="48dp"
    android:layout_height="48dp"
    android:layout_gravity="center_vertical"
    android:background="?attr/selectableItemBackgroundBorderless"
    android:contentDescription="@string/read_aloud_button_description"
    android:padding="8dp"
    android:visibility="visible"
    android:elevation="4dp"
    android:tint="#000000"
    android:importantForAccessibility="yes"
    app:tint="#000000"
    android:src="@drawable/ic_volume_up" />