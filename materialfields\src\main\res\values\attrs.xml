<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- fields -->
    <declare-styleable name="Field">
        <attr name="title" format="string" />
        <attr name="value" format="string" />
        <attr name="placeholder" format="string" />
        <attr name="disabled" format="boolean" />
    </declare-styleable>

    <declare-styleable name="FieldGeneric">
        <attr name="drawable" format="reference" />
    </declare-styleable>

    <declare-styleable name="FieldInputText">
        <attr name="required" format="boolean" />
    </declare-styleable>

    <declare-styleable name="FieldCheckBox">
        <attr name="checked" format="boolean" />
    </declare-styleable>

    <declare-styleable name="FieldDateRange">
        <attr name="defaultStartDateUnixTimestamp" format="float" />
        <attr name="defaultEndDateUnixTimestamp" format="float" />
        <attr name="dateLimitRangeStartUnixTimestamp" format="float" />
        <attr name="dateLimitRangeEndUnixTimestamp" format="float" />
        <attr name="checked" />
    </declare-styleable>

    <declare-styleable name="FieldAction">
        <attr name="title" />
        <attr name="drawable" />
        <attr name="disabled" />
        <attr name="drawableTint" format="color" />
    </declare-styleable>

    <!-- Texts -->
    <declare-styleable name="Text">
        <attr name="text" format="reference|string" />
        <attr name="textAlignment" format="string" />
    </declare-styleable>

    <!-- Extra -->
    <declare-styleable name="ShoppingList">
        <attr name="addItemPlaceHolder" format="string" required="true" />
        <attr name="disabledAdd" format="boolean" />
        <attr name="disabledEntries" format="boolean" />
    </declare-styleable>

    <declare-styleable name="List">
        <attr name="addItemPlaceHolder" required="true" />
        <attr name="disabledAdd" />
        <attr name="disabledEntries" />
    </declare-styleable>

</resources>