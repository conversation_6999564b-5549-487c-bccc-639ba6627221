package com.gabrielepmattia.materialfields.extra

import android.content.Context
import android.content.DialogInterface
import android.content.res.TypedArray
import android.graphics.PorterDuff
import android.graphics.Typeface
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.gabrielepmattia.materialfields.R
import com.gabrielepmattia.materialfields.utils.Dialogs

/**
 * @Project aj-android
 * <AUTHOR>
 * @Date 07/03/2018 15:54
 */

class List : LinearLayout {

    private var mRecyclerView: RecyclerView? = null
    private var mAddItemPlaceHolder: String? = null
    private var mRecyclerViewAdapter: ListItemRecyclerAdapter? = null
    private var mRecyclerViewLayoutManager: LinearLayoutManager? = null

    /*
     * Public vars
     */

    /**
     * Disabled state of the add entry of the List
     */
    var disabledAdd: Boolean = false

    /**
     * Disabled state of the entries of the list
     */
    var disabledEntries: Boolean = false

    /**
     * Programmatically set the display items of the list
     */
    var items: ArrayList<Pair<String, String>>
        set(s) {
            mRecyclerViewAdapter!!.items = s
            onItemChangeListener?.onChange(mRecyclerView!!)
        }
        get() {
            return mRecyclerViewAdapter!!.items
        }

    /**
     * Action to be performed when clicking an entry of the list
     */
    var addAction: OnClickListener? = null
        set(o) {
            mRecyclerViewAdapter!!.addAction = o
            field = o
        }

    /**
     * Action to be performed when clicking an entry of the list
     */
    var editAction: OnItemEditClickListener? = null
        set(l) {
            if (l == null) return
            field = l
        }

    var onItemChangeListener: OnItemChangeListener? = null
        set(l) {
            if (l == null) return
            field = l
        }

    var isEditable: Boolean = true
        set(l) {
            field = l
            mRecyclerViewAdapter!!.isEditable = l
        }

    /*
     * Constructor
     */
    constructor(context: Context) : super(context) {
        initView(context)
    }

    constructor(context: Context, attrs: AttributeSet) : super(context, attrs) {
        initView(context)
        initAttrs(attrs)
    }

    constructor(context: Context, attrs: AttributeSet, defAttr: Int) : super(context, attrs, defAttr) {
        initView(context)
        initAttrs(attrs)
    }

    constructor(context: Context, attrs: AttributeSet, defAttr: Int, defRes: Int) : super(context, attrs, defAttr, defRes) {
        initView(context)
        initAttrs(attrs)
    }

    /*
     * Helpers
     */

    private fun initView(context: Context) {
        val i: LayoutInflater = context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        i.inflate(R.layout.component_field_list, this, true)
    }

    private fun initAttrs(attrs: AttributeSet) {
        mRecyclerView = findViewById(R.id.field_list_recycler)
        mRecyclerViewAdapter = ListItemRecyclerAdapter()
        mRecyclerViewLayoutManager = LinearLayoutManager(context)

        val t: TypedArray = context.obtainStyledAttributes(attrs, R.styleable.List) as TypedArray
        mAddItemPlaceHolder = t.getString(R.styleable.List_addItemPlaceHolder)
        disabledAdd = t.getBoolean(R.styleable.List_disabledAdd, false)
        disabledEntries = t.getBoolean(R.styleable.List_disabledEntries, false)
        t.recycle()
    }

    override fun onFinishInflate() {
        super.onFinishInflate()

        mRecyclerView!!.setHasFixedSize(true)
        mRecyclerView!!.layoutManager = mRecyclerViewLayoutManager
        mRecyclerView!!.adapter = mRecyclerViewAdapter
    }

    fun addItem(item: Pair<String, String>) {
        mRecyclerViewAdapter?.items!!.add(item)
        onItemChangeListener?.onChange(mRecyclerView!!)
        mRecyclerViewAdapter?.notifyDataSetChanged()
    }

    inner class ListItemRecyclerAdapter() : RecyclerView.Adapter<ListItemRecyclerAdapter.ViewHolder>() {

        var isEditable: Boolean = true

        var items: ArrayList<Pair<String, String>> = ArrayList()
            set(s) {
                field = s
                onItemChangeListener?.onChange(mRecyclerView!!)
                notifyDataSetChanged()
            }

        /**
         * The action performed when clicking on the add item of the list
         */
        var addAction: OnClickListener? = null
            set(o) {
                if (o == field) return
                field = o
                // check if the view is ready otherwise the listener will be set onBindView
                val tempAddEntryHolder = mRecyclerView!!
                        .findViewHolderForAdapterPosition(itemCount - 1) ?: return
                val addEntryHolder = tempAddEntryHolder as ViewHolder
                addEntryHolder.itemView.setOnClickListener(o)
            }

        constructor(items: ArrayList<Pair<String, String>>) : this() {
            this.items = items
        }

        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            var mIcon: ImageView = itemView.findViewById(R.id.field_list_item_image)
            var mContent: TextView = itemView.findViewById(R.id.field_list_item_content)
            var mSubcontent: TextView = itemView.findViewById(R.id.field_list_item_subcontent)
            var mContainer: ConstraintLayout = itemView.findViewById(R.id.field_list_item_container)
            var mBottomLineSeparator: View = itemView.findViewById(R.id.field_list_item_bottom_line_separator)
            var mItemView: LinearLayout = itemView.findViewById(R.id.itemViewI);

            fun setAsAddElement() {
                mContent.text = mAddItemPlaceHolder
                mSubcontent.visibility = GONE
                mIcon.setImageDrawable(context.getDrawable(R.drawable.pencil))
                mIcon.setOnClickListener(null)

                mContent.setTypeface(null, Typeface.ITALIC)
                mContent.setTextColor(ContextCompat.getColor(itemView.context, R.color.grey600))
                mContainer.setBackgroundColor(ContextCompat.getColor(context, R.color.white))

                if(!isEditable) {
                    mItemView.visibility = GONE
                }else{
                    mItemView.visibility = VISIBLE
                }
            }

            fun setAsNormalElement() {
                if(items[adapterPosition].first.isEmpty()) {
                    mContent.text = items[adapterPosition].second
                    mSubcontent.visibility = GONE
                }else{
                    mContent.text = items[adapterPosition].first
                    if(items[adapterPosition].second.isEmpty()){
                        mSubcontent.visibility = GONE
                    }else {
                        mSubcontent.text = items[adapterPosition].second
                        mSubcontent.visibility = VISIBLE
                    }
                }

                if(isEditable) {
                    mIcon.setImageDrawable(context.getDrawable(R.drawable.delete))
                    mIcon.setColorFilter(ContextCompat.getColor(context, R.color.grey700), PorterDuff.Mode.SRC_IN)
                    mIcon.setOnClickListener { _: View ->
                        if (disabledEntries) return@setOnClickListener
                        Dialogs.showDialogWithPNButton(
                                itemView.context,
                                itemView.context.getString(R.string.dialog_delete_header),
                                itemView.context.getString(R.string.dialog_delete_description, mContent.text),
                                itemView.context.getString(R.string.dialog_action_ok),
                                itemView.context.getString(R.string.dialog_action_cancel),
                                DeleteItemOKAction(adapterPosition),
                                AddItemCancelAction()
                        )
                    }
                }else{
                    mIcon.visibility = GONE
                }

                mItemView.visibility = VISIBLE

                mContent.setTypeface(null, Typeface.NORMAL)
                mContent.setTextColor(ContextCompat.getColor(itemView.context, R.color.black))
                mContainer.setBackgroundColor(ContextCompat.getColor(context, R.color.white))
            }

            fun setAsDisabledElement() {
                mContainer.setBackgroundColor(ContextCompat.getColor(context, R.color.grey300))
                mContent.setTextColor(ContextCompat.getColor(itemView.context, R.color.grey500))
                mIcon.setColorFilter(ContextCompat.getColor(context, R.color.grey500), PorterDuff.Mode.SRC_IN)
                mBottomLineSeparator.setBackgroundColor(ContextCompat.getColor(context, R.color.grey400))
            }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val itemView = LayoutInflater.from(parent.context)
                    .inflate(R.layout.component_field_list_item, parent, false)
            return ViewHolder(itemView)
        }

        override fun getItemCount(): Int {
            return items.size + 1
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            if (holder.adapterPosition == itemCount - 1) {
                holder.setAsAddElement()
                if (disabledAdd) {
                    holder.setAsDisabledElement()
                } else {
                    holder.itemView.setOnClickListener {
                        addAction!!.onClick(it)
                    }
                }
            } else {
                holder.setAsNormalElement()
                if (disabledEntries) holder.setAsDisabledElement()

                holder.itemView.setOnClickListener {
                    editAction?.onEditClicked(mRecyclerView!!, this, position)
                }
            }
        }

        inner class AddItemCancelAction() : DialogInterface.OnClickListener {
            override fun onClick(p0: DialogInterface?, p1: Int) {
                p0!!.cancel()
            }
        }

        inner class DeleteItemOKAction(position: Int) : DialogInterface.OnClickListener {

            private var mPosition: Int = 0

            init {
                mPosition = position
            }

            override fun onClick(p0: DialogInterface?, p1: Int) {
                items.removeAt(mPosition)
                onItemChangeListener?.onChange(mRecyclerView!!)
                //mRecyclerView!!.adapter.notifyItemRemoved(mPosition)
                mRecyclerView!!.adapter?.notifyDataSetChanged()
            }
        }
    }

    interface OnItemChangeListener {
        fun onChange(recyclerView: RecyclerView)
    }

    interface OnItemEditClickListener {
        fun onEditClicked(recyclerView: RecyclerView, adapter: ListItemRecyclerAdapter, position: Int)
    }
}
