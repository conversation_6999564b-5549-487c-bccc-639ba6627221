package uk.ac.bournemouthuniversity.at4send.technologies;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import uk.ac.bournemouthuniversity.at4send.BaseActivity;
import androidx.appcompat.widget.AppCompatTextView;
import android.widget.TextView;
import androidx.appcompat.widget.Toolbar;
import androidx.browser.customtabs.CustomTabsIntent;
import androidx.core.content.res.ResourcesCompat;

import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.view.MenuItem;
import android.widget.LinearLayout;

import com.afollestad.materialdialogs.MaterialDialog;
import com.google.android.material.appbar.CollapsingToolbarLayout;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

import org.jetbrains.annotations.NotNull;

import java.util.Locale;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.api.AT4SENDAPIClient;
import uk.ac.bournemouthuniversity.at4send.api.AT4SENDAPIInterface;
import uk.ac.bournemouthuniversity.at4send.extensions.ui.Dialogs;
import uk.ac.bournemouthuniversity.at4send.extensions.utils.Network;
import uk.ac.bournemouthuniversity.at4send.models.AssistiveTechnology;
import uk.ac.bournemouthuniversity.at4send.utils.ContrastUtils;
import uk.ac.bournemouthuniversity.at4send.utils.ReadAloudUtils;
import uk.ac.bournemouthuniversity.at4send.utils.TextToSpeechManager;

public class ViewAssistiveTechnologyActivity extends BaseActivity {
    private static final String TAG = "ViewTechnology";

    public static final String EXTRA_TECHNOLOGY_ID = "technologyID";
    public static final String EXTRA_TECHNOLOGY_NAME = "technologyName";

    private int mTechnologyID = 0;
    private String mTechnologyName;

    private CollapsingToolbarLayout mToolbarLayout;

    private AppCompatTextView mDescription, mManufacturer, mPrice, mWebsite;
    private LinearLayout mWebsiteClickArea;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_view_assistive_technology);

        Bundle extras = getIntent().getExtras();

        if(extras != null) {
            mTechnologyID = extras.getInt(EXTRA_TECHNOLOGY_ID);
            mTechnologyName = extras.getString(EXTRA_TECHNOLOGY_NAME);
        }

        mToolbarLayout = findViewById(R.id.toolbar_layout);
        mToolbarLayout.setTitle(""); // Clear the CollapsingToolbarLayout title

        // Set up the title TextView with read-aloud functionality
        TextView titleTextView = findViewById(R.id.toolbar_title);
        if (titleTextView != null) {
            if (mTechnologyName != null) {
                titleTextView.setText(mTechnologyName);
            }
            ReadAloudUtils.addReadAloudButton(this, titleTextView);
        }

        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        ActionBar ab = getSupportActionBar();

        if(ab != null){
            ab.setDisplayHomeAsUpEnabled(true);
            ab.setDisplayShowTitleEnabled(false);  // Hide the app name in the toolbar
        }

        mDescription = findViewById(R.id.fullTechnologyDescription);
        mManufacturer = findViewById(R.id.technologyManufacturerName);
        mPrice = findViewById(R.id.technologyRRP);
        mWebsite = findViewById(R.id.technologyWebsite);
        mWebsiteClickArea = findViewById(R.id.technologyWebsiteClickArea);

        // Add read-aloud button to description text
        if (mDescription != null) {
            ReadAloudUtils.addReadAloudButton(this, mDescription);
        }
        // Add read-aloud button to manufacturer text
        if (mManufacturer != null) {
            ReadAloudUtils.addReadAloudButton(this, mManufacturer);
        }
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        int itemId = item.getItemId();

        if(itemId == android.R.id.home){
            //super.onBackPressed();
            // findViewById(R.id.returnHome).setOnClickListener(v -> finish());
            finish();
            return true;
        }

        return super.onOptionsItemSelected(item);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Stop TTS when activity is destroyed
        TextToSpeechManager.getInstance(this).stop();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (ContrastUtils.isHighContrastEnabled(this)) {
            ContrastUtils.applyContrastToViewHierarchy(getWindow().getDecorView());
        }

        if(mTechnologyID == 0){
            finish();
            return;
        }

        if(!Network.isNetworkAvailable(this)){
            new MaterialDialog(ViewAssistiveTechnologyActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                    .title(null, "Network Access Required")
                    .message(null, "An active network connection is required for this function. Please connect to the internet and try again.", null)
                    .cancelable(false)
                    .positiveButton(null, "Dismiss", materialDialog -> {
                        materialDialog.dismiss();
                        finish();
                        return null;
                    }).show();
            return;
        }

        retrieveTechnologyInformation();
    }

    private void retrieveTechnologyInformation(){
        MaterialDialog progressDialog = Dialogs.generateProgressDialog(this, "Loading...");
        progressDialog.show();

        AT4SENDAPIInterface apiService =
                AT4SENDAPIClient.getClient().create(AT4SENDAPIInterface.class);

        Call<AssistiveTechnology> call = apiService.getTechnology(true, mTechnologyID);

        call.enqueue(new Callback<AssistiveTechnology>() {
            @Override
            public void onResponse(@NotNull Call<AssistiveTechnology>call, @NotNull Response<AssistiveTechnology> response) {
                if(response.body() != null){
                    AssistiveTechnology technology = response.body();
                    mDescription.setText(technology.getTechnologyDescription());
                    mManufacturer.setText(technology.getTechnologyManufacturerName());
                    mPrice.setText(String.format(Locale.UK, "%s%.2f", "£", technology.getTechnologyRRP()));
                    mWebsite.setText(technology.getTechnologyWebsite());

                    // Update the title TextView instead of CollapsingToolbarLayout
                    TextView titleTextView = findViewById(R.id.toolbar_title);
                    if (titleTextView != null) {
                        titleTextView.setText(technology.getTechnologyName());
                    }

                    mWebsiteClickArea.setOnClickListener(v -> {
                        CustomTabsIntent.Builder builder = new CustomTabsIntent.Builder();
                        builder.setColorScheme(CustomTabsIntent.COLOR_SCHEME_SYSTEM);
                        CustomTabsIntent customTabsIntent = builder.build();
                        customTabsIntent.launchUrl(ViewAssistiveTechnologyActivity.this, Uri.parse(technology.getTechnologyWebsite()));
                    });
                }else{
                    new MaterialDialog(ViewAssistiveTechnologyActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                            .title(null, "Error Occurred")
                            .message(null, "An error occurred whilst retrieving the chosen assistive technology. Please try again later.", null)
                            .cancelable(false)
                            .positiveButton(null, "Dismiss", materialDialog -> {
                                materialDialog.dismiss();
                                finish();
                                return null;
                            }).show();
                }
                progressDialog.dismiss();
            }

            @Override
            public void onFailure(@NotNull Call<AssistiveTechnology>call, @NotNull Throwable t) {
                // Log error here since request failed
                Log.e(TAG, t.toString());
                FirebaseCrashlytics.getInstance().recordException(t);

                progressDialog.dismiss();

                new MaterialDialog(ViewAssistiveTechnologyActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                        .title(null, "Error Occurred")
                        .message(null, "An error occurred whilst retrieving the technologies list. Please try again later.", null)
                        .cancelable(false)
                        .positiveButton(null, "Dismiss", materialDialog -> {
                            materialDialog.dismiss();
                            finish();
                            return null;
                        }).show();
            }
        });
    }
}





