<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center_vertical"
    android:paddingStart="16dp"
    android:paddingEnd="16dp"
    android:paddingTop="12dp"
    android:paddingBottom="12dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/abilityItemName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            tools:text="Ability Name"
            android:textAppearance="@style/AT4SEND.AppTheme.TextAppearance.Subtitle"
            android:textSize="20sp"/>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/helpIcon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="8dp"
            android:src="@drawable/ic_help"
            android:tint="@color/md_grey_900"
            android:padding="2dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:focusable="true" />

    </LinearLayout>

    <uk.ac.bournemouthuniversity.at4send.extensions.views.indicatorseekbar.IndicatorSeekBar
        android:id="@+id/rating_seek_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="54dp"
        app:isb_max="4"
        app:isb_min="-1"
        app:isb_track_rounded_corners="true"
        app:isb_progress="-1"
        app:isb_seek_smoothly="false"
        app:isb_thumb_adjust_auto="true"
        app:isb_ticks_count="6"
        app:isb_show_tick_marks_type="oval"
        app:isb_show_tick_texts="true"
        app:isb_tick_texts_array="@array/tick_marks_admin_abilities_rating"
        app:isb_tick_marks_color="@color/selector_rating_slider_tick_color"
        app:isb_indicator_color="@color/md_blue_600"
        app:isb_tick_marks_size="16dp"
        app:isb_indicator_text_color="@color/md_white_1000"
        app:isb_indicator_text_size="18sp"
        app:isb_show_indicator="circular_bubble"
        app:isb_thumb_color="@color/md_blue_600"
        app:isb_thumb_size="24dp"
        app:isb_track_progress_color="@color/md_blue_400"
        app:isb_track_progress_size="8dp"
        app:isb_track_background_color="@color/md_grey_300"
        app:isb_track_background_size="20dp"
        app:isb_tick_texts_color="@color/md_grey_500"/>

</LinearLayout>
