package uk.ac.bournemouthuniversity.at4send.models;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class AssistiveTechnology {

    @SerializedName("technology_id")
    private int technologyID;

    @SerializedName("technology_name")
    private String technologyName;

    @SerializedName("technology_description")
    private String technologyDescription;

    @SerializedName("technology_website")
    private String technologyWebsite;

    @SerializedName("technology_image")
    private String technologyImage;

    @SerializedName("technology_manufacturer_id")
    private int technologyManufacturerID;

    @SerializedName("manufacturer_name")
    private String technologyManufacturerName;

    @SerializedName("technology_rrp")
    private double technologyRRP;

    @SerializedName("technology_ability_links")
    private List<Ability> technologyAbilityLinks;

    @SerializedName("technology_condition_links")
    private List<Condition> technologyConditionLinks;

    public AssistiveTechnology(int technologyID, String technologyName, String technologyDescription, String technologyWebsite, String technologyImage, int technologyManufacturerID, String technologyManufacturerName, int technologyRRP, List<Ability> technologyAbilityLinks, List<Condition>  technologyConditionLinks) {
        this.technologyID = technologyID;
        this.technologyName = technologyName;
        this.technologyDescription = technologyDescription;
        this.technologyWebsite = technologyWebsite;
        this.technologyImage = technologyImage;
        this.technologyManufacturerID = technologyManufacturerID;
        this.technologyManufacturerName = technologyManufacturerName;
        this.technologyRRP = technologyRRP;
        this.technologyAbilityLinks = technologyAbilityLinks;
        this.technologyConditionLinks = technologyConditionLinks;
    }

    public int getTechnologyID() {
        return technologyID;
    }

    public void setTechnologyID(int technologyID) {
        this.technologyID = technologyID;
    }

    public String getTechnologyName() {
        return technologyName;
    }

    public void setTechnologyName(String technologyName) {
        this.technologyName = technologyName;
    }

    public String getTechnologyDescription() {
        return technologyDescription;
    }

    public void setTechnologyDescription(String technologyDescription) {
        this.technologyDescription = technologyDescription;
    }

    public String getTechnologyWebsite() {
        return technologyWebsite;
    }

    public void setTechnologyWebsite(String technologyWebsite) {
        this.technologyWebsite = technologyWebsite;
    }

    public String getTechnologyImage() {
        return technologyImage;
    }

    public void setTechnologyImage(String technologyImage) {
        this.technologyImage = technologyImage;
    }

    public int getTechnologyManufacturerID() {
        return technologyManufacturerID;
    }

    public void setTechnologyManufacturerID(int technologyManufacturerID) {
        this.technologyManufacturerID = technologyManufacturerID;
    }

    public String getTechnologyManufacturerName() {
        return technologyManufacturerName;
    }

    public void setTechnologyManufacturerName(String technologyManufacturerName) {
        this.technologyManufacturerName = technologyManufacturerName;
    }

    public double getTechnologyRRP() {
        return technologyRRP;
    }

    public void setTechnologyRRP(int technologyRRP) {
        this.technologyRRP = technologyRRP;
    }

    public List<Ability> getTechnologyAbilityLinks() {
        return technologyAbilityLinks;
    }

    public void setTechnologyAbilityLinks(List<Ability> technologyAbilityLinks) {
        this.technologyAbilityLinks = technologyAbilityLinks;
    }

    public List<Condition> getTechnologyConditionLinks() {
        return technologyConditionLinks;
    }

    public void setTechnologyConditionLinks(List<Condition> technologyConditionLinks) {
        this.technologyConditionLinks = technologyConditionLinks;
    }
}
