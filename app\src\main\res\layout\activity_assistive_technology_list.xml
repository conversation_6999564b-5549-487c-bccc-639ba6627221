<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/mainView"
    android:background="@color/md_grey_50"
    tools:context=".technologies.AssistiveTechnologyListActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:elevation="0dp">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            app:contentInsetStartWithNavigation="0dp"
            app:titleTextAppearance="@style/AT4SEND.AppTheme.TextAppearance.Title"
            app:title="Technology Results"
            app:theme="@style/AT4SEND.BlueTheme.NoActionBar.Toolbar">

        </androidx.appcompat.widget.Toolbar>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">


        <LinearLayout
            android:id="@+id/noResults"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="32dp"
            android:gravity="center"
            android:visibility="gone">

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="200dp"
                android:layout_height="200dp"
                app:srcCompat="@drawable/ic_empty"
                android:layout_marginBottom="16dp"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="No Results"
                android:textStyle="bold"
                android:textSize="20sp"
                android:gravity="center"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="There were no results based on your submission."
                android:gravity="center"
                android:textSize="16sp"/>

        </LinearLayout>


        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:fillViewport="true"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/returnHomeContainer">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardBackgroundColor="@color/md_white_1000"
                    app:cardCornerRadius="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:gravity="center_vertical"
                            android:paddingStart="16dp"
                            android:paddingEnd="16dp"
                            android:paddingTop="8dp"
                            android:paddingBottom="8dp">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/listExplanatoryTextHeading"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Below is the list of appropriate Assistive Technologies based on the following selections:"
                                android:textSize="16sp"/>

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/listExplanatoryText"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content" />
                        </LinearLayout>

                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/results"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardBackgroundColor="@color/md_white_1000"
                    app:cardCornerRadius="0dp"
                    app:strokeWidth="1dp"
                    app:strokeColor="@color/md_grey_200"
                    app:cardElevation="0dp"
                    android:layout_marginTop="20dp"
                    android:visibility="gone">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/mainRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        tools:listitem="@layout/item_assistive_technology"/>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>


        <com.google.android.material.card.MaterialCardView
            android:id="@+id/returnHomeContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardBackgroundColor="@color/md_white_1000"
            app:cardCornerRadius="0dp"
            app:strokeWidth="1dp"
            app:strokeColor="@color/md_grey_200"
            app:cardElevation="0dp"
            android:layout_marginTop="20dp"
            app:layout_constraintBottom_toBottomOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center_horizontal"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingTop="12dp"
                android:paddingBottom="12dp">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/deleteTechnologies"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:letterSpacing="0"
                    android:textStyle="bold"
                    style="@style/Widget.MaterialComponents.Button.UnelevatedButton"
                    android:text="DELETE SELECTED TECHNOLOGIES"
                    app:backgroundTint="@color/red500"
                    android:visibility="gone"/>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/exportTechnologies"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:letterSpacing="0"
                    android:textStyle="bold"
                    style="@style/Widget.MaterialComponents.Button.UnelevatedButton"
                    android:text="EXPORT RECOMMENDATIONS"
                    app:backgroundTint="@color/md_blue_600"
                    android:visibility="gone"/>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/returnHome"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:letterSpacing="0"
                    style="@style/Widget.MaterialComponents.Button.UnelevatedButton"
                    android:text="RETURN TO HOME SCREEN"
                    app:backgroundTint="@color/md_orange_500"/>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <LinearLayout
            android:id="@+id/loader"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="32dp"
            android:gravity="center">

            <me.zhanghai.android.materialprogressbar.MaterialProgressBar
                android:layout_width="40dp"
                android:layout_height="40dp"/>

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>