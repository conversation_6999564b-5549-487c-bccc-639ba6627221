package uk.ac.bournemouthuniversity.at4send.security;

import android.content.Intent;
import android.content.res.ColorStateList;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import androidx.activity.OnBackPressedCallback;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.Toolbar;
import androidx.biometric.BiometricManager;
import androidx.biometric.BiometricPrompt;
import androidx.core.content.ContextCompat;
import androidx.core.content.res.ResourcesCompat;

import com.afollestad.materialdialogs.MaterialDialog;

import java.util.concurrent.Executor;

import uk.ac.bournemouthuniversity.at4send.HomeActivity;
import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.extensions.helpers.PreferenceManager;
import uk.ac.bournemouthuniversity.at4send.BaseActivity;

/**
 * Create Pin Code Activity
 *
 * @version 1.1.0
 * @owner David Passmore
 * <AUTHOR> Creative UK
 * @copyright 2019
 */
public class EnterPinCodeActivity extends BaseActivity {

    public static final String EXTRA_SUCCESS_INTENT = "successIntent";
    public static final String EXTRA_CAN_GO_BACK = "canGoBack";

    InputMethodManager imm;

    String pinCodeForConfirm;

    PreferenceManager pM;
    boolean biometricsAvailable = false;
    boolean biometricUnlockEnabled = false;

    AppCompatEditText pinCodeAutoFocus;
    AppCompatImageView[] pinDigits;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_enter_pin_code);

        Bundle extras = getIntent().getExtras();
        boolean hasExtras = extras != null;

        pM = new PreferenceManager(this);
        pinCodeForConfirm = pM.getSharedStringPreference("at4ed_securePinCode", true);

        imm = (InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);

        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        if (hasExtras && extras.getBoolean(EXTRA_CAN_GO_BACK, false)) {
            ActionBar ab = getSupportActionBar();

            if (ab != null) {
                ab.setDisplayHomeAsUpEnabled(true);
            }
        }

        pinCodeAutoFocus = findViewById(R.id.pinCodeAutoFocus);
        pinDigits = new AppCompatImageView[]{
            findViewById(R.id.pinDigit1),
            findViewById(R.id.pinDigit2),
            findViewById(R.id.pinDigit3),
            findViewById(R.id.pinDigit4),
            findViewById(R.id.pinDigit5),
            findViewById(R.id.pinDigit6)
        };

        pinCodeAutoFocus.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                for (int i = 0; i < 6; i++) {
                    if (s.length() - 1 >= i) {
                        pinDigits[i].setImageDrawable(ResourcesCompat.getDrawable(getResources(), R.drawable.ic_circle, null));
                        pinDigits[i].setSupportImageTintList(ColorStateList.valueOf(ResourcesCompat.getColor(getResources(), R.color.colorAccent, null)));
                    } else {
                        pinDigits[i].setImageDrawable(ResourcesCompat.getDrawable(getResources(), R.drawable.ic_circle_outline, null));
                        pinDigits[i].setSupportImageTintList(ColorStateList.valueOf(ResourcesCompat.getColor(getResources(), R.color.md_grey_500, null)));
                    }
                }

                if (s.length() == 6) {
                    if (pinCodeForConfirm.equals(s.toString())) {
                        if (imm != null) {
                            imm.hideSoftInputFromWindow(pinCodeAutoFocus.getWindowToken(), 0);
                        }

                        if (hasExtras && extras.getSerializable(EXTRA_SUCCESS_INTENT, Class.class) != null) {
                            Class<?> targetClass = extras.getSerializable(EXTRA_SUCCESS_INTENT, Class.class);
                            startActivity(new Intent(EnterPinCodeActivity.this, targetClass));
                        } else {
                            startActivity(new Intent(EnterPinCodeActivity.this, HomeActivity.class));
                        }

                        finish();
                    } else {
                        new MaterialDialog(EnterPinCodeActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                                .title(null, "Error")
                                .message(null, "The pin code you entered was not correct. Please try again.", null)
                                .positiveButton(null, "OK", materialDialog -> {
                                    materialDialog.dismiss();
                                    pinCodeAutoFocus.setText("");
                                    resetPinDigits();
                                    View view = getCurrentFocus();
                                    if (view != null) {
                                        imm.showSoftInput(view, InputMethodManager.SHOW_IMPLICIT);
                                    }
                                    return null;
                                })
                                .cancelable(false)
                                .show();
                    }
                }
            }

            @Override
            public void afterTextChanged(Editable s) {
            }
        });

        BiometricManager biometricManager = BiometricManager.from(this);
        switch (biometricManager.canAuthenticate(BiometricManager.Authenticators.BIOMETRIC_STRONG)) {
            case BiometricManager.BIOMETRIC_SUCCESS:
                biometricsAvailable = true;
                break;
            case BiometricManager.BIOMETRIC_ERROR_HW_UNAVAILABLE:
            case BiometricManager.BIOMETRIC_ERROR_NO_HARDWARE:
            case BiometricManager.BIOMETRIC_ERROR_NONE_ENROLLED:
                biometricsAvailable = false;
                break;
        }

        biometricUnlockEnabled = pM.getSharedBooleanPreference("biometricUnlock", false, false);

        presentBiometricLogin();
    }

    private void presentBiometricLogin() {
        if (biometricUnlockEnabled) {
            if (biometricsAvailable) {
                if (imm != null) {
                    imm.hideSoftInputFromWindow(pinCodeAutoFocus.getWindowToken(), 0);
                }

                Executor executor = ContextCompat.getMainExecutor(this);
                BiometricPrompt biometricPrompt = new BiometricPrompt(this, executor, new BiometricPrompt.AuthenticationCallback() {
                    @Override
                    public void onAuthenticationError(int errorCode, @NonNull CharSequence errString) {
                        super.onAuthenticationError(errorCode, errString);

                        if (errorCode == BiometricPrompt.ERROR_CANCELED || errorCode == BiometricPrompt.ERROR_NEGATIVE_BUTTON) {
                            View view = getCurrentFocus();
                            if (view != null) {
                                imm.showSoftInput(view, InputMethodManager.SHOW_IMPLICIT);
                            }
                        } else {
                            new MaterialDialog(EnterPinCodeActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                                    .title(null, "Error")
                                    .message(null, errString, null)
                                    .positiveButton(null, "OK", materialDialog -> {
                                        materialDialog.dismiss();
                                        return null;
                                    }).show();
                        }
                    }

                    @Override
                    public void onAuthenticationSucceeded(@NonNull BiometricPrompt.AuthenticationResult result) {
                        super.onAuthenticationSucceeded(result);
                        pinCodeAutoFocus.setText(pinCodeForConfirm);
                    }

                    @Override
                    public void onAuthenticationFailed() {
                        super.onAuthenticationFailed();
                    }
                });

                BiometricPrompt.PromptInfo promptInfo = new BiometricPrompt.PromptInfo.Builder()
                        .setTitle("Login to EduAbility")
                        .setSubtitle("Login using your biometric credential")
                        .setNegativeButtonText("USE PIN INSTEAD")
                        .build();

                biometricPrompt.authenticate(promptInfo);
            } else {
                new MaterialDialog(this, MaterialDialog.getDEFAULT_BEHAVIOR())
                        .title(null, "Error")
                        .message(null, "Your biometric credentials have changed, please re-verify your credentials.", null)
                        .positiveButton(null, "OK", materialDialog -> {
                            materialDialog.dismiss();
                            return null;
                        }).show();
            }
        }
    }

    private void resetPinDigits() {
        for (int i = 0; i < 6; i++) {
            pinDigits[i].setImageDrawable(ResourcesCompat.getDrawable(getResources(), R.drawable.ic_circle_outline, null));
            pinDigits[i].setSupportImageTintList(ColorStateList.valueOf(ResourcesCompat.getColor(getResources(), R.color.md_grey_500, null)));
        }
    }

    private void showKeyboard(View view) {
        if (imm != null && view != null) {
            view.requestFocus();
            imm.showSoftInput(view, InputMethodManager.SHOW_IMPLICIT);
        }
    }

    private void hideKeyboard(View view) {
        if (imm != null && view != null) {
            imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        MenuInflater menuInflater = getMenuInflater();
        menuInflater.inflate(R.menu.menu_enter_pin_activity, menu);

        MenuItem biometrics = menu.findItem(R.id.enter_pin_menu_biometrics);
        if (biometrics != null) {
            if (!biometricsAvailable || !biometricUnlockEnabled) {
                biometrics.setVisible(false);
            }
        }

        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        int itemId = item.getItemId();

        if (itemId == android.R.id.home) {
            finish();
        } else if (itemId == R.id.enter_pin_menu_show_keyboard) {
            View view = getCurrentFocus();
            if (view != null) {
                imm.showSoftInput(view, InputMethodManager.SHOW_IMPLICIT);
            }
        } else if (itemId == R.id.enter_pin_menu_biometrics) {
            presentBiometricLogin();

        }
        return super.onOptionsItemSelected(item);
    }
}


