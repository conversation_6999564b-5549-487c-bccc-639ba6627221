package uk.ac.bournemouthuniversity.at4send.quiz.models;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

public class Question {

    private final List<Answer> answers;
    private final List<Answer> correctAnswers;
    private int numberOfCorrectAnswers = 0;
    private List<String> categories;
    private String explanation;
    private String questionText;
    private String imageUri;
    private Map<String, String> questionAttributes = new HashMap<>();

    // if for each question there are some follow up, related, sub-questions, use this field.
    private List<Question> subQuestions;

    public Question(String questionText, String imageUri, List<Answer> answers, List<String> categories) {
        super();
        this.answers = answers;
        this.categories = categories;
        this.questionText = questionText;
        this.imageUri = imageUri;
        this.correctAnswers = new LinkedList<>();
        for (final Answer a : this.answers) {
            if (a.isCorrect()) {
                this.correctAnswers.add(a);
                numberOfCorrectAnswers++;
            }
        }
    }

    public int getNumberOfAnswers() {
        return this.answers.size();
    }

    public List<Answer> getAnswers() {
        return this.answers;
    }

    public List<Answer> getCorrectAnswers() {
        return correctAnswers;
    }

    @SuppressWarnings("ToArrayCallWithZeroLengthArrayArgument")
    public Answer[] getAnswersAsArray() {
        return this.answers.toArray(new Answer[this.answers.size()]);
    }

    public final String getExplanation() {
        return this.explanation;
    }

    public String getQuestionText() {
        return this.questionText;
    }

    public final void setExplanation(final String explanation) {
        this.explanation = explanation;
    }

    public List<String> getCategories() {
        return categories;
    }

    public void setCategories(List<String> categories) {
        this.categories = categories;
    }

    public Map<String, String> getQuestionAttributes() {
        return questionAttributes;
    }

    public void setQuestionAttributes(Map<String, String> questionAttributes) {
        this.questionAttributes = questionAttributes;
    }

    public boolean hasMultipleCorrectAnswers() {
        return numberOfCorrectAnswers > 1;
    }

    public int getNumberOfCorrectAnswers() {
        return numberOfCorrectAnswers;
    }

    public String getImageUri() {
        return imageUri;
    }

    public void setImageUri(String imageUri) {
        this.imageUri = imageUri;
    }

    public void setQuestionText(String questionText) {
        this.questionText = questionText;
    }

    public List<Question> getSubQuestions() {
        return subQuestions;
    }

    public void setSubQuestions(List<Question> subQuestions) {
        this.subQuestions = subQuestions;
    }
}