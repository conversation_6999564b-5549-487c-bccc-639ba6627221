package uk.ac.bournemouthuniversity.at4send.quiz.functions;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import uk.ac.bournemouthuniversity.at4send.quiz.helpers.QuizSession;
import uk.ac.bournemouthuniversity.at4send.quiz.models.Answer;
import uk.ac.bournemouthuniversity.at4send.quiz.models.Question;

public class TriviaQuiz implements Quiz {

    protected QuizSession session;
    protected int record = 0;
    protected List<Question> questions;
    protected List<Question> answeredQuestions;
    protected Question currentQuestion;
    protected boolean exited = false;

    public TriviaQuiz(QuestionDatabase questionDatabase, List<String> selectedCategories) {
        if (questionDatabase == null) {
            throw new IllegalArgumentException("QuestionDatabase cannot be null");
        }
        
        if ((selectedCategories != null) && (selectedCategories.size() > 0)) {
            this.questions = questionDatabase.getFilteredQuestions(selectedCategories);
        } else {
            this.questions = questionDatabase.getQuestions();
        }

        if (this.questions == null || this.questions.isEmpty()) {
            throw new IllegalStateException("No questions available for this quiz");
        }

        this.answeredQuestions = new ArrayList<>();
        this.session = new QuizSession(); // Initialize with default session
    }

    @Override
    public void reset() {
        session = new QuizSession();
        this.answeredQuestions = new ArrayList<>();
    }

    @Override
    public QuizSession getSession() {
        return session;
    }

    @Override
    public void setSession(QuizSession session) {
        if (session != null) {
            this.session = session;
        }
    }

    @Override
    public Question getQuestion() {
        if (questions == null || questions.isEmpty()) {
            return null;
        }
        
        List<Question> intersection = new ArrayList<>(questions);
        intersection.removeAll(answeredQuestions);
        Question question = getRandomQuestionFromQuestionList(intersection);
        this.currentQuestion = question;
        return question;
    }

    protected Question getRandomQuestionFromQuestionList(List<Question> questions) {
        if (questions == null || questions.isEmpty()) {
            return null;
        }
        Collections.shuffle(questions);
        return questions.get(0);
    }

    @Override
    public boolean checkAnswer(Question question, Answer answer, boolean firstAttempt) {
        boolean correct = question.getCorrectAnswers().contains(answer);

        if (!answeredQuestions.contains(question)) {
            answeredQuestions.add(question);
        }

        this.session.setTotalAttempts(this.session.getTotalAttempts() + 1);

        if (correct) {
            this.session.setCorrectAttempts(this.session.getCorrectAttempts() + 1);
            this.session.setConsecutiveAttempts(this.session.getConsecutiveAttempts() + 1);
            updateRecord();
        } else {
            this.session.setConsecutiveAttempts(0);

        }

        return correct;
    }

    @Override
    public boolean checkAnswers(Question question, List<Answer> answers, boolean firstAttempt) {
        boolean correct = question.getCorrectAnswers().containsAll(answers);

        if (!answeredQuestions.contains(question)) {
            answeredQuestions.add(question);
        }

        this.session.setTotalAttempts(this.session.getTotalAttempts() + 1);

        if (correct) {
            this.session.setCorrectAttempts(this.session.getCorrectAttempts() + 1);
            this.session.setConsecutiveAttempts(this.session.getConsecutiveAttempts() + 1);
            updateRecord();
        } else {
            this.session.setConsecutiveAttempts(0);
        }

        return correct;
    }

    public void updateRecord() {
        if (session.getConsecutiveAttempts() > this.record) {
            this.record = session.getConsecutiveAttempts();
        }
    }

    @Override
    public boolean isNewRecord() {
        if (session.getConsecutiveAttempts() > this.record) {
            this.record = session.getConsecutiveAttempts();
            return true;
        } else {
            return false;
        }
    }

    @Override
    public int getRecord() {
        return this.record;
    }

    @Override
    public void setRecord(int record) {
        this.record = record;
    }

    @Override
    public boolean isExited() {
        return exited;
    }

    @Override
    public void setExited(boolean exited) {
        this.exited = exited;
    }

}
