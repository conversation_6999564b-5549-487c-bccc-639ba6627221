<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardBackgroundColor="@color/md_white_1000"
            app:cardCornerRadius="0dp"
            app:strokeWidth="1dp"
            app:strokeColor="@color/md_grey_200"
            app:cardElevation="0dp"
            android:layout_marginTop="20dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="center_vertical"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:paddingTop="12dp"
                    android:paddingBottom="8dp">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Hardware"
                        android:textAppearance="@style/AT4SEND.AppTheme.TextAppearance.Title"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        />
                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/hardwareTrainingTechnologies"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    tools:listitem="@layout/item_assistive_technology_training"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</androidx.core.widget.NestedScrollView>