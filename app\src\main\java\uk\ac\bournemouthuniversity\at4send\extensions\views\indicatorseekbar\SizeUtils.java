package uk.ac.bournemouthuniversity.at4send.extensions.views.indicatorseekbar;

import android.content.Context;
import android.util.TypedValue;

/**
 * created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on  2017/9/9
 */

public class SizeUtils {
    public static int dp2px(Context context, float dpValue) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, dpValue, context.getResources().getDisplayMetrics());
    }

    public static int sp2px(Context context, float spValue) {
        return (int) (spValue * context.getResources().getDisplayMetrics().scaledDensity + 0.5f);
    }

    public static int px2sp(Context context, float pxValue) {
        return (int) (pxValue / context.getResources().getDisplayMetrics().scaledDensity + 0.5f);
    }

}
