package com.eduability.at4send2.fragments

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import uk.ac.bournemouthuniversity.at4send.R

class TrainingFragment : Fragment() {

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_training, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Set up click listener for feedback option
        view.findViewById<View>(R.id.training_feedback_card).setOnClickListener {
            findNavController().navigate(R.id.trainingFeedbackFragment)
        }

        // Other click listeners for training options can be added here
    }
} 