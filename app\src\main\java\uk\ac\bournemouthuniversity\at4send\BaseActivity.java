package uk.ac.bournemouthuniversity.at4send;

import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.os.Bundle;
import androidx.appcompat.app.AppCompatActivity;
import androidx.preference.PreferenceManager;

import uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils;
import uk.ac.bournemouthuniversity.at4send.utils.ContrastUtils;
import uk.ac.bournemouthuniversity.at4send.utils.ButtonSizeUtils;
import uk.ac.bournemouthuniversity.at4send.utils.TextToSpeechManager;

public class BaseActivity extends AppCompatActivity {
    protected SharedPreferences preferences;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        applyTextSize();
        applyButtonSize();
        super.onCreate(savedInstanceState);
        preferences = PreferenceManager.getDefaultSharedPreferences(this);
        preferences.registerOnSharedPreferenceChangeListener(preferenceChangeListener);
        
        if (ContrastUtils.isHighContrastEnabled(this)) {
            // Apply contrast settings after content view is set
            getWindow().getDecorView().post(() -> {
                ContrastUtils.applyContrastToViewHierarchy(getWindow().getDecorView());
            });
        }
    }

    private final SharedPreferences.OnSharedPreferenceChangeListener preferenceChangeListener = 
        new SharedPreferences.OnSharedPreferenceChangeListener() {
            @Override
            public void onSharedPreferenceChanged(SharedPreferences sharedPreferences, String key) {
                if (key.equals("text_size") || key.equals("high_contrast") || 
                    key.equals("contrast_theme") || key.equals("button_size")) {
                    recreate();
                }
            }
        };

    @Override
    protected void onDestroy() {
        super.onDestroy();
        preferences.unregisterOnSharedPreferenceChangeListener(preferenceChangeListener);
        
        // Shutdown TTS when activity is destroyed
        TextToSpeechManager.getInstance(this).stop();
    }

    protected void applyTextSize() {
        float textSize = preferences != null ? 
            preferences.getInt("text_size", 100) / 100f : 
            TextSizeUtils.getTextSizeScale(this);
        TextSizeUtils.setTextSize(this, textSize);
    }
    
    protected void applyButtonSize() {
        ButtonSizeUtils.applyButtonSize(this);
    }
    
    @Override
    public void onContentChanged() {
        super.onContentChanged();
        // Apply button size to all views after content is set
        ButtonSizeUtils.applyButtonSizeToView(getWindow().getDecorView());
    }
}

