<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".security.CreatePinCodeActivity"
    android:background="@color/md_white_1000">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:elevation="0dp">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:title=" "
            app:elevation="0dp"
            android:background="@color/md_white_1000"/>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fillViewport="false"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintVertical_bias="0.4">

            <LinearLayout
                android:padding="16dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                android:clipToPadding="false"
                android:clipChildren="false">

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="140dp"
                    android:layout_height="140dp"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:srcCompat="@drawable/ic_launcher_foreground"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/createPinCodeInstruction"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="Enter your Pin Code"
                    android:textAppearance="@style/AT4SEND.AppTheme.TextAppearance.Subtitle"
                    android:textSize="20sp"
                    android:layout_marginBottom="10dp"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/createPinCodeInstructionVerbatim"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="Please enter a 6 digit pin code to access the application."/>

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/pinCodeAutoFocus"
                    android:layout_width="1dp"
                    android:layout_height="1dp"
                    android:layout_gravity="center"
                    android:inputType="numberPassword"
                    android:maxLength="6">
                    <requestFocus/>
                </androidx.appcompat.widget.AppCompatEditText>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="24dp">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/pinDigit1"
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        app:srcCompat="@drawable/ic_circle_outline"
                        app:tint="@color/md_grey_500"
                        android:layout_margin="10dp"/>

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/pinDigit2"
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        app:srcCompat="@drawable/ic_circle_outline"
                        app:tint="@color/md_grey_500"
                        android:layout_margin="10dp" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/pinDigit3"
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        app:srcCompat="@drawable/ic_circle_outline"
                        app:tint="@color/md_grey_500"
                        android:layout_margin="10dp" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/pinDigit4"
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        app:srcCompat="@drawable/ic_circle_outline"
                        app:tint="@color/md_grey_500"
                        android:layout_margin="10dp" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/pinDigit5"
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        app:srcCompat="@drawable/ic_circle_outline"
                        app:tint="@color/md_grey_500"
                        android:layout_margin="10dp" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/pinDigit6"
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        app:srcCompat="@drawable/ic_circle_outline"
                        app:tint="@color/md_grey_500"
                        android:layout_margin="10dp" />

                </LinearLayout>

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:textStyle="bold"
                    android:textColor="@color/md_red_500"
                    android:text="Forgotten your Pin Code?"
                    android:layout_marginTop="48dp"
                    android:layout_marginBottom="4dp"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="You will need to login to the app again using your original user name and password:"/>

                <com.google.android.material.button.MaterialButton
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    style="@style/Widget.MaterialComponents.Button.UnelevatedButton"
                    app:backgroundTint="@color/md_red_500"
                    android:textColor="@color/md_white_1000"
                    android:layout_marginTop="8dp"
                    android:text="Reset App"
                    android:textStyle="bold"
                    android:paddingEnd="28dp"
                    android:paddingStart="28dp"
                    android:letterSpacing="0"/>


            </LinearLayout>

        </ScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>