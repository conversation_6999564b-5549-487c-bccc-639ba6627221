package uk.ac.bournemouthuniversity.at4send.utils;

import android.app.Activity;
import android.content.Context;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.graphics.Color;
import android.util.DisplayMetrics;
import androidx.preference.PreferenceManager;
import androidx.core.content.ContextCompat;
import android.content.res.ColorStateList;
import uk.ac.bournemouthuniversity.at4send.R;

public class TextSizeUtils {
    private static final String TEXT_SIZE_KEY = "text_size";
    private static final String HIGH_CONTRAST_KEY = "high_contrast";
    private static final String CONTRAST_THEME_KEY = "contrast_theme";
    private static float lastAppliedScale = 1.0f;
    private static long lastRecreateTime = 0;
    private static final long RECREATE_THRESHOLD = 200; // milliseconds

    public static void setTextSize(Context context, float scale) {
        if (context == null) return;

        // Limit scale between 0.5 and 2.0
        scale = Math.max(0.5f, Math.min(2.0f, scale));

        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(context);
        prefs.edit().putInt(TEXT_SIZE_KEY, (int)(scale * 80)).apply();

        if (Math.abs(lastAppliedScale - scale) < 0.01f) {
            return;
        }

        Resources resources = context.getResources();
        Configuration configuration = new Configuration(resources.getConfiguration());
        configuration.fontScale = scale;
        lastAppliedScale = scale;

        if (context instanceof Activity) {
            Activity activity = (Activity) context;
            activity.createConfigurationContext(configuration);
            resources.updateConfiguration(configuration, resources.getDisplayMetrics());
            
            long currentTime = System.currentTimeMillis();
            if (!activity.isFinishing() && !activity.isDestroyed() 
                && (currentTime - lastRecreateTime) > RECREATE_THRESHOLD) {
                lastRecreateTime = currentTime;
                activity.recreate();
            }
        } else {
            resources.updateConfiguration(configuration, resources.getDisplayMetrics());
        }
    }

    public static void applyTextSize(Context context) {
        if (context == null) return;
        float scale = getTextSizeScale(context);
        setTextSize(context, scale);
    }

    public static float getTextSizeScale(Context context) {
        if (context == null) return 1.0f;
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(context);
        int textSizePercent = prefs.getInt(TEXT_SIZE_KEY, 80);
        return textSizePercent / 80f;
    }

    public static boolean isHighContrastEnabled(Context context) {
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(context);
        return prefs.getBoolean(HIGH_CONTRAST_KEY, false);
    }

    public static ColorStateList getTextColor(Context context) {
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(context);
        if (!isHighContrastEnabled(context)) {
            return ColorStateList.valueOf(ContextCompat.getColor(context, R.color.grey600));
        }

        String theme = prefs.getString(CONTRAST_THEME_KEY, "white_on_black");
        switch (theme) {
            case "black_on_white":
                // Fallback for legacy settings
                return ColorStateList.valueOf(Color.BLACK);
            case "white_on_black":
                return ColorStateList.valueOf(Color.WHITE);
            case "yellow_on_black":
                return ColorStateList.valueOf(Color.YELLOW);
            default:
                // Default to white on black if theme not recognized
                return ColorStateList.valueOf(Color.WHITE);
        }
    }

    public static ColorStateList getBackgroundColor(Context context) {
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(context);
        if (!isHighContrastEnabled(context)) {
            return ColorStateList.valueOf(ContextCompat.getColor(context, R.color.grey100));
        }

        String theme = prefs.getString(CONTRAST_THEME_KEY, "white_on_black");
        switch (theme) {
            case "black_on_white":
                // Fallback for legacy settings
                return ColorStateList.valueOf(Color.WHITE);
            case "white_on_black":
            case "yellow_on_black":
                return ColorStateList.valueOf(Color.BLACK);
            default:
                // Default to black background if theme not recognized
                return ColorStateList.valueOf(Color.BLACK);
        }
    }

    /**
     * Gets the button size scale factor based on user preferences
     * @param context The context
     * @return The button size scale factor
     */
    public static float getButtonSizeScale(Context context) {
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(context);
        String buttonSize = preferences.getString("button_size", "normal");
        
        switch (buttonSize) {
            case "small":
                return 0.8f;
            case "large":
                return 1.2f;
            case "extra_large":
                return 1.5f;
            case "normal":
            default:
                return 1.0f;
        }
    }
}
