<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/itemView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:paddingTop="12dp"
        android:paddingBottom="12dp"
        android:clickable="true"
        android:focusable="true"
        android:foreground="?attr/selectableItemBackground">

        <com.google.android.material.card.MaterialCardView
            android:visibility="gone"
            android:layout_width="48dp"
            android:layout_height="48dp"
            app:cardBackgroundColor="@color/md_blue_600"
            app:cardCornerRadius="6dp"
            app:cardElevation="0dp"
            android:layout_gravity="center"
            android:layout_marginEnd="16dp">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/assistiveTechnologyImage"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:srcCompat="@drawable/illustration_learning_blackboard"
                />

        </com.google.android.material.card.MaterialCardView>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginEnd="16dp">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/assistivename"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                tools:text="Assistive Technology Name"
                android:textSize="17sp"
                android:textAppearance="@style/AT4SEND.AppTheme.TextAppearance.Subtitle"
                android:textFontWeight="500"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/assistiveTechnologyShortDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                tools:text="Short description of technology with space for 2 lines of description. Full description is available when clicked."
                android:maxLines="2"
                android:ellipsize="end"
                />

        </LinearLayout>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/selectionIcon"
            android:layout_width="24dp"
            android:layout_height="24dp"
            app:srcCompat="@drawable/ic_chevron_right"
            app:tint="@color/md_grey_500"/>
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginStart="16dp"
        android:background="@color/md_grey_200"/>

</LinearLayout>