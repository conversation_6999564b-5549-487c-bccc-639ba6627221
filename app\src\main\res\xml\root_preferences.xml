<!--
  ~ Copyright 2018 The app Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<PreferenceScreen
    xmlns:app="http://schemas.android.com/apk/res-auto"
    app:title="Settings">

    <PreferenceCategory
        app:key="accessibility"
        app:title="Accessibility"
        app:iconSpaceReserved="false">

        <SeekBarPreference
            app:key="text_size"
            app:title="Text Size"
            app:summary="Adjust the size of text throughout the app"
            app:defaultValue="100"
            app:min="50"
            app:max="200"
            app:showSeekBarValue="true"
            app:iconSpaceReserved="false"/>

        <ListPreference
            app:key="button_size"
            app:title="Button Size"
            app:summary="Adjust the size of buttons throughout the app"
            app:defaultValue="normal"
            app:entries="@array/button_size_entries"
            app:entryValues="@array/button_size_values"
            app:iconSpaceReserved="false"/>

        <!-- <ListPreference
            app:key="voice_gender"
            app:title="Voice Gender"
            app:summary="Choose the gender of the text-to-speech voice"
            app:defaultValue="female"
            app:entries="@array/voice_gender_entries"
            app:entryValues="@array/voice_gender_values"
            app:iconSpaceReserved="false"/> -->

        <SwitchPreferenceCompat
            app:key="high_contrast"
            app:title="High Contrast"
            app:summary="Increase contrast for better readability"
            app:defaultValue="false"
            app:iconSpaceReserved="false"/>

        <ListPreference
            app:key="contrast_theme"
            app:title="Contrast Theme"
            app:summary="Choose your preferred contrast theme"
            app:entries="@array/contrast_theme_entries"
            app:entryValues="@array/contrast_theme_values"
            app:defaultValue="white_on_black"
            app:iconSpaceReserved="false"
            app:dependency="high_contrast"/>

    </PreferenceCategory>

    <PreferenceScreen
        app:key="messagesScreen"
        app:title="@string/messages_header"
        app:iconSpaceReserved="false"
        app:icon="@drawable/ic_settings"
        app:isPreferenceVisible="false">

        <PreferenceCategory app:title="@string/messages_header"
            app:iconSpaceReserved="false">

            <EditTextPreference
                app:key="signature"
                app:title="@string/signature_title"
                app:useSimpleSummaryProvider="true"
                app:iconSpaceReserved="false"/>

            <ListPreference
                app:defaultValue="reply"
                app:entries="@array/reply_entries"
                app:entryValues="@array/reply_values"
                app:key="reply"
                app:title="@string/reply_title"
                app:useSimpleSummaryProvider="true"
                app:iconSpaceReserved="false" />

        </PreferenceCategory>

    </PreferenceScreen>

    <PreferenceScreen
        app:key="syncScreen"
        app:title="@string/sync_header"
        app:iconSpaceReserved="false"
        app:icon="@drawable/ic_database_sync"
        app:isPreferenceVisible="false">

        <PreferenceCategory app:title="@string/sync_header"
            app:iconSpaceReserved="false">

            <SwitchPreferenceCompat
                app:key="sync"
                app:title="@string/sync_title"
                app:iconSpaceReserved="false" />

            <SwitchPreferenceCompat
                app:dependency="sync"
                app:key="attachment"
                app:summaryOff="@string/attachment_summary_off"
                app:summaryOn="@string/attachment_summary_on"
                app:title="@string/attachment_title"
                app:iconSpaceReserved="false" />

        </PreferenceCategory>

    </PreferenceScreen>


    <PreferenceScreen
        app:key="securityScreen"
        app:title="@string/security_header"
        app:iconSpaceReserved="false"
        app:icon="@drawable/ic_lock">

        <PreferenceCategory
            app:title="@string/security_header"
            app:iconSpaceReserved="false">

            <SwitchPreferenceCompat
                app:key="secureApp"
                app:title="Secure Application"
                app:iconSpaceReserved="false" />

            <Preference
                app:dependency="secureApp"
                app:title="Pin Code"
                app:key="pinCode"
                app:iconSpaceReserved="false" />

            <SwitchPreferenceCompat
                app:dependency="pinCode"
                app:title="Biometric Unlock"
                app:key="biometricUnlock"
                app:useSimpleSummaryProvider="true"
                app:iconSpaceReserved="false" />

        </PreferenceCategory>

    </PreferenceScreen>

    <Preference
        app:key="appVersion"
        app:title="App Version"
        app:summary="0.0.1 - Build 1 (29/12/2019 22:35)"
        app:iconSpaceReserved="false" />

</PreferenceScreen>
