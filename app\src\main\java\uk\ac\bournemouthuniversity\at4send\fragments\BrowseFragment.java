package uk.ac.bournemouthuniversity.at4send.fragments;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.fragment.app.Fragment;
import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.BaseFragment;
import uk.ac.bournemouthuniversity.at4send.extensions.utils.Network;
import com.afollestad.materialdialogs.MaterialDialog;
import uk.ac.bournemouthuniversity.at4send.technologies.BrowseAssistiveTechnologiesActivity;

public class BrowseFragment extends BaseFragment {

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_browse, container, false);

        view.findViewById(R.id.browse_assistive_technologies_card).setOnClickListener(v -> {
            if (!Network.isNetworkAvailable(requireContext())) {
                new MaterialDialog(requireContext(), MaterialDialog.getDEFAULT_BEHAVIOR())
                        .title(null, "Network Access Required")
                        .message(null, "An active network connection is required for this function. Please connect to the internet and try again.", null)
                        .cancelable(false)
                        .positiveButton(null, "Dismiss", materialDialog -> {
                            materialDialog.dismiss();
                            requireActivity().finish();
                            return null;
                        }).show();
                return;
            }

            Intent i = new Intent(requireActivity(), BrowseAssistiveTechnologiesActivity.class);
            startActivity(i);
        });

        return view;
    }
}



