package uk.ac.bournemouthuniversity.at4send.models.training;

import com.google.gson.annotations.SerializedName;

import org.threeten.bp.LocalDateTime;
import org.threeten.bp.format.DateTimeFormatter;

public class QuizResult {

    @SerializedName("quiz_result_id ")
    private int quizResultID;

    @SerializedName("quiz_name")
    private String quizName;

    @SerializedName("quiz_score")
    private double quizScore;

    @SerializedName("quiz_pass")
    private int quizPassed;

    @SerializedName("quiz_taken")
    private String quizTaken;

    @SerializedName("fb_uid")
    private String firebaseUserID;

    public QuizResult(int quizResultID, String quizName, double quizScore, int quizPassed, String quizTaken, String firebaseUserID) {
        this.quizResultID = quizResultID;
        this.quizName = quizName;
        this.quizScore = quizScore;
        this.quizPassed = quizPassed;
        this.firebaseUserID = firebaseUserID;
    }

    public QuizResult(String quizName, double quizScore, int quizPassed, String quizTaken, String firebaseUserID) {
        this.quizName = quizName;
        this.quizScore = quizScore;
        this.quizPassed = quizPassed;
        this.firebaseUserID = firebaseUserID;
    }

    public int getQuizResultID() {
        return quizResultID;
    }

    public void setQuizResultID(int quizResultID) {
        this.quizResultID = quizResultID;
    }

    public String getQuizName() {
        return quizName;
    }

    public void setQuizName(String quizName) {
        this.quizName = quizName;
    }

    public double getQuizScore() {
        return quizScore;
    }

    public void setQuizScore(double quizScore) {
        this.quizScore = quizScore;
    }

    public int getQuizPassed() {
        return quizPassed;
    }

    public void setQuizPassed(int quizPassed) {
        this.quizPassed = quizPassed;
    }

    public String getQuizTaken() {
        LocalDateTime dateTime = LocalDateTime.parse(quizTaken, DateTimeFormatter.ofPattern("yyyy-MM-dd kk:mm:ss"));
        return dateTime.format(DateTimeFormatter.ofPattern("dd/MM/yyyy kk:mm"));
    }

    public void setQuizTaken(LocalDateTime quizTaken) {
        this.quizTaken = quizTaken.format(DateTimeFormatter.ofPattern("yyyy-MM-dd kk:mm:ss"));
    }

    public String getFirebaseUserID() {
        return firebaseUserID;
    }

    public void setFirebaseUserID(String firebaseUserID) {
        this.firebaseUserID = firebaseUserID;
    }
}
