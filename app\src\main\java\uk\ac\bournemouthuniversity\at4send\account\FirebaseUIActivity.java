package uk.ac.bournemouthuniversity.at4send.account;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;

import androidx.activity.result.ActivityResultLauncher;
import androidx.annotation.NonNull;
import uk.ac.bournemouthuniversity.at4send.BaseActivity;

import com.afollestad.materialdialogs.MaterialDialog;
import com.firebase.ui.auth.AuthUI;
import com.firebase.ui.auth.FirebaseAuthUIActivityResultContract;
import com.firebase.ui.auth.IdpResponse;
import com.firebase.ui.auth.data.model.FirebaseAuthUIAuthenticationResult;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseUser;

import java.util.Arrays;
import java.util.List;

import uk.ac.bournemouthuniversity.at4send.HomeActivity;
import uk.ac.bournemouthuniversity.at4send.R;

public class FirebaseUIActivity extends BaseActivity {

    private final ActivityResultLauncher<Intent> signInLauncher = registerForActivityResult(
            new FirebaseAuthUIActivityResultContract(),
            this::onSignInResult
    );

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_firebase_ui);

        createSignInIntent();
    }

    public void createSignInIntent() {
        // Choose authentication providers
        List<AuthUI.IdpConfig> providers = Arrays.asList(
                new AuthUI.IdpConfig.EmailBuilder().build(),
                new AuthUI.IdpConfig.GoogleBuilder().build());

        // Create and launch sign-in intent
        Intent signInIntent = AuthUI.getInstance()
                .createSignInIntentBuilder()
                .setAvailableProviders(providers)
                .setLogo(R.drawable.eduability_logo)
                .setTheme(R.style.AT4SEND_BlueTheme)
                .build();
        signInLauncher.launch(signInIntent);
    }

    private void onSignInResult(FirebaseAuthUIAuthenticationResult result) {
        IdpResponse response = result.getIdpResponse();
        if (result.getResultCode() == RESULT_OK) {
            // Successfully signed in
            FirebaseUser user = FirebaseAuth.getInstance().getCurrentUser();
            try {
                Intent homeIntent = new Intent(this, HomeActivity.class);
                homeIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                startActivity(homeIntent);
                finish();
            } catch (Exception e) {
                Log.e("FirebaseUIActivity", "Error launching HomeActivity: " + e.getMessage(), e);
                new MaterialDialog(FirebaseUIActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                        .title(null, "Error")
                        .message(null, "An error occurred while launching the application. Please try again.", null) //Corrected line
                        .positiveButton(null, "OK", materialDialog -> {
                            materialDialog.dismiss();
                            finish();
                            return null;
                        })
                        .show();
            }
        } else {
            // Sign in failed. If response is null the user canceled the
            // sign-in flow using the back button. Otherwise check
            // response.getError().getErrorCode() and handle the error.
            // ...

            if(response != null && response.getError() != null) {
                Log.e("FirebaseUIActivity", "Sign-in error: " + response.getError().getMessage() +
                    " (Error Code: " + response.getError().getErrorCode() + ")");
                
                new MaterialDialog(FirebaseUIActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                        .title(null, "Error Occurred")
                        .message(null, "An error occurred when attempting to sign into the application. \n\n" + //Corrected line
                                "Error details: " + response.getError().getMessage() + "\n\n" +
                                "(Error Code: " + response.getError().getErrorCode() + ")", null)
                        .cancelable(false)
                        .positiveButton(null, "Dismiss", materialDialog -> {
                            materialDialog.dismiss();
                            finish();
                            return null;
                        }).show();
            }else{
                finish();
            }
        }
    }

    public void signOut() {
        AuthUI.getInstance()
            .signOut(this)
            .addOnCompleteListener(new OnCompleteListener<Void>() {
                public void onComplete(@NonNull Task<Void> task) {
                    // ...
                }
            });
    }
}