<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingBottom="4dp"
    android:paddingLeft="@dimen/activity_horizontal_margin"
    android:paddingRight="@dimen/activity_horizontal_margin"
    android:paddingTop="4dp"
    tools:context=".QuizActivity">

    <RelativeLayout
        android:id="@+id/lastQuestionNeutral"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:layout_weight="1"
        android:gravity="center_horizontal">

        <RelativeLayout
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_gravity="center"
            android:background="@drawable/ic_circle"
            android:backgroundTint="#dddddd">

            <TextView
                android:id="@+id/lastQuestionNeutralText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text=""
                android:textSize="18sp"
                android:textColor="@android:color/white" />
        </RelativeLayout>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/lastQuestionOk"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:layout_weight="1"
        android:gravity="center_horizontal"
        android:visibility="gone" >

        <RelativeLayout
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_gravity="center"
            android:background="@drawable/ic_circle"
            android:backgroundTint="@color/md_green_400">

            <TextView
                android:id="@+id/lastQuestionOkText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="Ok"
                android:textSize="18dp"
                android:textColor="@android:color/white" />
        </RelativeLayout>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/lastQuestionWrong"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:layout_weight="1"
        android:gravity="center_horizontal"
        android:visibility="gone" >

        <RelativeLayout
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_gravity="center"
            android:background="@drawable/ic_circle"
            android:backgroundTint="@color/md_red_500">

            <TextView
                android:id="@+id/lastQuestionWrongText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="No"
                android:textSize="18sp"
                android:textColor="@android:color/white" />
        </RelativeLayout>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/consecutiveQuestions"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:layout_weight="1"
        android:gravity="center_horizontal" >

        <RelativeLayout
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_gravity="center"
            android:background="@drawable/ic_circle"
            android:backgroundTint="@color/md_yellow_800">

            <TextView
                android:id="@+id/consecutiveQuestionsText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="0"
                android:textSize="18sp"
                android:textColor="@android:color/white" />
        </RelativeLayout>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/record"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:layout_weight="1"
        android:gravity="center_horizontal" >

        <RelativeLayout
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_gravity="center"
            android:background="@drawable/ic_circle"
            android:backgroundTint="@color/md_blue_700">

            <TextView
                android:id="@+id/recordText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="0"
                android:textSize="18sp"
                android:textColor="@android:color/white" />
        </RelativeLayout>
    </RelativeLayout>

</LinearLayout>