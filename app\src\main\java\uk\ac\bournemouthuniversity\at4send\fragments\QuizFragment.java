package uk.ac.bournemouthuniversity.at4send.fragments;

import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.core.content.ContextCompat;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.BaseFragment;
import uk.ac.bournemouthuniversity.at4send.quiz.activities.StartQuizActivity;
import uk.ac.bournemouthuniversity.at4send.utils.ContrastUtils;
import uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils;

public class QuizFragment extends BaseFragment {

    public QuizFragment() {
        // Required empty public constructor
    }

    public static QuizFragment newInstance() {
        return new QuizFragment();
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View view = inflater.inflate(R.layout.fragment_quiz, container, false);
        Intent i = new Intent(requireContext(), StartQuizActivity.class);

        view.findViewById(R.id.switchesQuizAction).setOnClickListener(v -> {
            i.putExtra(StartQuizActivity.EXTRA_QUIZ_NAME, "Switches");
            startActivity(i);
        });

        view.findViewById(R.id.altKeyboardQuizAction).setOnClickListener(v -> {
            i.putExtra(StartQuizActivity.EXTRA_QUIZ_NAME, "Alternative Keyboards");
            startActivity(i);
        });

        view.findViewById(R.id.altMiceQuizAction).setOnClickListener(v -> {
            i.putExtra(StartQuizActivity.EXTRA_QUIZ_NAME, "Alternative Mice");
            startActivity(i);
        });

        view.findViewById(R.id.aacQuizAction).setOnClickListener(v -> {
            i.putExtra(StartQuizActivity.EXTRA_QUIZ_NAME, "Augmentative and Alternative Communication (AAC) Devices");
            startActivity(i);
        });

        view.findViewById(R.id.ecsQuizAction).setOnClickListener(v -> {
            i.putExtra(StartQuizActivity.EXTRA_QUIZ_NAME, "Environmental Control Systems");
            startActivity(i);
        });

        view.findViewById(R.id.eyeTrackingQuizAction).setOnClickListener(v -> {
            i.putExtra(StartQuizActivity.EXTRA_QUIZ_NAME, "Eye Tracking");
            startActivity(i);
        });

        view.findViewById(R.id.screenReadersQuizAction).setOnClickListener(v -> {
            i.putExtra(StartQuizActivity.EXTRA_QUIZ_NAME, "Screen Readers");
            startActivity(i);
        });

        view.findViewById(R.id.tabletQuizAction).setOnClickListener(v -> {
            i.putExtra(StartQuizActivity.EXTRA_QUIZ_NAME, "Tablet Computers");
            startActivity(i);
        });

        view.findViewById(R.id.androidQuizAction).setOnClickListener(v -> {
            i.putExtra(StartQuizActivity.EXTRA_QUIZ_NAME, "Android Accessibility Features");
            startActivity(i);
        });

        view.findViewById(R.id.curriculumQuizAction).setOnClickListener(v -> {
            i.putExtra(StartQuizActivity.EXTRA_QUIZ_NAME, "Curriculum Software");
            startActivity(i);
        });

        view.findViewById(R.id.dyslexiaQuizAction).setOnClickListener(v -> {
            i.putExtra(StartQuizActivity.EXTRA_QUIZ_NAME, "Dyslexia Software");
            startActivity(i);
        });

        view.findViewById(R.id.iOSQuizAction).setOnClickListener(v -> {
            i.putExtra(StartQuizActivity.EXTRA_QUIZ_NAME, "iOS Accessibility Features");
            startActivity(i);
        });

        view.findViewById(R.id.typingSkillsQuizAction).setOnClickListener(v -> {
            i.putExtra(StartQuizActivity.EXTRA_QUIZ_NAME, "Keyboard/Typing Skills Software");
            startActivity(i);
        });

        view.findViewById(R.id.visualQuizAction).setOnClickListener(v -> {
            i.putExtra(StartQuizActivity.EXTRA_QUIZ_NAME, "Visual Impairment Software");
            startActivity(i);
        });

        view.findViewById(R.id.windowsQuizAction).setOnClickListener(v -> {
            i.putExtra(StartQuizActivity.EXTRA_QUIZ_NAME, "Windows Accessibility Features");
            startActivity(i);
        });

        return view;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        // Apply appropriate styling based on contrast setting
        if (ContrastUtils.isHighContrastEnabled(requireContext())) {
            applyHighContrastToUI(view);
        } else {
            applyDefaultStylingToUI(view);
        }
    }

    private void applyHighContrastToUI(View rootView) {
        // Apply contrast to the entire view hierarchy
        ContrastUtils.applyContrastToViewHierarchy(rootView);
        
        // Find all ImageViews in the quiz cards and apply the text color as tint
        ViewGroup container = rootView.findViewById(R.id.quizCardsContainer);
        if (container != null) {
            applyContrastToIcons(container);
        }
    }

    private void applyDefaultStylingToUI(View rootView) {
        // Find all ImageViews in the quiz cards and apply blue tint
        ViewGroup container = rootView.findViewById(R.id.quizCardsContainer);
        if (container != null) {
            applyDefaultTintToIcons(container);
        }
    }

    private void applyContrastToIcons(ViewGroup viewGroup) {
        int textColor = TextSizeUtils.getTextColor(requireContext()).getDefaultColor();
        
        // Iterate through all children
        for (int i = 0; i < viewGroup.getChildCount(); i++) {
            View child = viewGroup.getChildAt(i);
            
            // If child is an ImageView, apply tint
            if (child instanceof ImageView) {
                ((ImageView) child).setColorFilter(textColor, android.graphics.PorterDuff.Mode.SRC_IN);
            }
            
            // If child is a ViewGroup, recursively apply to its children
            if (child instanceof ViewGroup) {
                applyContrastToIcons((ViewGroup) child);
            }
        }
    }

    private void applyDefaultTintToIcons(ViewGroup viewGroup) {
        // Get blue color from resources
        int blueColor = ContextCompat.getColor(requireContext(), R.color.colorAccent);
        
        // Iterate through all children
        for (int i = 0; i < viewGroup.getChildCount(); i++) {
            View child = viewGroup.getChildAt(i);
            
            // If child is an ImageView, apply blue tint
            if (child instanceof ImageView) {
                ((ImageView) child).setColorFilter(blueColor, android.graphics.PorterDuff.Mode.SRC_IN);
            }
            
            // If child is a ViewGroup, recursively apply to its children
            if (child instanceof ViewGroup) {
                applyDefaultTintToIcons((ViewGroup) child);
            }
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        // Reapply styling when returning to fragment
        if (getView() != null) {
            if (ContrastUtils.isHighContrastEnabled(requireContext())) {
                applyHighContrastToUI(getView());
            } else {
                applyDefaultStylingToUI(getView());
            }
        }
    }
}
