package uk.ac.bournemouthuniversity.at4send.extensions.helpers;

import android.content.Context;
import android.text.TextUtils;
import android.view.View;

import com.gabrielepmattia.materialfields.fields.Field;
import com.google.android.material.snackbar.Snackbar;

public class FieldUtils {

    public static boolean validateFields(Context context, View snackbarParentView, Field... fields){
        Snackbar sB = null;
        boolean validated = true;
        int validationErrors = 0;

        for (Field field : fields) {
            if (TextUtils.isEmpty(field.getValue())) {
                sB = Snackbar.make(snackbarParentView, "Please enter a " + field.getTitle(), Snackbar.LENGTH_LONG);
                field.setAlertState(true);
                validated = false;
                validationErrors++;
            } else {
                field.setAlertState(false);
            }
        }

        if(!validated){
            if(validationErrors > 1){
                sB = Snackbar.make(snackbarParentView, "This form is missing some information, please check the form and try again.", Snackbar.LENGTH_LONG);
            }

            SnackbarHelper.configSnackbar(context, sB);
            sB.show();
        }

        return validated;
    }

    public static boolean validateField(Context context, View snackbarParentView, Field field, boolean generateSnackBar){
        if(TextUtils.isEmpty(field.getValue())){
            if(generateSnackBar) {
                Snackbar sB = Snackbar.make(snackbarParentView, "Please enter a " + field.getTitle(), Snackbar.LENGTH_LONG);
                SnackbarHelper.configSnackbar(context, sB);
                sB.show();
            }
            field.setAlertState(true);
            return false;
        }else{
            field.setAlertState(false);
        }

        return true;
    }

    public static boolean validateField(Context context, View snackbarParentView, Field field){
        return validateField(context, snackbarParentView, field, false);
    }

    public static void enableFields(Field... fields){
        for(Field field : fields) {
            field.setDisabled(false);
        }
    }

    public static void disableFields(Field... fields){
        for(Field field : fields) {
            field.setDisabled(true);
        }
    }

    public static boolean hasChanged(Field field){
        return(!TextUtils.isEmpty(field.getValue()));
    }

    public static boolean hasChanged(Field... fields){
        boolean hasChanged = false;

        for(Field field : fields){
            if(field != null) {
                if (hasChanged(field)) {
                    hasChanged = true;
                }
            }
        }

        return hasChanged;
    }
}
