<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="fill_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical">

    <com.google.android.material.button.MaterialButton
        android:id="@+id/answer"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        tools:text="Hello"
        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
        android:textColor="@color/md_grey_900"
        android:backgroundTint="@color/md_grey_100"
        android:layout_marginHorizontal="8dp"
        android:layout_marginVertical="4dp"
        android:textAppearance="@android:style/TextAppearance.Small">
    </com.google.android.material.button.MaterialButton>

</LinearLayout>