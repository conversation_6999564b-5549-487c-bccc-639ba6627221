<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/root_layout"
    android:background="@color/md_grey_50"
    tools:context=".technologies.AssistiveTechnologyListActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:elevation="0dp">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            app:contentInsetStartWithNavigation="0dp"
            app:titleTextAppearance="@style/AT4SEND.AppTheme.TextAppearance.Title"
            app:title="Quiz"
            app:titleTextColor="@color/white"
            app:theme="@style/AT4SEND.BlueTheme.NoActionBar.Toolbar">

        </androidx.appcompat.widget.Toolbar>

    </com.google.android.material.appbar.AppBarLayout>

    <LinearLayout
        android:id="@+id/quiz_layout"
        app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <!-- Feedback -->
        <include layout="@layout/quiz_feedback"  android:layout_weight="3" />

        <!-- Question -->
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:paddingBottom="@dimen/activity_vertical_margin"
            android:paddingLeft="@dimen/activity_horizontal_margin"
            android:paddingRight="@dimen/activity_horizontal_margin"
            android:paddingTop="@dimen/activity_vertical_margin">

            <include
                layout="@layout/quiz_question"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </ScrollView>

        <!-- Answers -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardBackgroundColor="@color/md_grey_100"
            app:cardElevation="0dp"
            app:contentPadding="16dp"
            android:layout_marginHorizontal="16dp"
            android:layout_marginVertical="16dp"
            app:strokeWidth="1dp"
            app:strokeColor="@color/md_grey_200"
            app:cardCornerRadius="4dp">

            <ListView
                android:id="@+id/answer_list"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:focusable="true"
                android:gravity="center_horizontal" />

        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/returnHomeContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardBackgroundColor="@color/md_white_1000"
            app:cardCornerRadius="0dp"
            app:strokeWidth="1dp"
            app:strokeColor="@color/md_grey_200"
            app:cardElevation="0dp"
            android:layout_marginTop="20dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center_horizontal"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingTop="12dp"
                android:paddingBottom="12dp">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/returnHome"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:letterSpacing="0"
                    style="@style/Widget.MaterialComponents.Button.UnelevatedButton"
                    android:text="RETURN TO QUIZZES SCREEN"
                    app:backgroundTint="@color/md_orange_500"/>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>