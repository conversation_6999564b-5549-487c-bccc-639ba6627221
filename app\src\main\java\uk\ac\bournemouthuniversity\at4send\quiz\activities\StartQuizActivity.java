package uk.ac.bournemouthuniversity.at4send.quiz.activities;

import android.os.Bundle;

import androidx.appcompat.widget.Toolbar;

import java.util.ArrayList;
import java.util.List;

import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore;
import uk.ac.bournemouthuniversity.at4send.quiz.functions.QuestionDatabase;
import uk.ac.bournemouthuniversity.at4send.quiz.functions.TriviaQuiz;
import uk.ac.bournemouthuniversity.at4send.quiz.helpers.QuizHolder;
import uk.ac.bournemouthuniversity.at4send.quiz.helpers.QuizSession;
import uk.ac.bournemouthuniversity.at4send.quiz.helpers.SessionUtils;
import uk.ac.bournemouthuniversity.at4send.quiz.models.Question;

public class StartQuizActivity extends QuizActivity {
    public static final String EXTRA_QUIZ_NAME = "quizName";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        if(getIntent().getExtras() == null || getIntent().getExtras().getString(EXTRA_QUIZ_NAME, "none").equals("none")){
            throw new IllegalStateException("Invalid Quiz Supplied");
        }

        SessionUtils.setSession(this, new QuizSession(getIntent().getExtras().getString(EXTRA_QUIZ_NAME), false));

        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        if(getSupportActionBar() != null){
            getSupportActionBar().setTitle(getIntent().getExtras().getString(EXTRA_QUIZ_NAME));
        }

        QuestionDatabase qd = QuestionDatabase.getInstance();
        List<Question> questions = QuizDataStore.INSTANCE.prepareQuiz(getIntent().getExtras().getString(EXTRA_QUIZ_NAME));
        qd.prepare(questions);

        TriviaQuiz quiz = new TriviaQuiz(QuestionDatabase.getInstance(), new ArrayList<>());

        QuizHolder.setInstance(quiz);

        loadRecord();

        displayNextQuestion();
    }
}
