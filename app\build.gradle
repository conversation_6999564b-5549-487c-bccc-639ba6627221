apply plugin: 'com.android.application'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.firebase-perf'
apply plugin: 'kotlin-android'
apply plugin: 'com.google.firebase.crashlytics'

android {
    namespace "uk.ac.bournemouthuniversity.at4send"
    signingConfigs {
        release {
            storeFile file('../BU.jks')
            storePassword 'L@urel01'
            keyAlias 'at4send'
            keyPassword 'L@urel01'
        }
    }
    buildFeatures {
        viewBinding = true
        buildConfig = true
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = '11'
        freeCompilerArgs += [
            '-Xskip-metadata-version-check'
        ]
    }
    compileSdk 35  // Update from 35 as 34 is current stable
    defaultConfig {
        applicationId "uk.ac.bournemouthuniversity.at4send"
        minSdk 30
        targetSdk 34  // Match compileSdk
        versionCode 202
        versionName "2.0.2"
    }

    def versionPropsFile = file('version.properties')
    def versionBuild

    if (versionPropsFile.canRead()) {
        Properties versionProps = new Properties()
        versionProps.load(new FileInputStream(versionPropsFile))
        versionBuild = versionProps['VERSION_BUILD'].toInteger()
    } else {
        throw new FileNotFoundException("Could not read version.properties!")
    }

    ext.autoIncrementBuildNumber = {
        if (versionPropsFile.canRead()) {
            Properties versionProps = new Properties()
            versionProps.load(new FileInputStream(versionPropsFile))
            versionBuild = versionProps['VERSION_BUILD'].toInteger() + 1
            versionProps['VERSION_BUILD'] = versionBuild.toString()
            versionProps.store(versionPropsFile.newWriter(), null)
        } else {
            throw new FileNotFoundException("Could not read version.properties!")
        }
    }

    gradle.taskGraph.whenReady { taskGraph ->
        if (taskGraph.hasTask(assembleProductionDebug)) {
            autoIncrementBuildNumber()
        } else if (taskGraph.hasTask(assembleProductionRelease)) {
            autoIncrementBuildNumber()
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "java.util.Date", "BUILD_TIME", "new java.util.Date(" + System.currentTimeMillis() + "L)"
            buildConfigField "java.lang.Integer", "BUILD_VERSION", "" + versionBuild
            signingConfig signingConfigs.release
        }
        debug {
            debuggable true
            buildConfigField "java.util.Date", "BUILD_TIME", "new java.util.Date(" + System.currentTimeMillis() + "L)"
            buildConfigField "java.lang.Integer", "BUILD_VERSION", "" + versionBuild
        }
    }

    flavorDimensions "main"
    productFlavors {
        production {
            versionCode 202
            versionName '2.0.2'
        }
    }
}

repositories {
    // maven {
    //     url 'https://dl.bintray.com/fahmisdk6/maven'
    // }
}

gradle.projectsEvaluated {
    tasks.withType(JavaCompile) {
        options.compilerArgs << "-Xlint:deprecation"
    }
}

configurations {
    all*.exclude group: 'com.google.guava', module: 'listenablefuture'
}

dependencies {
    implementation "androidx.cardview:cardview:1.0.0"
    implementation "androidx.activity:activity:1.8.2"
    implementation "androidx.browser:browser:1.7.0"
    implementation "androidx.window:window:1.2.0"
    implementation "android.arch.lifecycle:extensions:1.1.1"
    implementation "androidx.viewpager2:viewpager2:1.0.0"
    implementation "androidx.fragment:fragment:1.6.2"
    implementation "androidx.fragment:fragment-ktx:1.6.2"
    // Local Libraries
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    implementation project(':materialfields')
    implementation project(':materialsearchview')

    // Firebase Addons
    implementation platform('com.google.firebase:firebase-bom:33.11.0')
    implementation 'com.google.firebase:firebase-perf'
    implementation 'com.google.firebase:firebase-crashlytics'
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-auth'
    implementation 'com.google.firebase:firebase-core:21.1.1'
    implementation 'com.google.firebase:firebase-database'
    implementation 'com.google.firebase:firebase-firestore'
    implementation 'com.google.firebase:firebase-messaging'

    // AndroidX
    implementation 'androidx.annotation:annotation:1.7.1'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.biometric:biometric:1.1.0'
    implementation 'androidx.browser:browser:1.7.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.preference:preference:1.2.1'
    implementation 'androidx.recyclerview:recyclerview:1.3.2'
    implementation 'androidx.security:security-crypto:1.1.0-alpha06'

    // Material Design
    implementation 'com.google.android.flexbox:flexbox:3.0.0'
    implementation 'com.google.android.material:material:1.12.0'    


    // Network Implementation
    implementation 'com.google.code.gson:gson:2.12.1'
    implementation 'com.squareup.retrofit2:retrofit:2.11.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.11.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'

    // Material Dialogs
    implementation 'com.afollestad.material-dialogs:core:3.3.0'
    implementation 'com.afollestad.material-dialogs:input:3.3.0'

    // Firebase UI
    implementation 'com.firebaseui:firebase-ui-auth:9.0.0'


    // Google Auth
    implementation 'com.google.android.gms:play-services-auth:21.3.0'

    // Glide Image Cache Library
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.8.7'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7'
    implementation 'androidx.navigation:navigation-fragment-ktx:2.9.0'
    implementation 'androidx.navigation:navigation-ui-ktx:2.9.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.16.0'

    // Butterknife Bindings
    // implementation 'com.jakewharton:butterknife:10.2.3'
    // annotationProcessor 'com.jakewharton:butterknife-compiler:10.2.3'

    // MikePenz Material UI
    implementation 'com.mikepenz:community-material-typeface:5.1.0@aar'
    implementation 'com.mikepenz:fontawesome-typeface:5.3.1.1@aar'
    implementation 'com.mikepenz:iconics-core:5.4.0'
    implementation 'com.mikepenz:iconics-views:5.4.0'
    implementation 'com.mikepenz:materialdrawer:6.1.2'

    // Progress Bar
    implementation 'me.zhanghai.android.materialprogressbar:library:1.6.1'

    // Material FAB Speed Dial
    implementation 'io.github.yavski:fab-speed-dial:1.0.6'

    // Google Extensions
    implementation 'com.hendraanggrian.material:collapsingtoolbarlayout-subtitle:1.5.0'

    // Avatar View
    // implementation('me.fahmisdk6.avatarview:avatarview:1.0@aar') {
    //     transitive = true
    // }

    // Dots Indicator
    implementation 'me.relex:circleindicator:2.1.6'

    // Permissions
    implementation 'com.karumi:dexter:6.2.3'

    // BackPort Calendar
    implementation 'com.jakewharton.threetenabp:threetenabp:1.4.9'

    // Test Implementations
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.2.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'
}






