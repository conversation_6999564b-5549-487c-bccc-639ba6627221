package uk.ac.bournemouthuniversity.at4send;

import android.app.Application;
import android.content.res.Configuration;
import uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils;
import uk.ac.bournemouthuniversity.at4send.utils.ButtonSizeUtils;
import com.jakewharton.threetenabp.AndroidThreeTen;

public class AT4SEND extends Application {

    @Override
    public void onCreate() {
        // Apply text size before super.onCreate
        float scale = TextSizeUtils.getTextSizeScale(this);
        Configuration configuration = getResources().getConfiguration();
        configuration.fontScale = scale;
        getResources().updateConfiguration(configuration, getResources().getDisplayMetrics());
        
        // Apply button size
        ButtonSizeUtils.applyButtonSize(this);
        
        super.onCreate();
        AndroidThreeTen.init(this);
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        TextSizeUtils.applyTextSize(this);
        ButtonSizeUtils.applyButtonSize(this);
    }
}
