// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext.kotlin_version = '1.9.22'

    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.2.1'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.google.gms:google-services:4.3.15'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.8.1'
        classpath 'com.google.firebase:perf-plugin:1.4.1'  // Performance Monitoring plugin


        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        google()
        //jcenter()
        mavenCentral()

        maven { url 'https://jitpack.io' }
        maven { url "https://dl.bintray.com/gabrielepmattia/maven" }
        maven { url 'https://maven.google.com/' }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}


