<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior"
    tools:context=".fragments.HomeFragment">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true"
        android:gravity="bottom"
        app:elevation="0dp">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            app:contentInsetStartWithNavigation="0dp"
            app:title="Training"
            app:titleTextColor="@color/white"
            app:titleTextAppearance="@style/AT4SEND.AppTheme.TextAppearance.Title"
            app:theme="@style/AT4SEND.BlueTheme.NoActionBar.Toolbar"
            app:layout_collapseMode="pin"
            />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

        <LinearLayout
            android:id="@+id/top_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            android:orientation="vertical">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/md_white_1000"
                app:cardCornerRadius="0dp"
                app:strokeWidth="1dp"
                app:strokeColor="@color/md_grey_200"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="center_vertical"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:paddingTop="8dp"
                        android:paddingBottom="8dp">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Select a category of Assistive Technology to learn more, including the benefits and limitations:"
                            android:id="@+id/training_instructions"
                            android:textSize="16sp"/>
                    </LinearLayout>
                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>


            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="@color/md_grey_50"
                android:layout_marginTop="16dp"/>



            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/md_white_1000"
                app:cardCornerRadius="0dp"
                app:strokeWidth="1dp"
                app:strokeColor="@color/md_grey_200"
                app:cardElevation="0dp">

                <com.google.android.material.tabs.TabLayout
                    android:id="@+id/tab_layout"
                    style="@style/Widget.MaterialComponents.TabLayout"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"/>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/view_pager"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintTop_toBottomOf="@id/top_layout"
            app:layout_constraintBottom_toTopOf="@id/returnHomeContainer" />

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/returnHomeContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardBackgroundColor="@color/md_white_1000"
            app:cardCornerRadius="0dp"
            app:strokeWidth="1dp"
            app:strokeColor="@color/md_grey_200"
            app:cardElevation="0dp"
            android:layout_marginTop="20dp"
            app:layout_constraintBottom_toBottomOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center_horizontal"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingTop="12dp"
                android:paddingBottom="12dp">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/returnHome"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:letterSpacing="0"
                    style="@style/Widget.MaterialComponents.Button.UnelevatedButton"
                    android:text="RETURN TO TRAINING SCREEN"
                    app:backgroundTint="@color/md_orange_500"/>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>
    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.coordinatorlayout.widget.CoordinatorLayout>