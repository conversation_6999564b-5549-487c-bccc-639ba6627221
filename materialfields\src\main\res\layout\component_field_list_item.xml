<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    android:id="@+id/itemViewI"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/field_list_item_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="72dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/field_list_item_content"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="0dp"
                tools:text="FieldShoppingListItem"
                android:textColor="@color/item_list_title_color"
                android:textSize="@dimen/item_list_title"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:id="@+id/field_list_item_subcontent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="0dp"
                tools:text="FieldShoppingListItem"
                android:textColor="@color/item_list_subtitle_color"
                android:textSize="@dimen/item_list_subtitle"
                app:layout_constraintStart_toStartOf="parent" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="72dp"
            android:layout_height="56dp"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/field_list_item_image"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/delete"
                android:tint="@color/grey600"
                app:srcCompat="@drawable/delete" />
        </LinearLayout>

        <View
            android:id="@+id/field_list_item_bottom_line_separator"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginBottom="0dp"
            android:background="@color/grey300"
            app:layout_constraintBottom_toBottomOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>