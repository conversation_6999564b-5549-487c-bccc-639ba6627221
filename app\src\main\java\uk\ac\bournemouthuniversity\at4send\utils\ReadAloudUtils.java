package uk.ac.bournemouthuniversity.at4send.utils;

import android.content.Context;
import android.graphics.Color;
import android.content.res.ColorStateList;
import android.text.Html;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.core.content.ContextCompat;

import uk.ac.bournemouthuniversity.at4send.R;

public class ReadAloudUtils {

    /**
     * Adds a read-aloud button next to a TextView
     * @param context The context
     * @param textView The TextView to add the button to
     * @return The created read-aloud button
     */
    public static ImageButton addReadAloudButton(Context context, TextView textView) {
        // Get the parent of the TextView
        ViewGroup parent = (ViewGroup) textView.getParent();
        if (parent == null) return null;
        
        // Create a horizontal LinearLayout to wrap the TextView and button
        LinearLayout wrapper = new LinearLayout(context);
        wrapper.setLayoutParams(new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT));
        wrapper.setOrientation(LinearLayout.HORIZONTAL);
        
        // Replace the TextView with the wrapper in the parent
        int index = parent.indexOfChild(textView);
        parent.removeView(textView);
        parent.addView(wrapper, index);
        
        // Add the TextView to the wrapper
        wrapper.addView(textView);
        textView.setLayoutParams(new LinearLayout.LayoutParams(
                0, LinearLayout.LayoutParams.WRAP_CONTENT, 1f));
        
        // Inflate the read-aloud button
        ImageButton readAloudButton = (ImageButton) LayoutInflater.from(context)
                .inflate(R.layout.view_read_aloud_button, wrapper, false);
        
        // Ensure button is visible and properly styled with blue icon
        readAloudButton.setVisibility(View.VISIBLE);
        readAloudButton.setImageResource(R.drawable.ic_volume_up);
        int materialBlue = Color.parseColor("#000000");
        
        // Apply color based on high contrast setting
        int buttonColor = ContrastUtils.isHighContrastEnabled(context) 
            ? TextSizeUtils.getTextColor(context).getDefaultColor()
            : materialBlue;
            
        readAloudButton.setImageTintList(ColorStateList.valueOf(buttonColor));
        readAloudButton.setColorFilter(buttonColor);
        
        // Add the button to the wrapper
        wrapper.addView(readAloudButton);
        
        // Apply button size
        float buttonScale = TextSizeUtils.getButtonSizeScale(context);
        readAloudButton.setScaleX(buttonScale);
        readAloudButton.setScaleY(buttonScale);
        
        // Force layout update
        wrapper.requestLayout();
        readAloudButton.requestLayout();
        readAloudButton.post(() -> {
            readAloudButton.setVisibility(View.VISIBLE);
            readAloudButton.setImageTintList(ColorStateList.valueOf(buttonColor));
            readAloudButton.setColorFilter(buttonColor);
            wrapper.invalidate();
        });
        
        // Set up the button click listener
        final TextToSpeechManager ttsManager = TextToSpeechManager.getInstance(context);
        readAloudButton.setOnClickListener(v -> {
            CharSequence text = textView.getText();
            String textToRead = text instanceof Html ? Html.fromHtml(text.toString(), Html.FROM_HTML_MODE_COMPACT).toString() : text.toString();
            
            if (ttsManager.isSpeaking()) {
                ttsManager.stop();
                readAloudButton.setImageResource(R.drawable.ic_volume_up);
                readAloudButton.setImageTintList(ColorStateList.valueOf(buttonColor));
                readAloudButton.setColorFilter(buttonColor);
                readAloudButton.setContentDescription(context.getString(R.string.read_aloud_button_description));
            } else {
                // Use the new method that takes a TextView for word tracking
                ttsManager.speak(textToRead, textView);
                readAloudButton.setImageResource(R.drawable.ic_volume_off);
                readAloudButton.setImageTintList(ColorStateList.valueOf(buttonColor));
                readAloudButton.setColorFilter(buttonColor);
                readAloudButton.setContentDescription(context.getString(R.string.read_aloud_button_stop_description));
            }
        });
        
        return readAloudButton;
    }
    
    /**
     * Adds read-aloud buttons to all TextViews in a ViewGroup
     * @param context The context
     * @param viewGroup The ViewGroup to search for TextViews
     */
    public static void addReadAloudButtonsToViewGroup(Context context, ViewGroup viewGroup) {
        for (int i = 0; i < viewGroup.getChildCount(); i++) {
            View child = viewGroup.getChildAt(i);
            if (child instanceof TextView && ((TextView) child).getText().length() > 0) {
                addReadAloudButton(context, (TextView) child);
            } else if (child instanceof ViewGroup) {
                addReadAloudButtonsToViewGroup(context, (ViewGroup) child);
            }
        }
    }
}
