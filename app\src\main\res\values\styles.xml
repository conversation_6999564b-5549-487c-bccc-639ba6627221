<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    
    <!-- Base application theme -->
    <style name="AT4SEND.AppTheme" parent="Theme.MaterialComponents.Light.DarkActionBar.Bridge">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="md_color_button_text">@color/colorAccent</item>
        <item name="android:fontFamily">@font/asap</item>
        <item name="fontFamily">@font/asap</item>
        <item name="android:windowLightStatusBar" tools:targetApi="23">true</item>
        
        <!-- Accessibility items -->
        <item name="scaledTextSize">@dimen/default_text_size</item>
        <item name="scaledTitleSize">@dimen/default_title_size</item>
    </style>

    <!-- Text size styles -->
    <style name="AT4SEND.TextAppearance.Title" parent="TextAppearance.MaterialComponents.Headline5">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>

    <style name="AT4SEND.TextAppearance.Body" parent="TextAppearance.MaterialComponents.Body1">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>

    <!-- Add this style for scaled text -->
    <style name="AT4SEND.AppTheme.TextSize.Title.Scaled" parent="AT4SEND.TextAppearance.Title">
        <item name="android:textSize">?attr/scaledTitleSize</item>
    </style>

    <!-- Base Application Theme - No ActionBar -->
    <style name="AT4SEND.AppTheme.NoActionBar" parent="AT4SEND.AppTheme">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="AT4SEND.AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="AT4SEND.AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <!-- Blue Application Theme -->
    <style name="AT4SEND.BlueTheme" parent="Theme.MaterialComponents.Light.DarkActionBar.Bridge">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/md_blue_500</item>
        <item name="colorPrimaryDark">@color/md_blue_600</item>
        <item name="colorAccent">@color/colorAccent</item>

        <item name="md_color_button_text">@color/md_blue_500</item>

        <item name="android:fontFamily" tools:targetApi="o">@font/asap</item>
        <item name="fontFamily">@font/asap</item>
    </style>

    <!-- Blue Application Theme - No ActionBar -->
    <style name="AT4SEND.BlueTheme.NoActionBar" parent="AT4SEND.BlueTheme">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <!-- Blue Application Theme - Toolbar -->
    <style name="AT4SEND.BlueTheme.NoActionBar.Toolbar" parent="AT4SEND.BlueTheme.NoActionBar">
        <item name="android:textColorPrimary">@android:color/white</item>
        <item name="android:textColorSecondary">@android:color/white</item>
        <item name="actionMenuTextColor">@android:color/white</item>
    </style>

    <!-- Splash Screen Background -->
    <style name="AT4SEND.SplashTheme" parent="AT4SEND.AppTheme.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background</item>
    </style>

    <!-- TextAppearance -->
    <!-- Title -->
    <style name="AT4SEND.AppTheme.TextAppearance.Title" parent="TextAppearance.AppCompat.Title">
        <item name="android:fontFamily" tools:targetApi="o">@font/asap_bold</item>
        <item name="fontFamily">@font/asap_bold</item>
    </style>

    <!-- Collapsed Title -->
    <style name="AT4SEND.AppTheme.TextAppearance.Title.Collapsed" parent="TextAppearance.AppCompat.Title">
        <item name="android:fontFamily" tools:targetApi="o">@font/asap_medium</item>
        <item name="fontFamily">@font/asap_medium</item>
    </style>

    <!-- Expanded Title -->
    <style name="AT4SEND.AppTheme.TextAppearance.Title.Expanded" parent="AT4SEND.AppTheme.TextAppearance.Title.Collapsed">
        <item name="android:textSize">40sp</item>
    </style>

    <style name="AT4SEND.AppTheme.TextAppearance.Title.Expanded.Technology" parent="AT4SEND.AppTheme.TextAppearance.Title.Collapsed">
        <item name="android:textSize">22sp</item>
    </style>

    <!-- Subtitle -->
    <style name="AT4SEND.AppTheme.TextAppearance.Subtitle" parent="@style/TextAppearance.AppCompat.Body2">
        <item name="android:fontFamily" tools:targetApi="o">@font/asap_semibold</item>
        <item name="fontFamily">@font/asap_semibold</item>
    </style>

    <!-- Home Screen Tab -->
    <style name="AT4SEND.AppTheme.TextAppearance.Tabs" parent="TextAppearance.AppCompat.Button">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">normal</item>
        <item name="textAllCaps">false</item>

        <item name="android:fontFamily" tools:targetApi="o">@font/asap</item>
        <item name="fontFamily">@font/asap</item>
    </style>


    <!-- Circle Image -->
    <style name="AT4SEND.ShapeableImageView.Circle" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>

    <!-- Quiz Card Styles -->
    <style name="QuizCard" parent="Widget.MaterialComponents.CardView">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
        <item name="cardCornerRadius">8dp</item>
        <item name="cardElevation">2dp</item>
        <item name="layout_flexBasisPercent">45%</item>
    </style>

    <style name="QuizCardContent">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:orientation">vertical</item>
        <item name="android:padding">16dp</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="QuizCardIcon">
        <item name="android:layout_width">48dp</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:layout_marginBottom">8dp</item>
        <item name="android:contentDescription">Quiz category icon</item>
    </style>

    <style name="QuizCardTextContainer">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:orientation">vertical</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="QuizCardTitle" parent="TextAppearance.MaterialComponents.Subtitle1">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center</item>
        <item name="android:textAlignment">center</item>
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
</resources>
