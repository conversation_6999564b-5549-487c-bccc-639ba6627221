<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.flexbox.FlexboxLayout
        android:id="@+id/quizCardsContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:flexWrap="wrap"
        app:alignItems="flex_start"
        app:alignContent="flex_start"
        app:justifyContent="space_between">

        <!-- AAC Devices Quiz -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/aacQuizAction"
            style="@style/QuizCard">

            <LinearLayout style="@style/QuizCardContent">
                <androidx.appcompat.widget.AppCompatImageView
                    style="@style/QuizCardIcon"
                    app:srcCompat="@drawable/ic_communication"/>

                <LinearLayout style="@style/QuizCardTextContainer">
                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/QuizCardTitle"
                        android:id="@+id/quizCardTitle"
                        android:text="AAC Devices"/>
                </LinearLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Alternative Keyboards Quiz -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/altKeyboardQuizAction"
            style="@style/QuizCard">

            <LinearLayout style="@style/QuizCardContent">
                <androidx.appcompat.widget.AppCompatImageView
                    style="@style/QuizCardIcon"
                    app:srcCompat="@drawable/ic_keyboard"/>

                <LinearLayout style="@style/QuizCardTextContainer">
                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/QuizCardTitle"
                        android:text="Alternative Keyboards"/>
                </LinearLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Alternative Mice Quiz -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/altMiceQuizAction"
            style="@style/QuizCard">

            <LinearLayout style="@style/QuizCardContent">
                <androidx.appcompat.widget.AppCompatImageView
                    style="@style/QuizCardIcon"
                    app:srcCompat="@drawable/ic_mouse"/>

                <LinearLayout style="@style/QuizCardTextContainer">
                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/QuizCardTitle"
                        android:text="Alternative Mice"/>
                </LinearLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Android Accessibility Quiz -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/androidQuizAction"
            style="@style/QuizCard">

            <LinearLayout style="@style/QuizCardContent">
                <androidx.appcompat.widget.AppCompatImageView
                    style="@style/QuizCardIcon"
                    app:srcCompat="@drawable/ic_android"/>

                <LinearLayout style="@style/QuizCardTextContainer">
                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/QuizCardTitle"
                        android:text="Android Accessibility"/>
                </LinearLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Curriculum Software Quiz -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/curriculumQuizAction"
            style="@style/QuizCard">

            <LinearLayout style="@style/QuizCardContent">
                <androidx.appcompat.widget.AppCompatImageView
                    style="@style/QuizCardIcon"
                    app:srcCompat="@drawable/ic_education"/>

                <LinearLayout style="@style/QuizCardTextContainer">
                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/QuizCardTitle"
                        android:text="Curriculum Software"/>
                </LinearLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Dyslexia Software Quiz -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/dyslexiaQuizAction"
            style="@style/QuizCard">

            <LinearLayout style="@style/QuizCardContent">
                <androidx.appcompat.widget.AppCompatImageView
                    style="@style/QuizCardIcon"
                    app:srcCompat="@drawable/ic_text_format"/>

                <LinearLayout style="@style/QuizCardTextContainer">
                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/QuizCardTitle"
                        android:text="Dyslexia Software"/>
                </LinearLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Environmental Control Systems Quiz -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/ecsQuizAction"
            style="@style/QuizCard">

            <LinearLayout style="@style/QuizCardContent">
                <androidx.appcompat.widget.AppCompatImageView
                    style="@style/QuizCardIcon"
                    app:srcCompat="@drawable/ic_home_automation"/>

                <LinearLayout style="@style/QuizCardTextContainer">
                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/QuizCardTitle"
                        android:text="Environmental Control Systems"/>
                </LinearLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Eye Tracking Quiz -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/eyeTrackingQuizAction"
            style="@style/QuizCard">

            <LinearLayout style="@style/QuizCardContent">
                <androidx.appcompat.widget.AppCompatImageView
                    style="@style/QuizCardIcon"
                    app:srcCompat="@drawable/ic_eye"/>

                <LinearLayout style="@style/QuizCardTextContainer">
                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/QuizCardTitle"
                        android:text="Eye Tracking"/>
                </LinearLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- iOS Accessibility Quiz -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/iOSQuizAction"
            style="@style/QuizCard">

            <LinearLayout style="@style/QuizCardContent">
                <androidx.appcompat.widget.AppCompatImageView
                    style="@style/QuizCardIcon"
                    app:srcCompat="@drawable/ic_apple"/>

                <LinearLayout style="@style/QuizCardTextContainer">
                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/QuizCardTitle"
                        android:text="iOS Accessibility"/>
                </LinearLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Screen Readers Quiz -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/screenReadersQuizAction"
            style="@style/QuizCard">

            <LinearLayout style="@style/QuizCardContent">
                <androidx.appcompat.widget.AppCompatImageView
                    style="@style/QuizCardIcon"
                    app:srcCompat="@drawable/ic_screen_reader"/>

                <LinearLayout style="@style/QuizCardTextContainer">
                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/QuizCardTitle"
                        android:text="Screen Readers"/>
                </LinearLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Switches Quiz -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/switchesQuizAction"
            style="@style/QuizCard">

            <LinearLayout style="@style/QuizCardContent">
                <androidx.appcompat.widget.AppCompatImageView
                    style="@style/QuizCardIcon"
                    app:srcCompat="@drawable/ic_switch"/>

                <LinearLayout style="@style/QuizCardTextContainer">
                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/QuizCardTitle"
                        android:text="Switches"/>
                </LinearLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Tablet Computers Quiz -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/tabletQuizAction"
            style="@style/QuizCard">

            <LinearLayout style="@style/QuizCardContent">
                <androidx.appcompat.widget.AppCompatImageView
                    style="@style/QuizCardIcon"
                    app:srcCompat="@drawable/ic_tablet"/>

                <LinearLayout style="@style/QuizCardTextContainer">
                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/QuizCardTitle"
                        android:text="Tablets"/>
                </LinearLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Typing Skills Quiz -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/typingSkillsQuizAction"
            style="@style/QuizCard">

            <LinearLayout style="@style/QuizCardContent">
                <androidx.appcompat.widget.AppCompatImageView
                    style="@style/QuizCardIcon"
                    app:srcCompat="@drawable/ic_typing"/>

                <LinearLayout style="@style/QuizCardTextContainer">
                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/QuizCardTitle"
                        android:text="Typing Skills Software"/>
                </LinearLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Visual Impairment Software Quiz -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/visualQuizAction"
            style="@style/QuizCard">

            <LinearLayout style="@style/QuizCardContent">
                <androidx.appcompat.widget.AppCompatImageView
                    style="@style/QuizCardIcon"
                    app:srcCompat="@drawable/ic_visibility"/>

                <LinearLayout style="@style/QuizCardTextContainer">
                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/QuizCardTitle"
                        android:text="Visual Impairment Software"/>
                </LinearLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Windows Accessibility Quiz -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/windowsQuizAction"
            style="@style/QuizCard">

            <LinearLayout style="@style/QuizCardContent">
                <androidx.appcompat.widget.AppCompatImageView
                    style="@style/QuizCardIcon"
                    app:srcCompat="@drawable/ic_windows"/>

                <LinearLayout style="@style/QuizCardTextContainer">
                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/QuizCardTitle"
                        android:text="Windows Accessibility"/>
                </LinearLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

    </com.google.android.flexbox.FlexboxLayout>

</androidx.core.widget.NestedScrollView>
