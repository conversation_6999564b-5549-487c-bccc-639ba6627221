<?xml version="1.0" encoding="utf-8"?><!--suppress ALL -->
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/field_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:clickable="true"
        android:focusable="true"
        android:foreground="?android:attr/selectableItemBackground">

        <LinearLayout
            android:layout_width="72dp"
            android:layout_height="72dp"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <CheckBox
                android:id="@+id/field_check"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@null"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="72dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="16dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/field_alert_image"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/field_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="0dp"
                android:maxLines="1"
                android:text="FieldCheckBox"
                android:textColor="@color/item_list_title_color"
                android:textSize="@dimen/item_list_title"
                app:layout_constraintStart_toStartOf="parent" />

            <TextView
                android:id="@+id/field_subtitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="0dp"
                android:text="Description"
                android:textColor="@color/item_list_subtitle_color"
                android:textSize="@dimen/item_list_subtitle"
                app:layout_constraintStart_toStartOf="parent" />

        </LinearLayout>

        <ImageView
            android:id="@+id/field_alert_image"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginEnd="16dp"
            android:src="@drawable/alert_circle"
            android:tint="@color/red500"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/alert_circle" />

        <View
            android:id="@+id/field_bottom_line_separator"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginBottom="0dp"
            android:background="@color/grey300"
            app:layout_constraintBottom_toBottomOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</merge>