<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/md_grey_50"
    tools:context=".technologies.AssistiveTechnologyListActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="180dp"
        android:fitsSystemWindows="true"
        android:gravity="bottom"
        app:elevation="0dp"
        android:orientation="vertical">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/toolbar_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fitsSystemWindows="true"
            app:contentScrim="?attr/colorPrimary"
            app:layout_scrollFlags="scroll|exitUntilCollapsed|snap"
            app:expandedTitleTextAppearance="@style/AT4SEND.AppTheme.TextAppearance.Title"
            app:collapsedTitleTextAppearance="@style/AT4SEND.AppTheme.TextAppearance.Title"
            app:toolbarId="@id/toolbar"
            app:collapsedTitleGravity="center|start"
            app:expandedTitleGravity="bottom|start"
            app:expandedTitleMarginStart="16dp"
            app:expandedTitleMarginBottom="22dp"
            app:title="Assistive Technology Name"
            app:collapsedTitleTextColor="@color/white"
            app:expandedTitleTextColor="@color/white"
            app:theme="@style/AT4SEND.BlueTheme.NoActionBar.Toolbar">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="bottom"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingBottom="22dp">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/toolbar_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textAppearance="@style/AT4SEND.AppTheme.TextAppearance.Title"
                    android:textColor="@color/white"
                    tools:text="Assistive Technology Name"/>

            </LinearLayout>

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                app:contentInsetStartWithNavigation="0dp"
                app:titleTextAppearance="@style/AT4SEND.AppTheme.TextAppearance.Title"
                app:theme="@style/AT4SEND.BlueTheme.NoActionBar.Toolbar"
                app:layout_collapseMode="pin"
                />

        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tab_layout"
            style="@style/Widget.MaterialComponents.TabLayout.Colored"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            app:layout_constraintTop_toTopOf="parent"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/md_blue_300"
            app:layout_constraintTop_toTopOf="parent"/>

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/view_pager"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintTop_toBottomOf="@id/tab_layout"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>