package uk.ac.bournemouthuniversity.at4send.training.fragments;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.adapters.training.AssistiveTechnologyTrainingAdapter;
import uk.ac.bournemouthuniversity.at4send.data.training.DataStore;
import uk.ac.bournemouthuniversity.at4send.models.training.AssistiveTechnologyTraining;
import uk.ac.bournemouthuniversity.at4send.BaseFragment;
import uk.ac.bournemouthuniversity.at4send.utils.ContrastUtils;
import uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils;

public class AssistiveTechnologiesSoftwareFragment extends BaseFragment {

    private final List<AssistiveTechnologyTraining> softwareTechnologies = DataStore.INSTANCE.getASSISTIVE_TECHNOLOGY_TRAINING_SOFTWARE();

    public AssistiveTechnologiesSoftwareFragment() {
        // Required empty public constructor
    }

    public static AssistiveTechnologiesSoftwareFragment newInstance() {
        return new AssistiveTechnologiesSoftwareFragment();
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View view = inflater.inflate(R.layout.fragment_assistive_technologies_software, container, false);

        RecyclerView softwareRecyclerView = view.findViewById(R.id.softwareTrainingTechnologies);
        softwareRecyclerView.setLayoutManager(new LinearLayoutManager(requireContext(), RecyclerView.VERTICAL, false));
        
        // Create and set adapter
        AssistiveTechnologyTrainingAdapter adapter = new AssistiveTechnologyTrainingAdapter(requireContext(), softwareTechnologies);
        softwareRecyclerView.setAdapter(adapter);
        
        // Apply high contrast if enabled
        if (ContrastUtils.isHighContrastEnabled(requireContext())) {
            applyHighContrastToRecyclerView(softwareRecyclerView);
        }
        
        return view;
    }

    @Override
    public void onResume() {
        super.onResume();
        // Reapply high contrast when returning to fragment
        if (getView() != null && ContrastUtils.isHighContrastEnabled(requireContext())) {
            RecyclerView softwareRecyclerView = getView().findViewById(R.id.softwareTrainingTechnologies);
            if (softwareRecyclerView != null) {
                applyHighContrastToRecyclerView(softwareRecyclerView);
            }
        }
    }

    private void applyHighContrastToRecyclerView(RecyclerView recyclerView) {
        // Apply contrast to the RecyclerView itself
        ContrastUtils.applyContrastToViewHierarchy(recyclerView);
        
        // Apply contrast to each visible item in the RecyclerView
        for (int i = 0; i < recyclerView.getChildCount(); i++) {
            View itemView = recyclerView.getChildAt(i);
            if (itemView != null) {
                // Apply contrast to the item view
                ContrastUtils.applyContrastToViewHierarchy(itemView);
                
                // Specifically target the icon
                ImageView iconView = itemView.findViewById(R.id.technologyIcon);
                if (iconView != null) {
                    iconView.setColorFilter(TextSizeUtils.getTextColor(requireContext()).getDefaultColor());
                }
                
                // Apply to text views
                TextView nameView = itemView.findViewById(R.id.technologyName);
                if (nameView != null) {
                    nameView.setTextColor(TextSizeUtils.getTextColor(requireContext()).getDefaultColor());
                }
            }
        }
        
        // Force adapter to refresh all items
        recyclerView.getAdapter().notifyDataSetChanged();
    }
}
