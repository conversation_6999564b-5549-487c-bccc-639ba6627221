<?xml version="1.0" encoding="utf-8"?>
<resources>

    <declare-styleable name="AT4SENDAvatarView">
        <attr format="string|reference" name="avatarInitials" />
        <attr format="dimension|reference" name="avatarCornerRadius" />
        <attr format="color|reference" name="avatarBackgroundColor" />
        <attr format="color|reference" name="avatarTextColor" />
        <attr format="dimension|reference" name="avatarTextSize" />
    </declare-styleable>

    <declare-styleable name="IndicatorSeekBar">
        <attr name="isb_max" format="float" />
        <!-- the max value of seekBar to seek, default 100-->

        <attr name="isb_min" format="float" />
        <!-- the min value of seekBar to seek, default 0 -->

        <attr name="isb_progress" format="float" />
        <!-- the current progress value of seekBar, default 0-->

        <attr name="isb_progress_value_float" format="boolean" />
        <!--set
        the value of seekBar to float type, default false-->

        <attr name="isb_seek_smoothly" format="boolean" />
        <!--true
        to seek smoothly when has ticks, default false-->

        <attr name="isb_r2l" format="boolean" />
        <!--compat
        app local change,like arabic local, default false-->

        <attr name="isb_ticks_count" format="integer" />
        <!--seekBar's
        ticks count, default zero-->

        <attr name="isb_user_seekable" format="boolean" />
        <!--prevent
        user from seeking,only can be change thumb location by setProgress(), default false-->

        <attr name="isb_clear_default_padding" format="boolean" />
        <!-- set seekBar's leftPadding&rightPadding to zero, default false, default padding is
        16dp-->

        <attr name="isb_only_thumb_draggable" format="boolean" />
        <!--user
        change the thumb's location by touching thumb/touching track,true for touching track to
        seek. false for touching thumb; default false-->

        <attr name="isb_show_indicator">
            <!-- the type of indicator, default rectangle_rounded_corner/0.-->
            <enum name="none" value="0" />
            <enum name="circular_bubble" value="1" />
            <enum name="rounded_rectangle" value="2" />
            <enum name="rectangle" value="3" />
            <enum name="custom" value="4" />
            <!--choose
            custom type that you can set the custom indicator layout you want.-->
        </attr>

        <attr name="isb_indicator_color" format="color|reference" />
        <!-- indicator's color, default #FF4081-->

        <attr name="isb_indicator_text_color" format="color|reference" />
        <!-- indicator's text color, default #FF4081 -->

        <attr name="isb_indicator_text_size" format="dimension|reference" />
        <!-- indicator's text size, default 14sp-->

        <attr name="isb_indicator_content_layout" format="reference" />
        <!-- when you set indicator type to custom , you can set ths indicator layout you want-->

        <attr name="isb_indicator_top_content_layout" format="reference" />
        <!--set
        the indicator's top view you want, not impact arrow , effect on indicator type : rectangle
        or rectangle_rounded_corner-->

        <attr name="isb_track_background_size" format="dimension|reference" />
        <!-- set indicatorSeekBar's track background bar size, default 2dp-->

        <attr name="isb_track_background_color" format="color|reference" />
        <!-- set indicatorSeekBar's track background bar color, default #D7D7D7-->

        <attr name="isb_track_progress_size" format="dimension|reference" />
        <!-- set indicatorSeekBar's track progress bar size, default 2dp-->

        <attr name="isb_track_progress_color" format="color|reference" />
        <!-- set indicatorSeekBar's track progress bar color, default #FF4081-->

        <attr name="isb_track_rounded_corners" format="boolean" />
        <!-- set the track's both ends' shape to round-->

        <attr name="isb_thumb_text_color" format="color|reference" />
        <!--set
        thumb's color, default #FF4081-->

        <attr name="isb_show_thumb_text" format="boolean" />
        <!--show
        thumb text or not, default false-->

        <attr name="isb_thumb_size" format="dimension|reference" />
        <!--set
        thumb's size, default 14dp, thumb size will be limited in 30dp-->

        <attr name="isb_thumb_color" format="color|reference" />
        <!--set
        thumb's color, default #FF4081-->

        <attr name="isb_thumb_drawable" format="reference" />
        <!--set
        custom thumb's drawable you want,thumb size will be limited in 30dp, if drawable less than
        30dp ,will show in intrinsic size -->

        <attr name="isb_thumb_adjust_auto" format="boolean" />
        <!--set
        the thumb move to the closed tick after touched up, default true-->

        <attr name="isb_tick_marks_color" format="color|reference" />
        <!--set
        tick's color, default #FF4081-->

        <attr name="isb_tick_marks_size" format="dimension|reference" />
        <!--set
        the tick width, default 10dp,custom drawable will be limited in 30dp, if less than 30dp
        ,will show in intrinsic size-->

        <attr name="isb_tick_marks_drawable" format="reference" />
        <!--set
        custom tick's drawable you want, custom drawable will be limited in 30dp, if less than 30dp
        ,will show in intrinsic size-->

        <attr name="isb_tick_marks_ends_hide" format="boolean" />
        <!--hide
        2 ticks on the seekBar's both ends, default false-->

        <attr name="isb_tick_marks_swept_hide" format="boolean" />
        <!--hide
        the ticks on the seekBar's thumb left, default false-->

        <attr name="isb_show_tick_marks_type">
            <!--select
            the tick shape type, default not show： NONE/0-->

            <enum name="none" value="0" />
            <enum name="oval" value="1" />
            <enum name="square" value="2" />
            <enum name="divider" value="3" />
            <!--show
            tickMarks shape as vertical line , line'size is 1 dp.-->
        </attr>

        <attr name="isb_show_tick_texts" format="boolean" />
        <!--show
        the text below tick or not, default false-->

        <attr name="isb_tick_texts_color" format="reference|color" />
        <!--set
        texts' color, default #FF4081-->

        <attr name="isb_tick_texts_size" format="dimension|reference" />
        <!--set
        the text size of tick below text, default 13sp-->

        <attr name="isb_tick_texts_array" format="reference" />
        <!--set
        the custom texts below tick to replace default progress text, default null-->

        <attr name="isb_tick_texts_typeface">
            <!--select
            the typeface for tick texts/thumb text, default normal-->
            <enum name="normal" value="0" />
            <enum name="monospace" value="1" />
            <enum name="sans" value="2" />
            <enum name="serif" value="3" />
        </attr>
        <attr name="scaledTextSize" format="dimension" />
        <attr name="scaledTitleSize" format="dimension" />
        <attr name="max" format="integer" />
    </declare-styleable>

</resources>
