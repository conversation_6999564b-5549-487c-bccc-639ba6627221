package uk.ac.bournemouthuniversity.at4send.technologies.training.fragments;

import android.os.Build;
import android.os.Bundle;
import android.text.Editable;
import android.text.Html;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;

import uk.ac.bournemouthuniversity.at4send.BaseFragment;

import org.xml.sax.XMLReader;

import uk.ac.bournemouthuniversity.at4send.databinding.FragmentViewAssistiveTechnologyTrainingSectionBinding;
import uk.ac.bournemouthuniversity.at4send.utils.ReadAloudUtils;
import uk.ac.bournemouthuniversity.at4send.utils.TextToSpeechManager;

public class AssistiveTechnologyDescriptionFragment extends BaseFragment {

    private static final String ARG_SECTION_TEXT = "sectionText";
    private FragmentViewAssistiveTechnologyTrainingSectionBinding binding;
    private String argSectionText;

    public AssistiveTechnologyDescriptionFragment() {
        // Required empty public constructor
    }

    public static AssistiveTechnologyDescriptionFragment newInstance(String sectionText) {
        AssistiveTechnologyDescriptionFragment fragment = new AssistiveTechnologyDescriptionFragment();
        Bundle args = new Bundle();
        args.putString(ARG_SECTION_TEXT, sectionText);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            argSectionText = getArguments().getString(ARG_SECTION_TEXT);
        }
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                           Bundle savedInstanceState) {
        binding = FragmentViewAssistiveTechnologyTrainingSectionBinding.inflate(inflater, container, false);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            binding.detail.setText(Html.fromHtml(argSectionText, Html.FROM_HTML_MODE_LEGACY, null, new UlTagHandler()));
        } else {
            @SuppressWarnings("deprecation")
            CharSequence sequence = Html.fromHtml(argSectionText, null, new UlTagHandler());
            binding.detail.setText(sequence);
        }
        
        // Add read-aloud button to the description text
        ReadAloudUtils.addReadAloudButton(requireContext(), binding.detail);
        
        return binding.getRoot();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        // Stop TTS when fragment is destroyed
        TextToSpeechManager.getInstance(requireContext()).stop();
    }

    public class UlTagHandler implements Html.TagHandler {
        @Override
        public void handleTag(boolean opening, String tag, Editable output, XMLReader xmlReader) {
            if(tag.equals("ul") && !opening) output.append("\n");
            if(tag.equals("li") && opening) output.append("\n•");
        }
    }
}
