package uk.ac.bournemouthuniversity.at4send.training;

import android.os.Bundle;
import android.view.MenuItem;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.BaseActivity;
import uk.ac.bournemouthuniversity.at4send.adapters.training.AssistiveTechnologyVideoAdapter;
import uk.ac.bournemouthuniversity.at4send.data.training.DataStore;
import uk.ac.bournemouthuniversity.at4send.models.training.AssistiveTechnologyVideo;
import uk.ac.bournemouthuniversity.at4send.utils.ReadAloudUtils;
import uk.ac.bournemouthuniversity.at4send.utils.TextToSpeechManager;

public class VideoActivity extends BaseActivity {

    private List<AssistiveTechnologyVideo> videoList = DataStore.INSTANCE.getASSISTIVE_TECHNOLOGY_VIDEOS();
    private AssistiveTechnologyVideoAdapter mAdapter;
    private RecyclerView mMainRecyclerView; 

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_training_video);

        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        ActionBar ab = getSupportActionBar();

        // Add read-aloud button to instructions text
        TextView instructionsText = findViewById(R.id.listExplanatoryTextHeading);
        if (instructionsText != null) {
            ReadAloudUtils.addReadAloudButton(this, instructionsText);
        }

        if(ab != null){
            ab.setDisplayHomeAsUpEnabled(true);
        }

        findViewById(R.id.returnHome).setOnClickListener(v -> 
            // getOnBackPressedDispatcher().onBackPressed()
            finish()
        );

        mAdapter = new AssistiveTechnologyVideoAdapter(this, videoList);

        mMainRecyclerView = findViewById(R.id.mainRecyclerView);
        mMainRecyclerView.setAdapter(mAdapter);
        mMainRecyclerView.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        int itemId = item.getItemId();

        if(itemId == android.R.id.home){
            finish();
            return true;
        }

        return super.onOptionsItemSelected(item);
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Stop TTS when activity is destroyed
        TextToSpeechManager.getInstance(this).stop();
    }
}
