package uk.ac.bournemouthuniversity.at4send.training;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import uk.ac.bournemouthuniversity.at4send.BaseActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.os.Bundle;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.afollestad.materialdialogs.MaterialDialog;
import com.google.android.material.card.MaterialCardView;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

import org.jetbrains.annotations.NotNull;

import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.adapters.training.QuizResultsAdapter;
import uk.ac.bournemouthuniversity.at4send.api.AT4SENDAPIClient;
import uk.ac.bournemouthuniversity.at4send.api.AT4SENDAPIInterface;
import uk.ac.bournemouthuniversity.at4send.extensions.ui.Dialogs;
import uk.ac.bournemouthuniversity.at4send.extensions.utils.ArrayListAnySize;
import uk.ac.bournemouthuniversity.at4send.extensions.utils.Network;
import uk.ac.bournemouthuniversity.at4send.models.response.QuizResultsResponse;
import uk.ac.bournemouthuniversity.at4send.models.training.QuizResult;
import uk.ac.bournemouthuniversity.at4send.utils.ReadAloudUtils;
import uk.ac.bournemouthuniversity.at4send.utils.TextToSpeechManager;

public class TrainingResultsActivity extends BaseActivity {
    private static final String TAG = "TrainingResults";

    private List<QuizResult> resultsList = new ArrayListAnySize<>();
    private QuizResultsAdapter mAdapter;

    private LinearLayout mNoResultsView;
    private LinearLayout mLoaderView;
    private MaterialCardView mResultsCardView;

    private final FirebaseCrashlytics crashlytics = FirebaseCrashlytics.getInstance();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_training_results);


        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        ActionBar ab = getSupportActionBar();

        // Add read-aloud button to instructions text
        TextView instructionsText = findViewById(R.id.training_results_instructions);
        if (instructionsText != null) {
            ReadAloudUtils.addReadAloudButton(this, instructionsText);
        }

        if(ab != null){
            ab.setDisplayHomeAsUpEnabled(true);
        }

        mAdapter = new QuizResultsAdapter(this, resultsList);

        RecyclerView mMainRecyclerView = findViewById(R.id.mainRecyclerView);
        mMainRecyclerView.setAdapter(mAdapter);
        mMainRecyclerView.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));

        mLoaderView = findViewById(R.id.loader);
        mNoResultsView = findViewById(R.id.noResults);
        mResultsCardView = findViewById(R.id.results);

        findViewById(R.id.returnHome).setOnClickListener(v -> {
            finish();
        });
        
        // Apply high contrast if enabled
        if (uk.ac.bournemouthuniversity.at4send.utils.ContrastUtils.isHighContrastEnabled(this)) {
            applyHighContrastToUI();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        
        // Reapply high contrast when returning to activity
        if (uk.ac.bournemouthuniversity.at4send.utils.ContrastUtils.isHighContrastEnabled(this)) {
            applyHighContrastToUI();
        }

        if(!Network.isNetworkAvailable(this)){
            new MaterialDialog(TrainingResultsActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                    .title(null, "Network Access Required")
                    .message(null, "An active network connection is required for this function. Please connect to the internet and try again.", null)
                    .cancelable(false)
                    .positiveButton(null, "Dismiss", materialDialog -> {
                        materialDialog.dismiss();
                        finish();
                        return null;
                    }).show();
            return;
        }

        populateQuizResults();
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        int itemId = item.getItemId();

        if(itemId == android.R.id.home){
            super.onBackPressed();
            findViewById(R.id.returnHome).setOnClickListener(v -> finish());
            return true;
        }

        return super.onOptionsItemSelected(item);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Stop TTS when activity is destroyed
        TextToSpeechManager.getInstance(this).stop();
    }

    private void populateQuizResults(){
        MaterialDialog progressDialog = Dialogs.generateProgressDialog(this, "Loading...");
        progressDialog.show();

        mLoaderView.setVisibility(View.VISIBLE);

        resultsList.clear();
        mAdapter.notifyDataSetChanged();

        AT4SENDAPIInterface apiService =
                AT4SENDAPIClient.getClient().create(AT4SENDAPIInterface.class);

        Call<QuizResultsResponse> call;

        call = apiService.getTrainingResults(FirebaseAuth.getInstance().getCurrentUser().getUid());

        call.enqueue(new Callback<QuizResultsResponse>() {
            @Override
            public void onResponse(@NotNull Call<QuizResultsResponse>call, @NotNull Response<QuizResultsResponse> response) {
                if(response.body() != null) {
                    List<QuizResult> technologies = response.body().getQuizResults();

                    if (technologies != null) {
                        Log.d(TAG, "Number of technologies received: " + technologies.size());
                        Log.d(TAG, "Result count: " + response.body().getCount());

                        resultsList.addAll(technologies);
                        mAdapter.notifyDataSetChanged();

                        if (resultsList.size() == 0) {
                            mNoResultsView.setVisibility(View.VISIBLE);
                        } else {
                            mResultsCardView.setVisibility(View.VISIBLE);
                        }

                        mLoaderView.setVisibility(View.GONE);
                    }
                }else{
                    new MaterialDialog(TrainingResultsActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                            .title(null, "Error Occurred")
                            .message(null, "An error occurred whilst retrieving your results. Please try again later.", null)
                            .cancelable(false)
                            .positiveButton(null, "Dismiss", materialDialog -> {
                                materialDialog.dismiss();
                                finish();
                                return null;
                            }).show();
                }

                progressDialog.dismiss();
            }

            @Override
            public void onFailure(@NotNull Call<QuizResultsResponse> call, @NotNull Throwable t) {
                // Log error here since request failed
                Log.e(TAG, t.toString());
                crashlytics.recordException(t);

                progressDialog.dismiss();

                new MaterialDialog(TrainingResultsActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                        .title(null, "Error Occurred")
                        .message(null, "An error occurred whilst retrieving your results. Please try again later.", null)
                        .cancelable(false)
                        .positiveButton(null, "Dismiss", materialDialog -> {
                            materialDialog.dismiss();
                            finish();
                            return null;
                        }).show();
            }
        });

        mAdapter.notifyDataSetChanged();
    }

    /**
     * Applies high contrast settings to the UI elements
     */
    private void applyHighContrastToUI() {
        // Apply contrast to the main view hierarchy
        uk.ac.bournemouthuniversity.at4send.utils.ContrastUtils.applyContrastToViewHierarchy(findViewById(android.R.id.content));
        
        // Apply contrast to toolbar
        Toolbar toolbar = findViewById(R.id.toolbar);
        if (toolbar != null) {
            toolbar.setTitleTextColor(uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils.getTextColor(this).getDefaultColor());
            if (toolbar.getNavigationIcon() != null) {
                toolbar.getNavigationIcon().setColorFilter(
                    uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils.getTextColor(this).getDefaultColor(),
                    android.graphics.PorterDuff.Mode.SRC_IN
                );
            }
        }
        
        // Apply contrast to RecyclerView items
        RecyclerView recyclerView = findViewById(R.id.mainRecyclerView);
        if (recyclerView != null) {
            // Apply to visible items
            for (int i = 0; i < recyclerView.getChildCount(); i++) {
                View itemView = recyclerView.getChildAt(i);
                if (itemView != null) {
                    uk.ac.bournemouthuniversity.at4send.utils.ContrastUtils.applyContrastToViewHierarchy(itemView);
                }
            }
            
            // Force adapter to refresh all items
            if (mAdapter != null) {
                mAdapter.notifyDataSetChanged();
            }
        }
        
        // Apply contrast to no results view
        if (mNoResultsView != null) {
            for (int i = 0; i < mNoResultsView.getChildCount(); i++) {
                View child = mNoResultsView.getChildAt(i);
                if (child instanceof android.widget.TextView) {
                    ((android.widget.TextView) child).setTextColor(
                        uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils.getTextColor(this).getDefaultColor()
                    );
                }
            }
        }
        
        // Apply contrast to results card
        if (mResultsCardView != null) {
            mResultsCardView.setCardBackgroundColor(
                uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils.getBackgroundColor(this).getDefaultColor()
            );
        }
        
        // Apply contrast to return home button
        android.view.View returnHomeButton = findViewById(R.id.returnHome);
        if (returnHomeButton instanceof android.widget.Button) {
            ((android.widget.Button) returnHomeButton).setTextColor(
                uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils.getTextColor(this).getDefaultColor()
            );
            returnHomeButton.setBackgroundTintList(
                uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils.getBackgroundColor(this)
            );
        }
    }
}
