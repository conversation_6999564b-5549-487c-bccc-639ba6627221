package uk.ac.bournemouthuniversity.at4send.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatCheckedTextView;
import androidx.core.content.res.ResourcesCompat;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.models.Condition;

public class ConditionsSelectionAdapter extends RecyclerView.Adapter<ConditionsSelectionAdapter.ItemViewHolder> {
    private static final String TAG = "AbilitiesAdapter";

    private List<Condition> conditionsList;
    private Context context;
    private OnConditionItemChangedListener onConditionItemChangedListener;

    public class ItemViewHolder extends RecyclerView.ViewHolder {
        public AppCompatCheckedTextView conditionItem;
        public LinearLayout itemView;

        public ItemViewHolder(View view) {
            super(view);
            conditionItem = view.findViewById(R.id.conditionText);
            itemView = view.findViewById(R.id.itemView);
        }
    }

    public ConditionsSelectionAdapter(Context context, List<Condition> conditionsList, OnConditionItemChangedListener itemChangedListener) {
        this.conditionsList = conditionsList;
        this.context = context;
        this.onConditionItemChangedListener = itemChangedListener;
    }

    @NonNull
    @Override
    public ItemViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_condition_selection, parent, false);

        return new ItemViewHolder(itemView);
    }

    @Override
    public void onBindViewHolder(@NonNull final ItemViewHolder holder, final int position) {
        final Condition item = conditionsList.get(position);

        holder.conditionItem.setText(item.getConditionName());

        holder.itemView.setOnClickListener(view -> {
            holder.conditionItem.setChecked(!holder.conditionItem.isChecked());
            if(holder.conditionItem.isChecked()){
                holder.itemView.setBackgroundColor(ResourcesCompat.getColor(context.getResources(), R.color.md_blue_50, null));
            }else{
                holder.itemView.setBackgroundColor(ResourcesCompat.getColor(context.getResources(), R.color.white, null));
            }

            onConditionItemChangedListener.onConditionItemChanged();
        });
    }

    @Override
    public int getItemCount() {
        return conditionsList.size();
    }

    public interface OnConditionItemChangedListener {
        void onConditionItemChanged();
    }
}
