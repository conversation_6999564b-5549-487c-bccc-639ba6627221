<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/trainingHelpRootLayout"
    android:background="@color/white"
    tools:context=".training.TrainingHelpActivity">

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/slideArea"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/bottomArea"
        android:background="@color/md_blue_600"/>

    <LinearLayout
        android:id="@+id/bottomArea"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent">

        <me.relex.circleindicator.CircleIndicator3
            android:id="@+id/indicator"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            app:ci_height="10dp"
            app:ci_width="10dp"
            app:ci_drawable="@drawable/ic_circle"
            app:ci_drawable_unselected="@drawable/ic_circle"
            app:layout_constraintBottom_toTopOf="@id/bottomArea"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingBottom="16dp"
            android:paddingTop="8dp"
            android:paddingHorizontal="16dp">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/prevBtn"
                    style="@style/Widget.MaterialComponents.Button.UnelevatedButton"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="wrap_content"
                    android:letterSpacing="0"
                    android:layout_marginHorizontal="8dp"
                    android:text="Previous"
                    android:textColor="@color/md_grey_900"
                    app:backgroundTint="@color/md_grey_300"
                    app:fontFamily="@font/asap_medium"
                    android:visibility="invisible"/>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/nextBtn"
                    style="@style/Widget.MaterialComponents.Button.UnelevatedButton"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="wrap_content"
                    android:letterSpacing="0"
                    android:layout_marginHorizontal="8dp"
                    android:text="Next"
                    android:textColor="@color/md_white_1000"
                    app:backgroundTint="@color/md_blue_600"
                    app:fontFamily="@font/asap_medium" />

        </LinearLayout>

        <androidx.appcompat.widget.AppCompatTextView
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="12dp"
            android:text="Copyright © Bournemouth University.\nAll Rights Reserved.\n\nApp designed and developed by Vers Creative UK."
            android:textAlignment="center"/>

    </LinearLayout>


    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/close"
            android:clickable="true"
            android:focusable="true"
            android:layout_width="24dp"
            android:layout_height="24dp"
            app:srcCompat="@drawable/close"
            android:layout_margin="16dp"
            app:tint="@color/white"
            android:foreground="?attr/actionBarItemBackground"/>

    </FrameLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
