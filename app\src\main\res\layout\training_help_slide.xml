<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/slideRootLayout"
    android:background="@color/white">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/slideBackground"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        app:srcCompat="@drawable/ic_clouds" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/slideHelpIcon"
        android:layout_width="140dp"
        android:layout_height="140dp"
        app:layout_constraintBottom_toTopOf="@id/slideContent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintVertical_bias="0.25"
        app:srcCompat="@drawable/ic_help"
        app:tint="@color/colorPrimary"/>

    <LinearLayout
        android:id="@+id/slideContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        android:padding="16dp"
        android:background="@android:color/transparent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_marginBottom="4dp">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/slideTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Test Slide"
                android:textSize="24sp"
                app:fontFamily="@font/asap_bold"
                android:textColor="@color/md_grey_800"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/slideContentText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Slide text to cover a maximum of 4 lines of text providing more descriptive information regarding the contexts of the slide and/or topic of choice which the slide depicts."
                android:textSize="15sp"
                app:fontFamily="@font/asap_regular"
                android:textColor="@color/md_grey_600"/>
        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>