<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/md_grey_50">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:paddingTop="18dp"
            android:paddingBottom="18dp">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Assistive Technologies"
                android:textAppearance="@style/AT4SEND.AppTheme.TextAppearance.Title"
                android:textStyle="bold"
            />
        </LinearLayout>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/browse_assistive_technologies_card"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardBackgroundColor="@color/md_white_1000"
            app:cardCornerRadius="8dp"
            app:strokeWidth="1dp"
            app:strokeColor="@color/md_grey_200"
            app:cardElevation="1dp"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?attr/selectableItemBackground"
            android:layout_marginTop="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:padding="16dp">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    app:cardBackgroundColor="@color/md_cyan_500"
                    app:cardCornerRadius="6dp"
                    app:cardElevation="0dp"
                    app:contentPadding="12dp"
                    android:layout_marginEnd="16dp">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        app:srcCompat="@drawable/ic_assistive_tech"
                        app:tint="@color/md_white_1000" />
                </com.google.android.material.card.MaterialCardView>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Browse Assistive Technologies"
                        android:textSize="18sp"
                        android:textAppearance="@style/AT4SEND.AppTheme.TextAppearance.Subtitle"
                        android:textFontWeight="500" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Browse a list of available Assistive Technologies."
                        android:textSize="16sp" />
                </LinearLayout>

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginStart="16dp"
                    app:srcCompat="@drawable/ic_chevron_right"
                    app:tint="@color/md_grey_500" />

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>
    </LinearLayout>
</ScrollView>