package uk.ac.bournemouthuniversity.at4send.models;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class AbilityGroup {

    @SerializedName("name")
    String groupName;

    @SerializedName("abilities")
    List<Ability> groupAbilities;

    public AbilityGroup(String groupName, List<Ability> groupAbilities) {
        this.groupName = groupName;
        this.groupAbilities = groupAbilities;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public List<Ability> getGroupAbilities() {
        return groupAbilities;
    }

    public void setGroupAbilities(List<Ability> groupAbilities) {
        this.groupAbilities = groupAbilities;
    }
}
