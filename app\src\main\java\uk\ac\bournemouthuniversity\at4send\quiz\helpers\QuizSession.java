package uk.ac.bournemouthuniversity.at4send.quiz.helpers;

import org.jetbrains.annotations.NotNull;

import java.io.Serializable;

public class QuizSession implements Serializable {

    private Integer totalAttempts = 0;
    private Integer correctAttempts = 0;
    private Integer consecutiveAttempts = 0;
    private Integer bestConsecutiveAttempts = 0;
    private String quizName = "";

    public QuizSession() {

    }

    public QuizSession(String serializedSession){
        this(serializedSession, true);
    }

    public QuizSession(String serializedSession, boolean serialized) {
        if(serialized) {
            try {
                String[] split = serializedSession.split(";", 5);
                quizName = split[0];
                totalAttempts = Integer.parseInt(split[1]);
                correctAttempts = Integer.parseInt(split[2]);
                consecutiveAttempts = Integer.parseInt(split[3]);
                bestConsecutiveAttempts = Integer.parseInt(split[4]);
            } catch (Exception e) {
                quizName = "";
                totalAttempts = 0;
                correctAttempts = 0;
                consecutiveAttempts = 0;
                bestConsecutiveAttempts = 0;
            }
        }else{
            quizName = serializedSession;
        }
    }

    public String getQuizName() {
        return quizName;
    }

    public void setQuizName(String quizName) {
        this.quizName = quizName;
    }

    public Integer getTotalAttempts() {
        return totalAttempts;
    }

    public void setTotalAttempts(Integer totalAttempts) {
        this.totalAttempts = totalAttempts;
    }

    public Integer getCorrectAttempts() {
        return correctAttempts;
    }

    public String getCorrectnessRate() {
        return getCorrectnessRate(correctAttempts, totalAttempts);
    }

    private String getCorrectnessRate(Integer correct, Integer total) {
        int percent = (int) Math.round(((float) correct / (float) total) * 100);
        return percent + "%";
    }

    public double getCorrectnessRateRaw(){
        return (int) Math.round(((float) correctAttempts / (float) totalAttempts) * 100);
    }

    public void setCorrectAttempts(Integer correctAttempts) {
        this.correctAttempts = correctAttempts;
    }

    public Integer getConsecutiveAttempts() {
        return consecutiveAttempts;
    }

    public void setConsecutiveAttempts(Integer consecutive) {
        this.consecutiveAttempts = consecutive;
        if (consecutive > bestConsecutiveAttempts) {
            bestConsecutiveAttempts = consecutive;
        }
    }

    public Integer getBestConsecutiveAttempts() {
        return bestConsecutiveAttempts;
    }

    @NotNull
    public String toString() {
        return quizName + ";" + totalAttempts + ";" + correctAttempts + ";"
                + consecutiveAttempts + ";" + bestConsecutiveAttempts;
    }

}
