<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".account.FirebaseUIActivity">

    <LinearLayout
        android:id="@+id/loader"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        android:padding="32dp"
        android:gravity="center">

        <me.zhanghai.android.materialprogressbar.MaterialProgressBar
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginEnd="@dimen/keyline_1_minus_8dp"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Logging in..."
            android:textColor="@color/md_grey_600" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>