package uk.ac.bournemouthuniversity.at4send.utils;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.SharedPreferences;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.util.TypedValue;
import android.widget.ListView;
import android.widget.TextView;

import androidx.appcompat.widget.AppCompatButton;
import androidx.preference.PreferenceManager;
import com.google.android.material.button.MaterialButton;

import uk.ac.bournemouthuniversity.at4send.R;

public class ButtonSizeUtils {
    private static final String BUTTON_SIZE_KEY = "button_size";
    
    public static final String SIZE_SMALL = "small";
    public static final String SIZE_NORMAL = "normal";
    public static final String SIZE_LARGE = "large";
    
    private static String lastAppliedSize = SIZE_NORMAL;
    
    public static void setButtonSize(Context context, String size) {
        if (context == null) return;
        
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(context);
        prefs.edit().putString(BUTTON_SIZE_KEY, size).apply();
        
        if (size.equals(lastAppliedSize)) {
            return;
        }
        
        lastAppliedSize = size;
        
        if (context instanceof Activity) {
            Activity activity = (Activity) context;
            if (!activity.isFinishing() && !activity.isDestroyed()) {
                activity.recreate();
            }
        }
    }
    
    public static void applyButtonSize(Context context) {
        if (context == null) return;
        String size = getButtonSize(context);
        setButtonSize(context, size);
    }
    
    public static String getButtonSize(Context context) {
        if (context == null) return SIZE_NORMAL;
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(context);
        
        // Handle migration from integer to string preference
        try {
            return prefs.getString(BUTTON_SIZE_KEY, SIZE_NORMAL);
        } catch (ClassCastException e) {
            // If we previously stored an integer value, convert it to the appropriate string value
            try {
                int intValue = prefs.getInt(BUTTON_SIZE_KEY, 100);
                String stringValue;
                
                if (intValue <= 80) {
                    stringValue = SIZE_SMALL;
                } else if (intValue >= 130) {
                    stringValue = SIZE_LARGE;
                } else {
                    stringValue = SIZE_NORMAL;
                }
                
                // Save the new string value
                prefs.edit().putString(BUTTON_SIZE_KEY, stringValue).apply();
                return stringValue;
            } catch (Exception ex) {
                // If all else fails, return the default
                return SIZE_NORMAL;
            } 
        }
    }
    
    public static float getButtonSizeScale(Context context) {
        String size = getButtonSize(context);
        switch (size) {
            case SIZE_SMALL:
                return 0.7f;  // More noticeable reduction
            case SIZE_LARGE:
                return 1.5f;  // More noticeable increase
            case SIZE_NORMAL:
            default:
                return 1.0f;
        }
    }
    
    public static void applyButtonSizeToView(View view) {
        if (view == null) return;
        
        Context context = view.getContext();
        float scale = getButtonSizeScale(context);
        
        // Handle all types of buttons
        if (view instanceof Button || 
            view instanceof MaterialButton || 
            view instanceof AppCompatButton ||
            (view.getClass().getName().contains("Button") && view instanceof TextView)) {
            
            // Apply text size scaling
            if (view instanceof TextView) {
                float currentSize = ((TextView) view).getTextSize();
                ((TextView) view).setTextSize(TypedValue.COMPLEX_UNIT_PX, currentSize * scale);
            }
            
            // Apply layout parameter scaling
            ViewGroup.LayoutParams params = view.getLayoutParams();
            if (params != null) {
                // Scale width if it's not MATCH_PARENT or WRAP_CONTENT
                if (params.width > 0 && params.width != ViewGroup.LayoutParams.MATCH_PARENT && 
                    params.width != ViewGroup.LayoutParams.WRAP_CONTENT) {
                    params.width = (int) (params.width * scale);
                }
                
                // Scale height if it's not MATCH_PARENT or WRAP_CONTENT
                if (params.height > 0 && params.height != ViewGroup.LayoutParams.MATCH_PARENT && 
                    params.height != ViewGroup.LayoutParams.WRAP_CONTENT) {
                    params.height = (int) (params.height * scale);
                }
                
                // For WRAP_CONTENT, we can apply min width/height
                if (params.width == ViewGroup.LayoutParams.WRAP_CONTENT) {
                    view.setMinimumWidth((int) (view.getMinimumWidth() * scale));
                }
                if (params.height == ViewGroup.LayoutParams.WRAP_CONTENT) {
                    view.setMinimumHeight((int) (view.getMinimumHeight() * scale));
                }
                
                view.setLayoutParams(params);
            }
            
            // Apply padding scaling
            int paddingLeft = view.getPaddingLeft();
            int paddingTop = view.getPaddingTop();
            int paddingRight = view.getPaddingRight();
            int paddingBottom = view.getPaddingBottom();
            
            view.setPadding(
                (int) (paddingLeft * scale),
                (int) (paddingTop * scale),
                (int) (paddingRight * scale),
                (int) (paddingBottom * scale)
            );
        }
        
        // Recursively apply to child views
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            for (int i = 0; i < viewGroup.getChildCount(); i++) {
                applyButtonSizeToView(viewGroup.getChildAt(i));
            }
        }
    }

    /**
     * Apply button size to a specific button, forcing dimension changes
     * @param button The button to resize
     */
    public static void forceButtonResize(View button) {
        if (button == null || !(button instanceof Button || button instanceof MaterialButton)) {
            return;
        }
        
        Context context = button.getContext();
        float scale = getButtonSizeScale(context);
        
        // Get the default button size from resources
        int defaultButtonSize = context.getResources().getDimensionPixelSize(R.dimen.button_size);
        
        // Calculate new dimensions
        int newSize = (int) (defaultButtonSize * scale);
        
        // Apply new dimensions
        ViewGroup.LayoutParams params = button.getLayoutParams();
        if (params != null) {
            // Only modify if not MATCH_PARENT or WRAP_CONTENT
            if (params.width > 0 && params.width != ViewGroup.LayoutParams.MATCH_PARENT && 
                params.width != ViewGroup.LayoutParams.WRAP_CONTENT) {
                params.width = newSize;
            }
            if (params.height > 0 && params.height != ViewGroup.LayoutParams.MATCH_PARENT && 
                params.height != ViewGroup.LayoutParams.WRAP_CONTENT) {
                params.height = newSize;
            }
            
            // For WRAP_CONTENT, set minimum dimensions
            if (params.width == ViewGroup.LayoutParams.WRAP_CONTENT) {
                button.setMinimumWidth(newSize);
            }
            if (params.height == ViewGroup.LayoutParams.WRAP_CONTENT) {
                button.setMinimumHeight(newSize);
            }
            
            button.setLayoutParams(params);
        }
        
        // Apply padding scaling
        int basePadding = context.getResources().getDimensionPixelSize(R.dimen.button_padding);
        int newPadding = (int) (basePadding * scale);
        button.setPadding(newPadding, newPadding, newPadding, newPadding);
    }

    /**
     * Apply button size to a preference dialog
     * @param dialog The dialog to apply button size to
     * @param context The context
     */
    public static void applyButtonSizeToDialog(Dialog dialog, Context context) {
        if (dialog == null || context == null) return;
        
        float scale = getButtonSizeScale(context);
        
        // Apply to dialog buttons
        Button positiveButton = dialog.findViewById(android.R.id.button1);
        Button negativeButton = dialog.findViewById(android.R.id.button2);
        Button neutralButton = dialog.findViewById(android.R.id.button3);
        
        if (positiveButton != null) {
            applyButtonSizeToView(positiveButton);
        }
        if (negativeButton != null) {
            applyButtonSizeToView(negativeButton);
        }
        if (neutralButton != null) {
            applyButtonSizeToView(neutralButton);
        }
        
        // Apply to list items if present
        ListView listView = dialog.findViewById(android.R.id.list);
        if (listView != null) {
            for (int i = 0; i < listView.getChildCount(); i++) {
                View child = listView.getChildAt(i);
                applyButtonSizeToView(child);
            }
        }
    }

}

