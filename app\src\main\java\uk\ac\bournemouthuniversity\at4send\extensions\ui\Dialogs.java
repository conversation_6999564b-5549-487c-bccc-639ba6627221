package uk.ac.bournemouthuniversity.at4send.extensions.ui;

import android.content.Context;
import android.view.View;

import androidx.appcompat.widget.AppCompatTextView;

import com.afollestad.materialdialogs.MaterialDialog;
import com.afollestad.materialdialogs.customview.DialogCustomViewExtKt;

import uk.ac.bournemouthuniversity.at4send.R;

public class Dialogs {

    public static MaterialDialog generateProgressDialog(Context ctx, String message){
        MaterialDialog dialog = DialogCustomViewExtKt.customView(new MaterialDialog(ctx, MaterialDialog.getDEFAULT_BEHAVIOR()), R.layout.dialog_progress, null, false, false, true, false);

        View view = DialogCustomViewExtKt.getCustomView(dialog);
        ((AppCompatTextView) view.findViewById(R.id.progressDialogMessage)).setText(message);

        return dialog;
    }

}
