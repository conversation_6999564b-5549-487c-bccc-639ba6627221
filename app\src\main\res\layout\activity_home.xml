<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".HomeActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/AT4SEND.AppTheme.AppBarOverlay">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="@color/md_blue_500"
            app:titleTextAppearance="@style/AT4SEND.AppTheme.TextAppearance.Title"
            app:title="EduAbility"
            app:theme="@style/AT4SEND.BlueTheme.NoActionBar.Toolbar"
            app:popupTheme="@style/ThemeOverlay.MaterialComponents.Dark">

        </androidx.appcompat.widget.Toolbar>

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabs"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:tabTextColor="@color/md_blue_200"
            app:tabSelectedTextColor="@color/md_white_1000"
            app:tabIndicatorColor="@color/md_white_1000"
            app:tabIndicatorFullWidth="false"
            app:tabTextAppearance="@style/AT4SEND.AppTheme.TextAppearance.Tabs"
            app:tabMode="scrollable"
            app:tabRippleColor="@color/md_white_1000"
            android:background="@color/md_blue_500" />
    </com.google.android.material.appbar.AppBarLayout>

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/view_pager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
