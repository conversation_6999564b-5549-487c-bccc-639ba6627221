<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/transparent">

    <RelativeLayout
        android:id="@+id/view_search"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#50000000"
        android:clickable="true"
        android:focusable="true"
        android:visibility="invisible">

    </RelativeLayout>

    <androidx.cardview.widget.CardView
        android:id="@+id/card_search"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="4dp"
        android:visibility="invisible"
        card_view:cardBackgroundColor="@android:color/white"
        card_view:cardCornerRadius="2dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:id="@+id/linearLayout_search"
                android:layout_width="match_parent"
                android:layout_height="48dp">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/image_search_back"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="Search"
                    android:padding="12dp"
                    android:src="@drawable/abc_ic_ab_back_material" />

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/edit_text_search"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="#fff"
                    android:gravity="center_vertical"
                    android:hint="Search"
                    android:imeOptions="actionSearch"
                    android:inputType="textCapWords"
                    android:paddingLeft="12dp"
                    android:paddingRight="8dp"
                    android:singleLine="true"/>

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/clearSearch"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:clickable="true"
                    android:focusable="true"
                    android:visibility="invisible"
                    android:contentDescription="Cancel"
                    android:padding="12dp"
                    android:src="@drawable/abc_ic_clear_material" />
            </LinearLayout>

            <View
                android:id="@+id/line_divider"
                android:layout_width="match_parent"
                android:layout_height=".5dp"
                android:layout_below="@+id/linearLayout_search"
                android:background="#eee"/>

            <ListView
                android:id="@+id/material_search_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:divider="@null"
                android:dividerHeight="0dp"
                android:layout_below="@+id/line_divider"/>

            <FrameLayout
                android:id="@+id/progressLayout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone"
                android:layout_below="@+id/material_search_container">
                <ProgressBar
                    android:id="@+id/progressBar"
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    android:layout_gravity="center"
                    android:visibility="visible" />
                <TextView
                    android:id="@+id/textView13"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="16sp"
                    android:fontFamily="roboto"
                    android:layout_gravity="center"
                    android:text="No Results Found"
                    android:textColor="@color/color_accent" />

            </FrameLayout>

        </RelativeLayout>
    </androidx.cardview.widget.CardView>

</RelativeLayout>