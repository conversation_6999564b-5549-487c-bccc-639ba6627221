package uk.ac.bournemouthuniversity.at4send;

import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.PorterDuff;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.ImageView;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.OnBackPressedCallback;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBar;
import uk.ac.bournemouthuniversity.at4send.BaseActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.biometric.BiometricManager;
import androidx.biometric.BiometricPrompt;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import androidx.preference.ListPreference;
import androidx.preference.Preference;
import androidx.preference.PreferenceCategory;
import androidx.preference.PreferenceFragmentCompat;
import androidx.preference.PreferenceScreen;
import androidx.preference.SwitchPreferenceCompat;
import androidx.preference.SeekBarPreference;

import com.afollestad.materialdialogs.MaterialDialog;
import com.google.android.material.appbar.CollapsingToolbarLayout;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.concurrent.Executor;

import uk.ac.bournemouthuniversity.at4send.extensions.helpers.PreferenceManager;
import uk.ac.bournemouthuniversity.at4send.security.CreatePinCodeActivity;
import uk.ac.bournemouthuniversity.at4send.security.EnterPinCodeActivity;
import uk.ac.bournemouthuniversity.at4send.utils.ButtonSizeUtils;
import uk.ac.bournemouthuniversity.at4send.utils.ContrastUtils;
import uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils;

import android.text.SpannableString;
import android.text.Spannable;
import android.text.style.ForegroundColorSpan;
import androidx.preference.PreferenceGroup;
import android.os.Handler;
import android.os.Looper;
import androidx.fragment.app.DialogFragment;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.preference.PreferenceDialogFragmentCompat;
import androidx.preference.ListPreferenceDialogFragmentCompat;

public class SettingsActivity extends BaseActivity implements 
        PreferenceFragmentCompat.OnPreferenceStartScreenCallback {

    ActionBar actionBar;
    CollapsingToolbarLayout collapsingToolbarLayout;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_settings);

        androidx.preference.PreferenceManager.setDefaultValues(this, R.xml.root_preferences,false);

        getSupportFragmentManager()
                .beginTransaction()
                .replace(R.id.settings, new SettingsFragment(), SettingsFragment.FRAGMENT_TAG)
                .addToBackStack(SettingsFragment.FRAGMENT_TAG)
                .commit();

        collapsingToolbarLayout = findViewById(R.id.toolbarLayout);

        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.setDisplayHomeAsUpEnabled(true);
        }

        getOnBackPressedDispatcher().addCallback(this, new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                finish();
            }
        });
        
        // Apply contrast to toolbar and title
        applyContrastToToolbar();
    }

    @Override
    protected void onResume() {
        super.onResume();
        // Reapply contrast when returning to activity
        applyContrastToToolbar();
    }

    private void applyContrastToToolbar() {
        if (ContrastUtils.isHighContrastEnabled(this)) {
            // Apply contrast to toolbar title
            if (collapsingToolbarLayout != null) {
                collapsingToolbarLayout.setCollapsedTitleTextColor(TextSizeUtils.getTextColor(this).getDefaultColor());
                collapsingToolbarLayout.setExpandedTitleColor(TextSizeUtils.getTextColor(this).getDefaultColor());
            }
            
            // Apply contrast to back button
            Toolbar toolbar = findViewById(R.id.toolbar);
            if (toolbar != null) {
                // Set navigation icon (back button) tint
                if (toolbar.getNavigationIcon() != null) {
                    toolbar.getNavigationIcon().setColorFilter(
                        TextSizeUtils.getTextColor(this).getDefaultColor(), 
                        PorterDuff.Mode.SRC_ATOP
                    );
                }
            }
            
            // Apply background color to toolbar and collapsing toolbar
            if (collapsingToolbarLayout != null) {
                collapsingToolbarLayout.setBackgroundTintList(TextSizeUtils.getBackgroundColor(this));
            }
        }
    }

    @Override
    public boolean onPreferenceStartScreen(PreferenceFragmentCompat preferenceFragmentCompat,
                                           PreferenceScreen preferenceScreen) {
        FragmentTransaction ft = getSupportFragmentManager().beginTransaction();
        SettingsFragment fragment = new SettingsFragment();
        Bundle args = new Bundle();
        args.putString(PreferenceFragmentCompat.ARG_PREFERENCE_ROOT, preferenceScreen.getKey());
        fragment.setArguments(args);
        ft.replace(R.id.settings, fragment, preferenceScreen.getKey());
        ft.addToBackStack(preferenceScreen.getKey());
        ft.commit();

        collapsingToolbarLayout.setTitle(preferenceScreen.getTitle());

        return true;
    }


    public static class SettingsFragment extends PreferenceFragmentCompat {

        static final String FRAGMENT_TAG = "settingsFragment";

        @Override
        public void onCreatePreferences(Bundle savedInstanceState, String rootKey) {
            setPreferencesFromResource(R.xml.root_preferences, rootKey);

            Preference pinCodePreference = findPreference("pinCode");
            if (pinCodePreference != null) {
                pinCodePreference.setSummaryProvider(PinCodeSummaryProfiler.getInstance(getActivity()));
            }

            Preference appVersionPreference = findPreference("appVersion");
            if(appVersionPreference != null){
                Date buildDate = BuildConfig.BUILD_TIME;
                Integer buildVersion = BuildConfig.BUILD_VERSION;
                SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault());

                String versionDateFull = format.format(buildDate);

                try {
                    if(getContext() != null) {
                        PackageInfo pInfo = getContext().getPackageManager().getPackageInfo(getContext().getPackageName(), 0);

                        String versionDetail = "v" + pInfo.versionName + " - Build " + buildVersion.toString() + " (" + versionDateFull + ")";
                        appVersionPreference.setSummary(versionDetail);
                    }
                } catch (PackageManager.NameNotFoundException e) {
                    e.printStackTrace();
                }
            }

            SwitchPreferenceCompat biometricUnlock = findPreference("biometricUnlock");
            if(biometricUnlock != null){
                BiometricManager biometricManager = BiometricManager.from(biometricUnlock.getContext());
                switch (biometricManager.canAuthenticate(BiometricManager.Authenticators.BIOMETRIC_STRONG)) {
                    case BiometricManager.BIOMETRIC_SUCCESS:
                        // App can authenticate using biometrics
                        biometricUnlock.setEnabled(true);
                        biometricUnlock.setSummary("Enable biometric authentication rather than entering your pin code.");
                        break;
                    case BiometricManager.BIOMETRIC_ERROR_HW_UNAVAILABLE:
                    case BiometricManager.BIOMETRIC_ERROR_NO_HARDWARE:
                        // Biometric features are currently unavailable
                        // No biometric features available on this device
                        biometricUnlock.setEnabled(false);
                        biometricUnlock.setVisible(false);
                        break;
                    case BiometricManager.BIOMETRIC_ERROR_NONE_ENROLLED:
                        // The user hasn't associated any biometric credentials with their account
                        biometricUnlock.setEnabled(false);
                        biometricUnlock.setSummary("You do not have any biometric features registered on your device.");
                        break;
                }

                if(biometricUnlock.isChecked()){
                    if(!biometricUnlock.isEnabled()){
                        biometricUnlock.setChecked(false);
                        MaterialDialog dialog = new MaterialDialog(biometricUnlock.getContext(), MaterialDialog.getDEFAULT_BEHAVIOR())
                                .title(null, "Error")
                                .message(null, "Your biometric credentials have changed, please re-verify your credentials.", null)
                                .positiveButton(null, "OK", materialDialog -> {
                                    materialDialog.dismiss();
                                    return null;
                                });

                        if (ContrastUtils.isHighContrastEnabled(biometricUnlock.getContext())) {
                            ContrastUtils.applyContrastToDialog(dialog);
                        }
                        
                        dialog.show();
                    }
                }


            }

            SeekBarPreference textSizePreference = findPreference("text_size");
            if (textSizePreference != null) {
                textSizePreference.setOnPreferenceChangeListener((preference, newValue) -> {
                    if (getActivity() != null) {
                        TextSizeUtils.setTextSize(getActivity(), ((Integer) newValue) / 100f);
                    }
                    return true;
                });
            }

            SwitchPreferenceCompat highContrastPreference = findPreference("high_contrast");
            if (highContrastPreference != null) {
                highContrastPreference.setOnPreferenceChangeListener((preference, newValue) -> {
                    // Apply contrast immediately to current view before recreating
                    if (getView() != null && getContext() != null) {
                        ContrastUtils.applyContrastToViewHierarchy(getView());
                        
                        // Apply contrast to all preferences immediately
                        if ((Boolean) newValue) {
                            applyContrastToAllPreferences();
                        }
                    }
                    if (getActivity() != null) {
                        getActivity().recreate();
                    }
                    return true;
                });
            }

            ListPreference buttonSizePreference = findPreference("button_size");
            if (buttonSizePreference != null) {
                buttonSizePreference.setOnPreferenceChangeListener((preference, newValue) -> {
                    if (getActivity() != null) {
                        ButtonSizeUtils.setButtonSize(getActivity(), (String) newValue);
                    }
                    return true;
                });
                
                // Add summary provider to display the selected option as subtext
                buttonSizePreference.setSummaryProvider(ListPreference.SimpleSummaryProvider.getInstance());
            }

            ListPreference contrastThemePreference = findPreference("contrast_theme");
            if (contrastThemePreference != null) {
                contrastThemePreference.setOnPreferenceChangeListener((preference, newValue) -> {
                    // Apply contrast immediately to current view before recreating
                    if (getView() != null && getContext() != null && ContrastUtils.isHighContrastEnabled(getContext())) {
                        ContrastUtils.applyContrastToViewHierarchy(getView());
                        applyContrastToAllPreferences();
                    }
                    if (getActivity() != null) {
                        getActivity().recreate();
                    }
                    return true;
                });
                
                // Add summary provider to display the selected option as subtext
                contrastThemePreference.setSummaryProvider(ListPreference.SimpleSummaryProvider.getInstance());
            }
        }

        @Override
        public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
            super.onViewCreated(view, savedInstanceState);
            getListView().setNestedScrollingEnabled(true);
            
            // Apply contrast to all preference views
            if (getContext() != null && ContrastUtils.isHighContrastEnabled(getContext())) {
                ContrastUtils.applyContrastToViewHierarchy(view);
                applyContrastToPreferenceIcons();
                applyContrastToTextViews(view);
                
                // Post a delayed action to ensure all views are fully inflated
                view.post(() -> {
                    applyContrastToAllPreferences();
                    applyContrastToTextViews(view);
                });
            }
        }

        @Override
        public void onResume() {
            super.onResume();
            // Reapply contrast when returning to fragment
            if (getContext() != null && ContrastUtils.isHighContrastEnabled(getContext())) {
                applyContrastToPreferenceIcons();
                applyContrastToAllPreferences();
                
                if (getView() != null) {
                    applyContrastToTextViews(getView());
                    
                    // Post a delayed action to ensure all views are fully inflated
                    getView().post(() -> {
                        applyContrastToAllPreferences();
                        applyContrastToTextViews(getView());
                    });
                }
            }
        }

        private void applyContrastToPreferenceIcons() {
            Context context = getContext();
            if (context == null) return;
            
            // Get the preference screen
            PreferenceScreen screen = getPreferenceScreen();
            if (screen == null) return;
            
            // Apply contrast to all preferences in the screen
            int prefCount = screen.getPreferenceCount();
            for (int i = 0; i < prefCount; i++) {
                Preference pref = screen.getPreference(i);
                
                // Set icon tint for the preference
                if (pref.getIcon() != null) {
                    pref.getIcon().setColorFilter(
                        TextSizeUtils.getTextColor(context).getDefaultColor(),
                        PorterDuff.Mode.SRC_IN
                    );
                }
                
                // If it's a category, apply to all its preferences
                if (pref instanceof PreferenceCategory) {
                    PreferenceCategory category = (PreferenceCategory) pref;
                    int catPrefCount = category.getPreferenceCount();
                    for (int j = 0; j < catPrefCount; j++) {
                        Preference catPref = category.getPreference(j);
                        if (catPref.getIcon() != null) {
                            catPref.getIcon().setColorFilter(
                                TextSizeUtils.getTextColor(context).getDefaultColor(),
                                PorterDuff.Mode.SRC_IN
                            );
                        }
                    }
                }
                
                // If it's a preference screen, apply to its icon
                if (pref instanceof PreferenceScreen) {
                    PreferenceScreen prefScreen = (PreferenceScreen) pref;
                    if (prefScreen.getIcon() != null) {
                        prefScreen.getIcon().setColorFilter(
                            TextSizeUtils.getTextColor(context).getDefaultColor(),
                            PorterDuff.Mode.SRC_IN
                        );
                    }
                }
            }
        }

        private void applyContrastToTextViews(View view) {
            Context context = getContext();
            if (context == null) return;
            
            if (view instanceof TextView) {
                ((TextView) view).setTextColor(TextSizeUtils.getTextColor(context).getDefaultColor());
            }
            
            // Apply contrast to ImageView elements (icons)
            if (view instanceof ImageView) {
                ImageView imageView = (ImageView) view;
                // Apply tint to the icon
                imageView.setColorFilter(
                    TextSizeUtils.getTextColor(context).getDefaultColor(),
                    PorterDuff.Mode.SRC_IN
                );
            }
            
            if (view instanceof ViewGroup) {
                ViewGroup viewGroup = (ViewGroup) view;
                for (int i = 0; i < viewGroup.getChildCount(); i++) {
                    applyContrastToTextViews(viewGroup.getChildAt(i));
                }
            }
        }

        private void applyContrastToAllPreferences() {
            Context context = getContext();
            if (context == null) return;
            
            // Get the preference screen
            PreferenceScreen screen = getPreferenceScreen();
            if (screen == null) return;
            
            // Apply contrast to specific preferences
            SwitchPreferenceCompat secureAppPref = findPreference("secureApp");
            if (secureAppPref != null) {
                secureAppPref.setTitle(getColoredText(secureAppPref.getTitle(), context));
                // Don't set summary if it has a summary provider
                if (secureAppPref.getSummaryProvider() == null) {
                    secureAppPref.setSummary(getColoredText(secureAppPref.getSummary(), context));
                }
            }
            
            Preference pinCodePref = findPreference("pinCode");
            if (pinCodePref != null) {
                pinCodePref.setTitle(getColoredText(pinCodePref.getTitle(), context));
                // Don't modify summary for preferences with summary providers
                // pinCodePref has a summary provider set in onCreatePreferences
            }
            
            SwitchPreferenceCompat biometricPref = findPreference("biometricUnlock");
            if (biometricPref != null) {
                biometricPref.setTitle(getColoredText(biometricPref.getTitle(), context));
                // Don't set summary if it has a summary provider
                if (biometricPref.getSummaryProvider() == null) {
                    biometricPref.setSummary(getColoredText(biometricPref.getSummary(), context));
                }
            }
            
            // Apply to all preferences in the screen
            applyContrastToPreferenceRecursively(screen);
        }
        
        private void applyContrastToPreferenceRecursively(Preference preference) {
            Context context = getContext();
            if (context == null) return;
            
            // Apply contrast to this preference's title
            preference.setTitle(getColoredText(preference.getTitle(), context));
            
            // Only set summary if there's no summary provider
            if (preference.getSummaryProvider() == null && preference.getSummary() != null) {
                preference.setSummary(getColoredText(preference.getSummary(), context));
            }
            
            // If it's a category or screen, apply to all its preferences
            if (preference instanceof PreferenceGroup) {
                PreferenceGroup group = (PreferenceGroup) preference;
                for (int i = 0; i < group.getPreferenceCount(); i++) {
                    applyContrastToPreferenceRecursively(group.getPreference(i));
                }
            }
        }
        
        private CharSequence getColoredText(CharSequence text, Context context) {
            if (text == null) return null;
            
            // Create a SpannableString to apply the color
            SpannableString spannableString = new SpannableString(text);
            spannableString.setSpan(
                new ForegroundColorSpan(TextSizeUtils.getTextColor(context).getDefaultColor()),
                0, spannableString.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            );
            
            return spannableString;
        }

        @Override
        public boolean onPreferenceTreeClick(Preference preference) {
            switch (preference.getKey()){
                case "pinCode":
                    Intent i;
                    if(preference.getSummary().toString().equals("Set")) {
                        i = new Intent(getActivity(), EnterPinCodeActivity.class);
                        i.putExtra(EnterPinCodeActivity.EXTRA_CAN_GO_BACK, true);
                        i.putExtra(EnterPinCodeActivity.EXTRA_SUCCESS_INTENT, CreatePinCodeActivity.class);
                    }else{
                        i = new Intent(getActivity(), CreatePinCodeActivity.class);
                    }
                    startActivity(i);
                    return true;
                case "biometricUnlock":
                    SwitchPreferenceCompat sP = (SwitchPreferenceCompat) preference;
                    if(sP.isChecked()) {
                        Executor executor = ContextCompat.getMainExecutor(preference.getContext());
                        BiometricPrompt biometricPrompt = new BiometricPrompt(getActivity(),
                                executor, new BiometricPrompt.AuthenticationCallback() {
                            @Override
                            public void onAuthenticationError(int errorCode, @NonNull CharSequence errString) {
                                super.onAuthenticationError(errorCode, errString);

                                MaterialDialog dialog = new MaterialDialog(preference.getContext(), MaterialDialog.getDEFAULT_BEHAVIOR())
                                        .title(null, "Error")
                                        .message(null, errString, null)
                                        .positiveButton(null, "OK", materialDialog -> {
                                            materialDialog.dismiss();
                                            return null;
                                        });
                                if (ContrastUtils.isHighContrastEnabled(preference.getContext())) {
                                    ContrastUtils.applyContrastToDialog(dialog);
                                }
                                
                                dialog.show();

                                sP.setChecked(false);
                            }

                            @Override
                            public void onAuthenticationSucceeded(
                                    @NonNull BiometricPrompt.AuthenticationResult result) {
                                super.onAuthenticationSucceeded(result);
                                Toast.makeText(preference.getContext(),
                                        "Authentication succeeded!", Toast.LENGTH_SHORT).show();
                                sP.setChecked(true);
                            }

                            @Override
                            public void onAuthenticationFailed() {
                                super.onAuthenticationFailed();
                                sP.setChecked(false);
                            }
                        });

                        BiometricPrompt.PromptInfo promptInfo = new BiometricPrompt.PromptInfo.Builder()
                                .setTitle("Verify your Credentials to EduAbility")
                                .setSubtitle("Verify using your biometric credential")
                                .setNegativeButtonText("Cancel")
                                .build();

                        biometricPrompt.authenticate(promptInfo);
                    }
                    return true;
                default:
                    return super.onPreferenceTreeClick(preference);
            }
        }

        // Override onDisplayPreferenceDialog to handle our custom dialogs
        @Override
        public void onDisplayPreferenceDialog(Preference preference) {
            // Check if it's one of our preferences that needs high contrast dialog
            if (ContrastUtils.isHighContrastEnabled(requireContext()) && 
                (preference.getKey().equals("button_size") || preference.getKey().equals("contrast_theme"))) {
                // Create our custom high contrast dialog
                DialogFragment dialogFragment = HighContrastListPreferenceDialogFragment.newInstance(preference.getKey());
                dialogFragment.setTargetFragment(this, 0);
                dialogFragment.show(getParentFragmentManager(), "androidx.preference.PreferenceFragment.DIALOG");
            } else {
                super.onDisplayPreferenceDialog(preference);
            }
        }

        // Custom dialog fragment that applies high contrast immediately
        public static class HighContrastListPreferenceDialogFragment extends ListPreferenceDialogFragmentCompat {
            
            public static HighContrastListPreferenceDialogFragment newInstance(String key) {
                final HighContrastListPreferenceDialogFragment fragment = new HighContrastListPreferenceDialogFragment();
                final Bundle b = new Bundle(1);
                b.putString(ARG_KEY, key);
                fragment.setArguments(b);
                return fragment;
            }
            
            @Override
            public Dialog onCreateDialog(Bundle savedInstanceState) {
                Dialog dialog = super.onCreateDialog(savedInstanceState);
                
                // Apply high contrast immediately
                Window window = dialog.getWindow();
                if (window != null) {
                    window.setBackgroundDrawable(new ColorDrawable(
                        TextSizeUtils.getBackgroundColor(requireContext()).getDefaultColor()
                    ));
                }
                
                // Set up a callback for when the dialog is shown
                dialog.setOnShowListener(dialogInterface -> {
                    // Apply contrast to all views in the dialog
                    ContrastUtils.applyContrastToViewHierarchy(dialog.getWindow().getDecorView());
                    
                    // Apply contrast to list items
                    ListView listView = dialog.findViewById(android.R.id.list);
                    if (listView != null) {
                        listView.setBackgroundColor(TextSizeUtils.getBackgroundColor(requireContext()).getDefaultColor());
                        
                        // Apply to each list item
                        for (int i = 0; i < listView.getChildCount(); i++) {
                            View item = listView.getChildAt(i);
                            item.setBackgroundColor(TextSizeUtils.getBackgroundColor(requireContext()).getDefaultColor());
                            ContrastUtils.applyContrastToViewHierarchy(item);
                        }
                        
                        // Set adapter listener to apply contrast to newly visible items
                        listView.setOnHierarchyChangeListener(new ViewGroup.OnHierarchyChangeListener() {
                            @Override
                            public void onChildViewAdded(View parent, View child) {
                                child.setBackgroundColor(TextSizeUtils.getBackgroundColor(requireContext()).getDefaultColor());
                                ContrastUtils.applyContrastToViewHierarchy(child);
                            }
                            
                            @Override
                            public void onChildViewRemoved(View parent, View child) {
                                // Not needed
                            }
                        });
                    }
                });
                
                return dialog;
            }
        }
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        int itemId = item.getItemId();

        if(itemId == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    // Removed onBackPressed() method

    public static final class PinCodeSummaryProfiler implements Preference.SummaryProvider<Preference> {

        private static PinCodeSummaryProfiler sSimpleSummaryProvider;
        private static PreferenceManager pM;

        private PinCodeSummaryProfiler() {}

        /**
         * Retrieve a singleton instance of this simple
         * {@link androidx.preference.Preference.SummaryProvider} implementation.
         *
         * @return a singleton instance of this simple
         * {@link androidx.preference.Preference.SummaryProvider} implementation
         */
        static PinCodeSummaryProfiler getInstance(Context ctx) {
            pM = new PreferenceManager(ctx);

            if (sSimpleSummaryProvider == null) {
                sSimpleSummaryProvider = new PinCodeSummaryProfiler();
            }
            return sSimpleSummaryProvider;
        }

        @Override
        public CharSequence provideSummary(Preference preference) {
            if (pM.getSharedStringPreference("at4ed_securePinCode", true) == null) {
                return (preference.getContext().getString(R.string.preference_not_set));
            } else {
                return "Set";
            }
        }
    }

    // Removed onPreferenceDisplayDialog() method

    // Removed HighContrastListPreferenceDialogFragment class
}
