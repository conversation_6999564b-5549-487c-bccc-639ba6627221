package uk.ac.bournemouthuniversity.at4send.extensions.ui;

import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.graphics.BlendMode;
import android.graphics.BlendModeColorFilter;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.StateListDrawable;
import android.os.Build;

import androidx.annotation.ColorInt;
import androidx.core.content.res.ResourcesCompat;

import uk.ac.bournemouthuniversity.at4send.R;

public class UIUtils {

    /**
     * Dynamically produce Thumb Drawable for IndicatorSeekBar Custom View
     *
     * @param color color to mutate default thumb drawable to
     * @return {@link StateListDrawable} ThumbDrawable
     */
    @SuppressWarnings("deprecation")
    public static StateListDrawable makeThumbDrawable(Resources resources, Resources.Theme theme, @ColorInt int color){
        StateListDrawable res = new StateListDrawable();
        res.addState(new int[]{android.R.attr.state_pressed}, ResourcesCompat.getDrawable(resources, R.drawable.selected_thumb, theme));

        Drawable drawable = ResourcesCompat.getDrawable(resources, R.drawable.default_thumb, null);

        if(drawable != null) {
            if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                drawable.mutate().setColorFilter(new BlendModeColorFilter(color, BlendMode.SRC_IN));
            }else{
                drawable.mutate().setColorFilter(color, PorterDuff.Mode.SRC_IN);
            }
        }
        res.addState(new int[]{}, drawable);

        return res;
    }

    /**
     * Dynamically produce Track Tick color for IndicatorSeekBar Custom View
     *
     * @param color color to mutate default color states to
     * @return {@link ColorStateList} ColorStateList
     */
    public static ColorStateList makeTickMarkColorStateList(Resources resources, @ColorInt int color){
        int[][] states = new int[][] {
                new int[] { android.R.attr.state_selected},
                new int[]{}
        };

        int[] colors = new int[] {
                color,
                ResourcesCompat.getColor(resources, R.color.md_grey_300, null)
        };

        return new ColorStateList(states, colors);
    }
}
