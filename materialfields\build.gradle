// Library top buildscript
buildscript {
    ext.kotlin_version = '1.9.22'
    ext.dokka_version = '1.9.10'
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.2.1'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "org.jetbrains.dokka:dokka-gradle-plugin:$dokka_version"
    }
}

plugins {
    id 'com.android.library'
    id 'kotlin-android'
    id 'org.jetbrains.dokka' version '1.9.10'
    id 'maven-publish'
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply from: 'versioning.gradle'

android {
    namespace = "com.gabrielepmattia.materialfields"  // Fixed namespace to match package name
    compileSdkVersion 35

    buildFeatures {
        viewBinding = true
    }

    defaultConfig {
        minSdkVersion 30
        targetSdkVersion 35
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = '11'
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlin_version"
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'androidx.recyclerview:recyclerview:1.4.0'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test:runner:1.6.2'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.2.1'
}

repositories {
    mavenCentral()
}

tasks.dokkaHtml.configure {
    outputDirectory.set(file("$buildDir/javadoc"))
    dokkaSourceSets {
        named("main") {
            noAndroidSdks.set(false)
            includeNonPublic.set(false)
            packageOptions {
                prefix.set("android")
                suppress.set(true)
            }
        }
    }
}

task wrapper(type: Wrapper) {
    gradleVersion = '4.10.1' 
}




