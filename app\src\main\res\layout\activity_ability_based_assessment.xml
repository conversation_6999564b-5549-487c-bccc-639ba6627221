<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/md_grey_50"
    android:id="@+id/mainView"
    tools:context=".recommendations.AbilityBasedAssessmentActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:elevation="0dp">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            app:contentInsetStartWithNavigation="0dp"
            app:titleTextAppearance="@style/AT4SEND.AppTheme.TextAppearance.Title"
            app:title="Ability-Based Assessment"
            app:theme="@style/AT4SEND.BlueTheme.NoActionBar.Toolbar">

        </androidx.appcompat.widget.Toolbar>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardBackgroundColor="@color/md_white_1000"
                    app:cardCornerRadius="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:gravity="center_vertical"
                            android:paddingStart="16dp"
                            android:paddingEnd="16dp"
                            android:paddingTop="8dp"
                            android:paddingBottom="8dp">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/assessment_instructions"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Indicate the ease of performing each ability below by tapping the ratings on the scales:"
                                android:textSize="16sp"/>
                        </LinearLayout>

                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardBackgroundColor="@color/md_white_1000"
                    app:cardCornerRadius="0dp"
                    app:strokeWidth="1dp"
                    app:strokeColor="@color/md_grey_200"
                    app:cardElevation="0dp"
                    android:layout_marginTop="20dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/mainRecyclerView"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            tools:listitem="@layout/item_ability_rating_scale"/>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="@color/md_grey_200"
                            android:layout_marginTop="-2dp"/>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:gravity="center_vertical"
                            android:paddingStart="16dp"
                            android:paddingEnd="16dp"
                            android:paddingTop="12dp"
                            android:paddingBottom="12dp"
                            android:background="@color/md_white_1000">

                            <com.google.android.material.button.MaterialButton
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:letterSpacing="0"
                                android:textStyle="bold"
                                android:onClick="submitAssessment"
                                style="@style/Widget.MaterialComponents.Button.UnelevatedButton"
                                android:text="FIND ASSISTIVE TECHNOLOGIES" />

                            <com.google.android.material.button.MaterialButton
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:letterSpacing="0"
                                style="@style/Widget.MaterialComponents.Button.UnelevatedButton"
                                android:text="RETURN TO ASSESSMENT MENU"
                                app:backgroundTint="@color/md_orange_500"
                                android:onClick="returnHome"/>

                        </LinearLayout>

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>