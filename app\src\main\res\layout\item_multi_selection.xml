<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/itemView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:foreground="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true"
        android:orientation="horizontal"
        android:minHeight="56dp"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:paddingStart="16dp"
        android:paddingEnd="10dp"
        android:gravity="center_vertical">

        <!-- Was AutoResizeTextView -->
        <androidx.appcompat.widget.AppCompatCheckedTextView
            android:id="@+id/itemText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:singleLine="true"
            android:textStyle="bold"
            tools:text="Long Long Long Item Name"
            android:textColor="@color/black"
            android:gravity="center_vertical"
            android:checkMark="@null"
            android:drawableEnd="?android:attr/listChoiceIndicatorMultiple"/>

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginStart="16dp"
        android:background="@color/md_grey_200"/>
</LinearLayout>
