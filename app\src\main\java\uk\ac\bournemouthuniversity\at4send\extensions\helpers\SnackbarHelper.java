package uk.ac.bournemouthuniversity.at4send.extensions.helpers;

import android.content.Context;
import android.view.Gravity;
import android.view.View;

import com.google.android.material.snackbar.Snackbar;

import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.core.content.res.ResourcesCompat;
import androidx.core.view.ViewCompat;

import uk.ac.bournemouthuniversity.at4send.R;

/**
 * Snackbar Helper
 *
 * Amends the default SnackBar to include material styling
 *
 * @owner <PERSON>
 * @version 1.2
 * <AUTHOR> Creative UK
 * @copyright 2019
 */
public class SnackbarHelper {

    public static void configSnackbar(Context context, Snackbar snack) {
        addMargins(snack);
        setRoundBordersBg(context, snack);
        ViewCompat.setElevation(snack.getView(), 6f);
    }

    private static void addMargins(Snackbar snack) {
        snack.getView().addOnAttachStateChangeListener(new View.OnAttachStateChangeListener() {
            @Override
            public void onViewAttachedToWindow(View v) {
                CoordinatorLayout.LayoutParams params = new CoordinatorLayout.LayoutParams(CoordinatorLayout.LayoutParams.MATCH_PARENT, CoordinatorLayout.LayoutParams.WRAP_CONTENT);
                params.setMargins(params.leftMargin + 24, params.topMargin, params.rightMargin + 24, params.bottomMargin + 36);
                params.gravity = Gravity.BOTTOM;

                v.setLayoutParams(params);
                v.requestLayout();
            }

            @Override
            public void onViewDetachedFromWindow(View v) { }
        });
    }

    private static void setRoundBordersBg(Context context, Snackbar snackbar) {
        snackbar.getView().setBackground(ResourcesCompat.getDrawable(context.getResources(), R.drawable.snackbar_ui, null));
    }
}