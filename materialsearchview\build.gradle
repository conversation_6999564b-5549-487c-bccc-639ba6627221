plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

android {
    namespace = "com.shahroz.svlibrary"
    compileSdkVersion 35

    defaultConfig {
        minSdkVersion 30
        targetSdkVersion 35
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = '11'
        freeCompilerArgs += [
            '-Xskip-metadata-version-check'
        ]
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    testImplementation 'junit:junit:4.13.2'

    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation "androidx.cardview:cardview:1.0.0"
    implementation 'androidx.recyclerview:recyclerview:1.4.0'
    implementation 'com.google.android.material:material:1.12.0'
}




