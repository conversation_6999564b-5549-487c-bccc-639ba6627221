package uk.ac.bournemouthuniversity.at4send.extensions.ui.transforms;

import android.view.View;

import androidx.viewpager2.widget.ViewPager2;

public class GateTransformation implements ViewPager2.PageTransformer{
    private static final String TAG  = "GateTransformation";

    @Override
    public void transformPage(View page, float position) {
        page.setTranslationX(-position * page.getWidth());

        if (position < -1) {
            page.setAlpha(0);
        } else if (position <= 0) {
            page.setAlpha(1);
            page.setPivotX(0);
            page.setRotationY(90 * Math.abs(position));
        } else if (position <= 1) {
            page.setAlpha(1);
            page.setPivotX(page.getWidth());
            page.setRotationY(-90 * Math.abs(position));
        } else {
            page.setAlpha(0);
        }
    }

}