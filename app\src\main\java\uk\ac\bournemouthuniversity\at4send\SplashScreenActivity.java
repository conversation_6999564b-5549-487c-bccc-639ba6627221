package uk.ac.bournemouthuniversity.at4send;

import uk.ac.bournemouthuniversity.at4send.BaseActivity;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;

import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseUser;

import uk.ac.bournemouthuniversity.at4send.account.FirebaseUIActivity;
import uk.ac.bournemouthuniversity.at4send.extensions.helpers.PreferenceManager;
import uk.ac.bournemouthuniversity.at4send.security.EnterPinCodeActivity;

/**
 * Splash Screen Activity
 *
 * @since 0.0.1
 * @owner <PERSON>
 * <AUTHOR> Creative UK
 * @copyright 2019
 */

public class SplashScreenActivity extends BaseActivity {

    boolean hasPinCode;
    PreferenceManager pM;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_splash_screen);



        FirebaseUser user = FirebaseAuth.getInstance().getCurrentUser();
        if (user == null) {
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                startActivity(new Intent(this, FirebaseUIActivity.class));
                finish();
            }, 1000);
        }else {

            pM = new PreferenceManager(this);

            boolean secureApp = pM.getSharedBooleanPreference("secureApp", false, false);
            boolean pinCodeAvail = pM.getSharedStringPreference("at4ed_securePinCode", true) != null;

            hasPinCode = (secureApp && pinCodeAvail);

            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                if (hasPinCode) {
                    Intent i = new Intent(this, EnterPinCodeActivity.class);
                    i.putExtra(EnterPinCodeActivity.EXTRA_SUCCESS_INTENT, HomeActivity.class);
                    startActivity(i);
                } else {
                    startActivity(new Intent(this, HomeActivity.class));
                }
                finish();
            }, 1000);
        }
    }
}

