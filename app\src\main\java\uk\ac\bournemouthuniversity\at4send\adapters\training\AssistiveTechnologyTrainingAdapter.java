package uk.ac.bournemouthuniversity.at4send.adapters.training;

import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;

import java.util.List;

import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.models.training.AssistiveTechnologyTraining;
import uk.ac.bournemouthuniversity.at4send.technologies.training.ViewAssistiveTechnologyTrainingActivity;
import uk.ac.bournemouthuniversity.at4send.utils.ContrastUtils;
import uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils;

public class AssistiveTechnologyTrainingAdapter extends RecyclerView.Adapter<AssistiveTechnologyTrainingAdapter.ViewHolder> {
    private static final String TAG = "AssistiveTechnologyTrainingAdapter";

    private List<AssistiveTechnologyTraining> technologyList;
    private Context context;

    public class ViewHolder extends RecyclerView.ViewHolder {
        AppCompatTextView technologyName;
        AppCompatImageView technologyIcon;
        LinearLayout itemView;
        public boolean selected = false;

        public ViewHolder(View view) {
            super(view);
            itemView = view.findViewById(R.id.itemView);
            technologyName = view.findViewById(R.id.technologyName);
            technologyIcon = view.findViewById(R.id.technologyIcon);
        }
    }

    public AssistiveTechnologyTrainingAdapter(Context context, List<AssistiveTechnologyTraining> technologyList) {
        this.technologyList = technologyList;
        this.context = context;
    }

    @NonNull
    @Override
    public AssistiveTechnologyTrainingAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_assistive_technology_training, parent, false);

        return new AssistiveTechnologyTrainingAdapter.ViewHolder(itemView);
    }

    @Override
    public void onBindViewHolder(@NonNull final AssistiveTechnologyTrainingAdapter.ViewHolder holder, final int position) {
        final AssistiveTechnologyTraining item = technologyList.get(position);

        holder.technologyName.setText(item.getTechnologyName());

        if(!item.getIconUrl().equals("")){
            Glide.with(context).load(item.getIconUrl()).into(holder.technologyIcon);
        }

        holder.itemView.setOnClickListener(v -> {
            Intent i = new Intent(context, ViewAssistiveTechnologyTrainingActivity.class);
            i.putExtra(ViewAssistiveTechnologyTrainingActivity.EXTRA_TECHNOLOGY, item.toString());
            context.startActivity(i);
        });
        
        // Apply high contrast if enabled
        if (ContrastUtils.isHighContrastEnabled(context)) {
            // Apply text color to the technology name
            holder.technologyName.setTextColor(TextSizeUtils.getTextColor(context).getDefaultColor());
            
            // Apply tint to the icon
            holder.technologyIcon.setColorFilter(TextSizeUtils.getTextColor(context).getDefaultColor());
            
            // Apply background color to the item view
            holder.itemView.setBackgroundTintList(TextSizeUtils.getBackgroundColor(context));
        }
    }

    @Override
    public int getItemCount() {
        return technologyList.size();
    }

}

