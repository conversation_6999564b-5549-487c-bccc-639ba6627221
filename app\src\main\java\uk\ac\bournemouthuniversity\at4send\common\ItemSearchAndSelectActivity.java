package uk.ac.bournemouthuniversity.at4send.common;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import uk.ac.bournemouthuniversity.at4send.BaseActivity;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.afollestad.materialdialogs.MaterialDialog;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.snackbar.Snackbar;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.shahroz.svlibrary.interfaces.onSearchListener;
import com.shahroz.svlibrary.widgets.MaterialSearchView;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.adapters.ItemSelectionAdapter;
import uk.ac.bournemouthuniversity.at4send.api.AT4SENDAPIClient;
import uk.ac.bournemouthuniversity.at4send.api.AT4SENDAPIInterface;
import uk.ac.bournemouthuniversity.at4send.extensions.enums.SelectionOrdinal;
import uk.ac.bournemouthuniversity.at4send.extensions.helpers.SnackbarHelper;
import uk.ac.bournemouthuniversity.at4send.extensions.ui.Dialogs;
import uk.ac.bournemouthuniversity.at4send.extensions.utils.ArrayListAnySize;
import uk.ac.bournemouthuniversity.at4send.extensions.utils.Network;
import uk.ac.bournemouthuniversity.at4send.extensions.utils.Triple;
import uk.ac.bournemouthuniversity.at4send.models.AssistiveTechnology;
import uk.ac.bournemouthuniversity.at4send.models.Condition;
import uk.ac.bournemouthuniversity.at4send.models.Manufacturer;
import uk.ac.bournemouthuniversity.at4send.models.response.ConditionsResponse;
import uk.ac.bournemouthuniversity.at4send.models.response.ManufacturerResponse;
import uk.ac.bournemouthuniversity.at4send.models.response.TechnologiesResponse;

public class ItemSearchAndSelectActivity extends BaseActivity implements onSearchListener {
    private static final String TAG = "ItemSearchAndSelect";

    public static final String EXTRA_LIST_TYPE = "listType";

    public static final String EXTRA_MANUFACTURER_FILTER = "manufacturerFilter";
    public static final String EXTRA_MANUFACTURER_FILTER_NAME = "manufacturerFilterName";

    public static final String EXTRA_ITEM_CHOSEN_NAMES = "chosenItemNames";
    public static final String EXTRA_ITEM_CHOSEN_IDS = "chosenItemIDs";
    public static final String EXTRA_ITEM_CHOSEN_ORDINAL = "chosenItemOrdinal";

    public static final int LIST_TYPE_TECHNOLOGY = 1;
    public static final int LIST_TYPE_MANUFACTURER = 2;
    public static final int LIST_TYPE_CONDITION = 3;

    private int listType = -1;

    private int manufacturerFilter = -1;
    private String manufacturerFilterName = "";

    private List<Triple<Integer, String, Boolean>> mItemList = new ArrayListAnySize<>();

    private List<Integer> mSelectedIDs = new ArrayListAnySize<>();
    private List<String> mSelectedNames = new ArrayListAnySize<>();

    private ItemSelectionAdapter mAdapter;


    private boolean mSearchViewAdded = false;
    private MaterialSearchView mSearchView;
    private WindowManager mWindowManager;

    private boolean searchActive = false;

    private FirebaseCrashlytics crashlytics = FirebaseCrashlytics.getInstance();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_item_search_and_select);
        
        // Apply high contrast if enabled
        if (uk.ac.bournemouthuniversity.at4send.utils.ContrastUtils.isHighContrastEnabled(this)) {
            applyHighContrastToUI();
        }

        Bundle extras = getIntent().getExtras();

        if(extras != null){
            listType = extras.getInt(EXTRA_LIST_TYPE, -1);
            manufacturerFilter = extras.getInt(EXTRA_MANUFACTURER_FILTER, -1);
            manufacturerFilterName = extras.getString(EXTRA_MANUFACTURER_FILTER_NAME, "");
        }else{
            setResult(RESULT_CANCELED);
            finish();
        }

        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        mWindowManager = (WindowManager) getSystemService(Context.WINDOW_SERVICE);
        mSearchView = new MaterialSearchView(this);
        mSearchView.setOnSearchListener(this);
        mSearchView.setHintText("Search");

        ActionBar ab = getSupportActionBar();

        if(ab != null){
            ab.setDisplayHomeAsUpEnabled(true);
            ab.setHomeAsUpIndicator(R.drawable.ic_close);
            switch (listType){
                case LIST_TYPE_TECHNOLOGY:
                    ab.setTitle("Select Assistive Technology");
                    break;
                case LIST_TYPE_CONDITION:
                    ab.setTitle("Select Condition(s)");
                    break;
                case LIST_TYPE_MANUFACTURER:
                    ab.setTitle("Select Manufacturer");
                    break;
            }

            if (toolbar != null) {
                toolbar.post(() -> {
                    if (!mSearchViewAdded && mWindowManager != null) {
                        mWindowManager.addView(mSearchView,
                                MaterialSearchView.getSearchViewLayoutParams(ItemSearchAndSelectActivity.this));
                        mSearchViewAdded = true;
                    }
                });
            }
        }

        mAdapter = new ItemSelectionAdapter(mItemList, new ItemSelectionAdapter.OnItemClickedListener() {
            @Override
            public void onItemClicked(Triple<Integer, String, Boolean> item) {
                super.onItemClicked(item);
                Intent data = new Intent();
                data.putExtra(EXTRA_ITEM_CHOSEN_IDS, item.getFirst());
                data.putExtra(EXTRA_ITEM_CHOSEN_NAMES, item.getSecond());
                data.putExtra(EXTRA_ITEM_CHOSEN_ORDINAL, SelectionOrdinal.SINGLE);
                setResult(RESULT_OK, data);
                finish();
            }

            @Override
            public void onMultiItemClicked(List<Integer> ids, List<String> names) {
                super.onMultiItemClicked(ids, names);
                mSelectedIDs = ids;
                mSelectedNames = names;
            }
        }, listType == LIST_TYPE_CONDITION);

        MaterialCardView bottomView = findViewById(R.id.bottomView);
        if(listType == LIST_TYPE_CONDITION){
            bottomView.setVisibility(View.VISIBLE);
        }

        RecyclerView recyclerView = findViewById(R.id.mainRecyclerView);
        recyclerView.setAdapter(mAdapter);
        recyclerView.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));

        MaterialCardView filterTextContainer = findViewById(R.id.filterTextContainer);
        AppCompatTextView filterText = findViewById(R.id.filterText);
        if(manufacturerFilter != -1){
            filterTextContainer.setVisibility(View.VISIBLE);
            filterText.setText(String.format("%s%s", "Below is the available Assistive Technologies from ", manufacturerFilterName));
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        
        // Reapply high contrast when returning to activity
        if (uk.ac.bournemouthuniversity.at4send.utils.ContrastUtils.isHighContrastEnabled(this)) {
            applyHighContrastToUI();
        }
        
        if(!Network.isNetworkAvailable(this)){
            new MaterialDialog(ItemSearchAndSelectActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                    .title(null, "Network Access Required")
                    .message(null, "An active network connection is required for this function. Please connect to the internet and try again.", null)
                    .cancelable(false)
                    .positiveButton(null, "Dismiss", materialDialog -> {
                        materialDialog.dismiss();
                        setResult(RESULT_CANCELED);
                        finish();
                        return null;
                    }).show();
            return;
        }

        retrieveRequiredList();
    }

    @Override
    public void finish() {
        if(mSearchView.isSearchViewVisible()){
            mSearchView.hide();
        }
        super.finish();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_item_search_activity, menu);

        MenuItem searchItem = menu.findItem(R.id.action_search);
        searchItem.setOnMenuItemClickListener(item -> {
            mSearchView.display();
            return true;
        });

        if(searchActive)
            mSearchView.display();

        return true;
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        int id = item.getItemId();

        if(id == android.R.id.home){
            setResult(RESULT_CANCELED);
            finish();
        }

        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onBackPressed() {
        if(mSearchView.isSearchViewVisible()){
            mSearchView.hide();
            return;
        }

        setResult(Activity.RESULT_CANCELED);
        finish();
    }

    private void retrieveRequiredList(){
        switch (listType){
            case -1:
                setResult(RESULT_CANCELED);
                finish();
                break;
            case LIST_TYPE_TECHNOLOGY:
                prepareTechnologiesList();
                break;
            case LIST_TYPE_MANUFACTURER:
                prepareManufacturerList();
                break;
            case LIST_TYPE_CONDITION:
                prepareConditionsList();
                break;
        }
    }

    private void prepareTechnologiesList(){
        MaterialDialog progressDialog = Dialogs.generateProgressDialog(this, "Loading...");
        progressDialog.show();

        mItemList.clear();
        mAdapter.notifyDataSetChanged();

        AT4SENDAPIInterface apiService =
                AT4SENDAPIClient.getClient().create(AT4SENDAPIInterface.class);


        Call<TechnologiesResponse> call;
        if(manufacturerFilter == -1) {
            call = apiService.getTechnologies(true);
        }else{
            call = apiService.getTechnologies(true, manufacturerFilter);
        }

        call.enqueue(new Callback<TechnologiesResponse>() {
            @Override
            public void onResponse(@NotNull Call<TechnologiesResponse>call, @NotNull Response<TechnologiesResponse> response) {
                if(response.body() != null) {
                    List<AssistiveTechnology> technologies = response.body().getAssistiveTechnologies();

                    if (technologies != null) {
                        Log.d(TAG, "Number of technologies received: " + technologies.size());
                        for (AssistiveTechnology technology : technologies) {
                            mItemList.add(new Triple<>(technology.getTechnologyID(), technology.getTechnologyName(), false));
                        }
                        mAdapter.notifyDataSetChanged();
                    }
                }else{
                    new MaterialDialog(ItemSearchAndSelectActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                            .title(null, "Error Occurred")
                            .message(null, "An error occurred whilst retrieving the technologies list. Please try again later.", null)
                            .cancelable(false)
                            .positiveButton(null, "Dismiss", materialDialog -> {
                                materialDialog.dismiss();
                                finish();
                                return null;
                            }).show();
                }
                progressDialog.dismiss();
            }

            @Override
            public void onFailure(@NotNull Call<TechnologiesResponse>call, @NotNull Throwable t) {
                // Log error here since request failed
                Log.e(TAG, t.toString());
                crashlytics.recordException(t);

                progressDialog.dismiss();

                new MaterialDialog(ItemSearchAndSelectActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                        .title(null, "Error Occurred")
                        .message(null, "An error occurred whilst retrieving the technologies list. Please try again later.", null)
                        .cancelable(false)
                        .positiveButton(null, "Dismiss", materialDialog -> {
                            materialDialog.dismiss();
                            finish();
                            return null;
                        }).show();
            }
        });
    }

    private void prepareManufacturerList(){
        MaterialDialog progressDialog = Dialogs.generateProgressDialog(this, "Loading...");
        progressDialog.show();

        mItemList.clear();
        mAdapter.notifyDataSetChanged();

        AT4SENDAPIInterface apiService =
                AT4SENDAPIClient.getClient().create(AT4SENDAPIInterface.class);

        Call<ManufacturerResponse> call = apiService.getManufacturers(true);
        call.enqueue(new Callback<ManufacturerResponse>() {
            @Override
            public void onResponse(@NotNull Call<ManufacturerResponse>call, @NotNull Response<ManufacturerResponse> response) {
                if(response.body() != null) {
                    List<Manufacturer> manufacturers = response.body().getManufacturers();

                    if (manufacturers != null) {
                        Log.d(TAG, "Number of manufacturers received: " + manufacturers.size());
                        for (Manufacturer manufacturer : manufacturers) {
                            mItemList.add(new Triple<>(manufacturer.getManufacturerID(), manufacturer.getManufacturerName(), false));
                        }
                        mAdapter.notifyDataSetChanged();
                    }
                }else{
                    new MaterialDialog(ItemSearchAndSelectActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                            .title(null, "Error Occurred")
                            .message(null, "An error occurred whilst retrieving the manufacturer list. Please try again later.", null)
                            .cancelable(false)
                            .positiveButton(null, "Dismiss", materialDialog -> {
                                materialDialog.dismiss();
                                finish();
                                return null;
                            }).show();
                }
                progressDialog.dismiss();
            }

            @Override
            public void onFailure(@NotNull Call<ManufacturerResponse>call, @NotNull Throwable t) {
                // Log error here since request failed
                Log.e(TAG, t.toString());
                crashlytics.recordException(t);

                progressDialog.dismiss();

                new MaterialDialog(ItemSearchAndSelectActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                        .title(null, "Error Occurred")
                        .message(null, "An error occurred whilst retrieving the manufacturer list. Please try again later.", null)
                        .cancelable(false)
                        .positiveButton(null, "Dismiss", materialDialog -> {
                            materialDialog.dismiss();
                            finish();
                            return null;
                        }).show();
            }
        });
    }

    private void prepareConditionsList(){
        MaterialDialog progressDialog = Dialogs.generateProgressDialog(this, "Loading...");
        progressDialog.show();

        mItemList.clear();
        mAdapter.notifyDataSetChanged();

        AT4SENDAPIInterface apiService =
                AT4SENDAPIClient.getClient().create(AT4SENDAPIInterface.class);

        Call<ConditionsResponse> call = apiService.getConditions(true);
        call.enqueue(new Callback<ConditionsResponse>() {
            @Override
            public void onResponse(@NotNull Call<ConditionsResponse>call, @NotNull Response<ConditionsResponse> response) {
                if(response.body() != null) {
                    List<Condition> conditions = response.body().getConditions();

                    if (conditions != null) {
                        Log.d(TAG, "Number of conditions received: " + conditions.size());
                        for (Condition condition : conditions) {
                            mItemList.add(new Triple<>(condition.getConditionID(), condition.getConditionName(), false));
                        }
                        mAdapter.notifyDataSetChanged();
                    }
                }else{
                    new MaterialDialog(ItemSearchAndSelectActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                            .title(null, "Error Occurred")
                            .message(null, "An error occurred whilst retrieving the conditions list. Please try again later.", null)
                            .cancelable(false)
                            .positiveButton(null, "Dismiss", materialDialog -> {
                                materialDialog.dismiss();
                                finish();
                                return null;
                            }).show();
                }

                progressDialog.dismiss();
            }

            @Override
            public void onFailure(@NotNull Call<ConditionsResponse>call, @NotNull Throwable t) {
                // Log error here since request failed
                Log.e(TAG, t.toString());
                crashlytics.recordException(t);

                progressDialog.dismiss();

                new MaterialDialog(ItemSearchAndSelectActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                        .title(null, "Error Occurred")
                        .message(null, "An error occurred whilst retrieving the manufacturer list. Please try again later.", null)
                        .cancelable(false)
                        .positiveButton(null, "Dismiss", materialDialog -> {
                            materialDialog.dismiss();
                            finish();
                            return null;
                        }).show();
            }
        });
    }

    @Override
    public void onSearch(String query) {
        mAdapter.getFilter().filter(query);
    }

    @Override
    public void searchViewOpened() {

    }

    @Override
    public void searchViewClosed() {

    }

    @Override
    public void onCancelSearch() {
        searchActive = false;
        mSearchView.hide();
    }

    public void confirmSelections(View view){
        if(mSelectedIDs.isEmpty() || mSelectedNames.isEmpty()){
            Snackbar sB = Snackbar.make(findViewById(R.id.mainView), "Please select at least 1 option to include.", Snackbar.LENGTH_LONG);
            SnackbarHelper.configSnackbar(this, sB);
            sB.show();
            return;
        }

        Intent data = new Intent();
        data.putIntegerArrayListExtra(EXTRA_ITEM_CHOSEN_IDS, (ArrayList<Integer>) mSelectedIDs);
        data.putStringArrayListExtra(EXTRA_ITEM_CHOSEN_NAMES, (ArrayList<String>) mSelectedNames);
        data.putExtra(EXTRA_ITEM_CHOSEN_ORDINAL, SelectionOrdinal.MULTIPLE);
        setResult(RESULT_OK, data);
        finish();
    }

    /**
     * Applies high contrast settings to the UI elements
     */
    private void applyHighContrastToUI() {
        // Apply contrast to the main view hierarchy
        uk.ac.bournemouthuniversity.at4send.utils.ContrastUtils.applyContrastToViewHierarchy(findViewById(android.R.id.content));
        
        // Apply contrast to RecyclerView items
        RecyclerView recyclerView = findViewById(R.id.mainRecyclerView);
        if (recyclerView != null) {
            // Apply to visible items
            for (int i = 0; i < recyclerView.getChildCount(); i++) {
                View itemView = recyclerView.getChildAt(i);
                if (itemView != null) {
                    uk.ac.bournemouthuniversity.at4send.utils.ContrastUtils.applyContrastToViewHierarchy(itemView);
                }
            }
            
            // Force adapter to refresh all items
            if (mAdapter != null) {
                mAdapter.notifyDataSetChanged();
            }
        }
        
        // Apply contrast to filter text
        AppCompatTextView filterText = findViewById(R.id.filterText);
        if (filterText != null) {
            filterText.setTextColor(uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils.getTextColor(this).getDefaultColor());
        }
        
        // Apply contrast to bottom view if visible
        MaterialCardView bottomView = findViewById(R.id.bottomView);
        if (bottomView != null && bottomView.getVisibility() == View.VISIBLE) {
            bottomView.setCardBackgroundColor(uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils.getBackgroundColor(this).getDefaultColor());
        }
    }
}
