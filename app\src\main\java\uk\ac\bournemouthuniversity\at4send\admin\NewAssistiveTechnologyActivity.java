package uk.ac.bournemouthuniversity.at4send.admin;

import android.app.Activity;
import android.content.Intent;
import android.content.res.ColorStateList;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.speech.tts.TextToSpeech;
import android.text.InputType;
import android.text.Spanned;
import android.text.Html;
import android.util.Log;
import android.util.Patterns;
import android.view.MenuItem;
import android.view.View;
import android.widget.CheckBox;
import android.widget.EditText;

import android.net.NetworkCapabilities;
import android.widget.TextView;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBar;
import uk.ac.bournemouthuniversity.at4send.BaseActivity;

import androidx.appcompat.widget.AppCompatCheckedTextView;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.core.content.res.ResourcesCompat;
import androidx.core.widget.CompoundButtonCompat;
import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.afollestad.materialdialogs.MaterialDialog;
import com.afollestad.materialdialogs.input.DialogInputExtKt;
import com.gabrielepmattia.materialfields.fields.FieldGenericNoIcon;
import com.gabrielepmattia.materialfields.fields.FieldInputTextNoIcon;
import com.google.android.material.button.MaterialButton;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import kotlin.Pair;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.adapters.AbilitiesAdapter;
import uk.ac.bournemouthuniversity.at4send.adapters.ConditionsSelectionAdapter;
import uk.ac.bournemouthuniversity.at4send.api.AT4SENDAPIClient;
import uk.ac.bournemouthuniversity.at4send.api.AT4SENDAPIInterface;
import uk.ac.bournemouthuniversity.at4send.common.ItemSearchAndSelectActivity;
import uk.ac.bournemouthuniversity.at4send.extensions.helpers.FieldUtils;
import uk.ac.bournemouthuniversity.at4send.extensions.ui.Dialogs;
import uk.ac.bournemouthuniversity.at4send.extensions.utils.ArrayListAnySize;
import uk.ac.bournemouthuniversity.at4send.extensions.utils.Network;
import uk.ac.bournemouthuniversity.at4send.models.Ability;
import uk.ac.bournemouthuniversity.at4send.models.AbilityGroup;
import uk.ac.bournemouthuniversity.at4send.models.Condition;
import uk.ac.bournemouthuniversity.at4send.models.response.AbilitiesGroupResponse;
import uk.ac.bournemouthuniversity.at4send.models.response.ConditionsResponse;
import uk.ac.bournemouthuniversity.at4send.models.response.StatusResponse;
import uk.ac.bournemouthuniversity.at4send.utils.ContrastUtils;
import uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils;
import uk.ac.bournemouthuniversity.at4send.utils.ReadAloudUtils;
import uk.ac.bournemouthuniversity.at4send.utils.TextToSpeechManager;

public class NewAssistiveTechnologyActivity extends BaseActivity {
    private static final String TAG = "AddTechnology";

    private static final int REQUEST_MANUFACTURER = 10001;

    private FieldInputTextNoIcon mName, mDescription;
    private FieldGenericNoIcon mManufacturer, mPrice, mWebsite;
    private int mManufacturerSelected;

    private List<Ability> abilityList = new ArrayListAnySize<>();
    private AbilitiesAdapter mAbilitiesAdapter;
    private RecyclerView mMainRecyclerViewAbilities;

    private List<Condition> conditionList = new ArrayListAnySize<>();
    private ConditionsSelectionAdapter mConditionsAdapter;
    private RecyclerView mMainRecyclerViewConditions;

    private CoordinatorLayout mMainView;
    private NestedScrollView mScrollView;

    private boolean saveAttempted = false;
    private boolean itemsChanged = false;

    private FirebaseCrashlytics crashlytics = FirebaseCrashlytics.getInstance();

    private final ActivityResultLauncher<Intent> manufacturerLauncher = registerForActivityResult(
        new ActivityResultContracts.StartActivityForResult(),
        result -> {
            if (result.getResultCode() == Activity.RESULT_OK) {
                Intent data = result.getData();
                if (data != null) {
                    Bundle extras = data.getExtras();
                    if (extras != null) {
                        mManufacturer.setValue(extras.getString(ItemSearchAndSelectActivity.EXTRA_ITEM_CHOSEN_NAMES, ""));
                        mManufacturerSelected = extras.getInt(ItemSearchAndSelectActivity.EXTRA_ITEM_CHOSEN_IDS, -1);
                        mManufacturer.setAlertState(false);
                    }
                }
            }
        }
    );

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_admin_assistive_technology);

        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        ActionBar ab = getSupportActionBar();

        if(ab != null){
            ab.setDisplayHomeAsUpEnabled(true);
            ab.setTitle("Add Assistive Technology");
        }

        mMainView = findViewById(R.id.mainView);
        mScrollView = findViewById(R.id.mainScrollView);

        mName = findViewById(R.id.fieldTechnologyName);
        mDescription = findViewById(R.id.fieldTechnologyDescription);
        
        // Add click listener for the name field to show dialog with high contrast support
        mName.setOnClickListener(v -> {
            MaterialDialog dialog = DialogInputExtKt.input(
                new MaterialDialog(NewAssistiveTechnologyActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR()), 
                "Name", 
                null, 
                mName.getValue(), 
                null, 
                InputType.TYPE_CLASS_TEXT, 
                null, 
                true, 
                false, 
                (materialDialog, charSequence) -> null
            ).title(null, "Technology Name")
            .positiveButton(null, "OK", materialDialog -> {
                EditText field = DialogInputExtKt.getInputField(materialDialog);
                String val = field.getText().toString();
                mName.setValue(val);
                materialDialog.dismiss();
                return null;
            }).negativeButton(null, "Cancel", materialDialog -> {
                materialDialog.dismiss();
                return null;
            });
            
            // Apply high contrast if enabled
            if (ContrastUtils.isHighContrastEnabled(NewAssistiveTechnologyActivity.this)) {
                ContrastUtils.applyContrastToDialog(dialog);
                
                // Also apply high contrast to the input field
                EditText inputField = DialogInputExtKt.getInputField(dialog);
                if (inputField != null) {
                    inputField.setTextColor(TextSizeUtils.getTextColor(NewAssistiveTechnologyActivity.this).getDefaultColor());
                    inputField.setHintTextColor(TextSizeUtils.getTextColor(NewAssistiveTechnologyActivity.this).withAlpha(128).getDefaultColor());
                }
            }
            
            dialog.show();
        });

        mDescription.setOnClickListener(v -> {
            MaterialDialog dialog = DialogInputExtKt.input(
                new MaterialDialog(NewAssistiveTechnologyActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR()), 
                "Description", 
                null, 
                mDescription.getValue(), 
                null, 
                InputType.TYPE_CLASS_TEXT, 
                null, 
                true, 
                false, 
                (materialDialog, charSequence) -> null
            ).title(null, "Technology Description")
            .positiveButton(null, "OK", materialDialog -> {
                EditText field = DialogInputExtKt.getInputField(materialDialog);
                String val = field.getText().toString();
                mDescription.setValue(val);
                materialDialog.dismiss();
                return null;
            }).negativeButton(null, "Cancel", materialDialog -> {
                materialDialog.dismiss();
                return null;
            });
            
            // Apply high contrast if enabled
            if (ContrastUtils.isHighContrastEnabled(NewAssistiveTechnologyActivity.this)) {
                ContrastUtils.applyContrastToDialog(dialog);
                
                // Also apply high contrast to the input field
                EditText inputField = DialogInputExtKt.getInputField(dialog);
                if (inputField != null) {
                    inputField.setTextColor(TextSizeUtils.getTextColor(NewAssistiveTechnologyActivity.this).getDefaultColor());
                    inputField.setHintTextColor(TextSizeUtils.getTextColor(NewAssistiveTechnologyActivity.this).withAlpha(128).getDefaultColor());
                }
            }
            
            dialog.show();
        });



        mManufacturer = findViewById(R.id.fieldTechnologyManufacturer);
        mWebsite = findViewById(R.id.fieldTechnologyWebsite);
        mPrice = findViewById(R.id.fieldTechnologyRRP);

        mManufacturer.setOnClickListener(v -> {
            Intent i = new Intent(this, ItemSearchAndSelectActivity.class);
            i.putExtra(ItemSearchAndSelectActivity.EXTRA_LIST_TYPE, ItemSearchAndSelectActivity.LIST_TYPE_MANUFACTURER);
            manufacturerLauncher.launch(i);
        });

        mWebsite.setOnClickListener(v -> {
            MaterialDialog dialog = DialogInputExtKt.input(
                new MaterialDialog(NewAssistiveTechnologyActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR()), 
                "Website", 
                null, 
                mWebsite.getValue(), 
                null, 
                InputType.TYPE_TEXT_VARIATION_URI, 
                null, 
                true, 
                false, 
                (materialDialog, charSequence) -> null
            ).title(null, "Technology Website")
            .positiveButton(null, "OK", materialDialog -> {
                EditText field = DialogInputExtKt.getInputField(materialDialog);
                String val = field.getText().toString();

                if(Patterns.WEB_URL.matcher(val).matches()) {
                    mWebsite.setValue(val);
                    materialDialog.dismiss();
                } else {
                    field.setError("Please enter a valid website address");
                }

                return null;
            }).negativeButton(null, "Cancel", materialDialog -> {
                materialDialog.dismiss();
                return null;
            }).noAutoDismiss();
            
            // Apply high contrast if enabled
            if (ContrastUtils.isHighContrastEnabled(NewAssistiveTechnologyActivity.this)) {
                ContrastUtils.applyContrastToDialog(dialog);
                
                // Also apply high contrast to the input field
                EditText inputField = DialogInputExtKt.getInputField(dialog);
                if (inputField != null) {
                    inputField.setTextColor(TextSizeUtils.getTextColor(NewAssistiveTechnologyActivity.this).getDefaultColor());
                    inputField.setHintTextColor(TextSizeUtils.getTextColor(NewAssistiveTechnologyActivity.this).withAlpha(128).getDefaultColor());
                }
            }
            
            dialog.show();
        });

        mPrice.setOnClickListener(v -> {
            String value = mPrice.getValue();
            if(value.contains("£")){
                value = value.substring(value.indexOf("£")+1);
            }

            MaterialDialog dialog1 = DialogInputExtKt.input(new MaterialDialog(NewAssistiveTechnologyActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR()), "Price", null, value, null, InputType.TYPE_NUMBER_FLAG_DECIMAL | InputType.TYPE_CLASS_NUMBER, null, true, false, (materialDialog, charSequence) -> null).title(null, "Technology Recommended Retail Price")
            .positiveButton(null, "OK", materialDialog -> {
                mPrice.setValue("£"+DialogInputExtKt.getInputField(materialDialog).getText().toString());
                materialDialog.dismiss();
                return null;
            }).negativeButton(null, "Cancel", materialDialog -> {
                materialDialog.dismiss();
                return null;
            });
            if (ContrastUtils.isHighContrastEnabled(this)) {
                ContrastUtils.applyContrastToDialog(dialog1);
            }

            DialogInputExtKt.getInputField(dialog1).setSelectAllOnFocus(true);

            dialog1.show();
        });

        MaterialButton submitTechnology = findViewById(R.id.submitTechnology);
        submitTechnology.setOnClickListener(v -> createAssistiveTechnology());

        MaterialButton returnHome = findViewById(R.id.returnHome);
        returnHome.setOnClickListener(v -> returnToAdminHome());

        mAbilitiesAdapter = new AbilitiesAdapter(this, abilityList, () -> itemsChanged = true, true);

        mMainRecyclerViewAbilities = findViewById(R.id.mainRecyclerViewAbilities);
        mMainRecyclerViewAbilities.setAdapter(mAbilitiesAdapter);
        mMainRecyclerViewAbilities.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));

        mConditionsAdapter = new ConditionsSelectionAdapter(this, conditionList, () -> itemsChanged = true);

        mMainRecyclerViewConditions = findViewById(R.id.mainRecyclerViewConditions);
        mMainRecyclerViewConditions.setAdapter(mConditionsAdapter);
        mMainRecyclerViewConditions.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));


        // Add read-aloud button to instructions text
        TextView instructionsText = findViewById(R.id.ability_mapping_description);
        if (instructionsText != null) {
            ReadAloudUtils.addReadAloudButton(this, instructionsText);
        }

        TextView instructionsText2 = findViewById(R.id.condition_mapping_description);
        if (instructionsText2 != null) {
            ReadAloudUtils.addReadAloudButton(this, instructionsText2);
        }

        if(!Network.isNetworkAvailable(this)){
            MaterialDialog dialog = new MaterialDialog(NewAssistiveTechnologyActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                    .title(null, "Network Access Required")
                    .message(null, "An active network connection is required for this function. Please connect to the internet and try again.", null)
                    .cancelable(false)
                    .positiveButton(null, "Dismiss", materialDialog -> {
                        materialDialog.dismiss();
                        finish();
                        return null;
                    });

            if (ContrastUtils.isHighContrastEnabled(this)) {
                ContrastUtils.applyContrastToDialog(dialog);
            }
            
            dialog.show();
            return;
        }

        retrieveAbilitiesList();
        retrieveConditionsList();
    }

    @Override
    public void finish() {
        super.finish();
        overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_right);
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        int itemId = item.getItemId();

        if(itemId == android.R.id.home){
            onBackPressed();
            return true;
        }

        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onBackPressed() {
        if(FieldUtils.hasChanged(mName, mDescription, mManufacturer, mWebsite, mPrice) || saveAttempted || itemsChanged){
            MaterialDialog dialog = new MaterialDialog(this, MaterialDialog.getDEFAULT_BEHAVIOR())
                    .title(null, "Confirmation")
                    .message(null, "Are you sure you want to return to Administration Options without adding the Assistive Technology, all entered information will be lost.", null)
                    .positiveButton(null, "No, Cancel", materialDialog -> {
                        materialDialog.dismiss();
                        return null;
                    })
                    .negativeButton(null, "Yes, Return", materialDialog -> {
                        super.onBackPressed();
                        materialDialog.dismiss();
                        return null;
                    });
            
            if (ContrastUtils.isHighContrastEnabled(this)) {
                ContrastUtils.applyContrastToDialog(dialog);
            }
            
            dialog.show();
        }else {
            super.onBackPressed();
        }
    }

    private void retrieveAbilitiesList(){
        MaterialDialog progressDialog = Dialogs.generateProgressDialog(this, "Loading...");
        progressDialog.show();
        if (ContrastUtils.isHighContrastEnabled(this)) {
            ContrastUtils.applyContrastToDialog(progressDialog);
        }

        AT4SENDAPIInterface apiService =
                AT4SENDAPIClient.getClient().create(AT4SENDAPIInterface.class);

        Call<AbilitiesGroupResponse> call = apiService.getAbilitiesWithGroups(true);
        call.enqueue(new Callback<AbilitiesGroupResponse>() {
            @Override
            public void onResponse(@NotNull Call<AbilitiesGroupResponse>call, @NotNull Response<AbilitiesGroupResponse> response) {
                if(response.body() != null) {
                    List<AbilityGroup> abilityGroups = response.body().getAbilityGroups();

                    if (abilityGroups != null) {
                        Log.d(TAG, "Number of ability groups received: " + abilityGroups.size());
                        populateAbilitiesList(abilityGroups);
                    }
                }else{
                    MaterialDialog dialog = new MaterialDialog(NewAssistiveTechnologyActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                            .title(null, "Error Occurred")
                            .message(null, "An error occurred whilst retrieving the ability list. Please try again later.", null)
                            .cancelable(false)
                            .positiveButton(null, "Dismiss", materialDialog -> {
                                materialDialog.dismiss();
                                finish();
                                return null;
                            });

                    if (ContrastUtils.isHighContrastEnabled(NewAssistiveTechnologyActivity.this)) {
                        ContrastUtils.applyContrastToDialog(dialog);
                    }
                    
                    dialog.show();
                }

                progressDialog.dismiss();
            }

            @Override
            public void onFailure(@NotNull Call<AbilitiesGroupResponse>call, @NotNull Throwable t) {
                // Log error here since request failed
                Log.e(TAG, t.toString());
                crashlytics.recordException(t);

                progressDialog.dismiss();

                MaterialDialog dialog = new MaterialDialog(NewAssistiveTechnologyActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                        .title(null, "Error Occurred")
                        .message(null, "An error occurred whilst retrieving the ability list. Please try again later.", null)
                        .cancelable(false)
                        .positiveButton(null, "Dismiss", materialDialog -> {
                            materialDialog.dismiss();
                            finish();
                            return null;
                        });
                        
                if (ContrastUtils.isHighContrastEnabled(NewAssistiveTechnologyActivity.this)) {
                    ContrastUtils.applyContrastToDialog(dialog);
                }
                
                dialog.show();
            }
        });
    }

    private void populateAbilitiesList(List<AbilityGroup> abilityGroups){
        abilityList.clear();
        mAbilitiesAdapter.notifyDataSetChanged();

        for(AbilityGroup group : abilityGroups){
            abilityList.add(new Ability(group.getGroupName(), Ability.VIEW_TYPE_HEAD));
            abilityList.addAll(group.getGroupAbilities());
        }

        mAbilitiesAdapter.notifyDataSetChanged();
        
        // Apply high contrast if enabled
        if (ContrastUtils.isHighContrastEnabled(this)) {
            applyHighContrastToRecyclerViews();
        }
    }

    private void retrieveConditionsList(){
        MaterialDialog progressDialog = Dialogs.generateProgressDialog(this, "Loading...");
        progressDialog.show();

        conditionList.clear();
        mConditionsAdapter.notifyDataSetChanged();

        AT4SENDAPIInterface apiService =
                AT4SENDAPIClient.getClient().create(AT4SENDAPIInterface.class);

        Call<ConditionsResponse> call = apiService.getConditions(true);
        call.enqueue(new Callback<ConditionsResponse>() {
            @Override
            public void onResponse(@NotNull Call<ConditionsResponse>call, @NotNull Response<ConditionsResponse> response) {
                if(response.body() != null) {
                    List<Condition> conditions = response.body().getConditions();

                    if (conditions != null) {
                        Log.d(TAG, "Number of conditions received: " + conditions.size());
                        conditionList.addAll(conditions);
                        mConditionsAdapter.notifyDataSetChanged();
                    }
                }else{
                    MaterialDialog dialog = new MaterialDialog(NewAssistiveTechnologyActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                            .title(null, "Error Occurred")
                            .message(null, "An error occurred whilst retrieving the conditions list. Please try again later.", null)
                            .cancelable(false)
                            .positiveButton(null, "Dismiss", materialDialog -> {
                                materialDialog.dismiss();
                                finish();
                                return null;
                            });
                            
                    if (ContrastUtils.isHighContrastEnabled(NewAssistiveTechnologyActivity.this)) {
                        ContrastUtils.applyContrastToDialog(dialog);
                    }
                    
                    dialog.show();
                }
                progressDialog.dismiss();
            }

            @Override
            public void onFailure(@NotNull Call<ConditionsResponse>call, @NotNull Throwable t) {
                // Log error here since request failed
                Log.e(TAG, t.toString());
                crashlytics.recordException(t);

                progressDialog.dismiss();

                MaterialDialog dialog = new MaterialDialog(NewAssistiveTechnologyActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                        .title(null, "Error Occurred")
                        .message(null, "An error occurred whilst retrieving the conditions list. Please try again later.", null)
                        .cancelable(false)
                        .positiveButton(null, "Dismiss", materialDialog -> {
                            materialDialog.dismiss();
                            finish();
                            return null;
                        });
                        
                if (ContrastUtils.isHighContrastEnabled(NewAssistiveTechnologyActivity.this)) {
                    ContrastUtils.applyContrastToDialog(dialog);
                }
                
                dialog.show();
            }
        });
    }

    private void createAssistiveTechnology() {
        saveAttempted = true;

        if(!FieldUtils.validateFields(this, mMainView, mName, mDescription, mWebsite, mPrice, mManufacturer)){
            mScrollView.smoothScrollTo(0,0);
            return;
        }

        String technologyName = mName.getValue();
        String technologyDescription = mDescription.getValue();
        String technologyWebsite = mWebsite.getValue();
        String technologyPrice = mPrice.getValue();
        double technologyRRP = Double.parseDouble(technologyPrice.substring(technologyPrice.indexOf("£") + 1));
        int technologyManufacturerID = mManufacturerSelected;

        List<Pair<Integer, Integer>> abilityLinks = new ArrayList<>();

        for (int i = 0; i < mMainRecyclerViewAbilities.getChildCount(); i++) {
            if(mMainRecyclerViewAbilities.findViewHolderForAdapterPosition(i) instanceof AbilitiesAdapter.ItemViewHolder) {
                AbilitiesAdapter.ItemViewHolder holder = (AbilitiesAdapter.ItemViewHolder) mMainRecyclerViewAbilities.findViewHolderForAdapterPosition(i);
                if (holder != null) {
                    if(holder.ratingSeekBar.getProgress() != -1) {
                        abilityLinks.add(new Pair<>(abilityList.get(i).getAbilityID(), holder.ratingSeekBar.getProgress()));
                    }
                }
            }
        }

        Map<String, Object> abilityMap = new LinkedHashMap<>();
        for(int j = 0; j < abilityLinks.size(); j++){
            abilityMap.put("ability_links[" + abilityLinks.get(j).getFirst() + "]", abilityLinks.get(j).getSecond());
        }

        List<Integer> conditionLinks = new ArrayList<>();

        for (int i = 0; i < mMainRecyclerViewConditions.getChildCount(); i++) {
            ConditionsSelectionAdapter.ItemViewHolder holder = (ConditionsSelectionAdapter.ItemViewHolder) mMainRecyclerViewConditions.findViewHolderForAdapterPosition(i);
            if (holder != null) {
                if (holder.conditionItem.isChecked()) {
                    conditionLinks.add(conditionList.get(i).getConditionID());
                }
            }
        }

        if (conditionLinks.size() == 0 && abilityLinks.size() == 0) {
            MaterialDialog dialog = new MaterialDialog(NewAssistiveTechnologyActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                    .title(null, "Error")
                    .message(null, "You have not linked this technology with any abilities or conditions.", null)
                    .positiveButton(null, "Try Again", materialDialog -> {
                        materialDialog.dismiss();
                        return null;
                    });
                    
            if (ContrastUtils.isHighContrastEnabled(NewAssistiveTechnologyActivity.this)) {
                ContrastUtils.applyContrastToDialog(dialog);
            }
            
            dialog.show();
        }else if (conditionLinks.size() == 0) {
            MaterialDialog dialog = new MaterialDialog(NewAssistiveTechnologyActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                    .title(null, "Confirmation")
                    .message(null, "You have not linked this technology with any conditions, is this correct?", null)
                    .cancelable(false)
                    .negativeButton(null, "Yes", materialDialog -> {
                        saveAssistiveTechnology(technologyName, technologyDescription, technologyWebsite, technologyRRP, technologyManufacturerID, conditionLinks, abilityMap);
                        materialDialog.dismiss();
                        return null;
                    })
                    .positiveButton(null, "No", materialDialog -> {
                        materialDialog.dismiss();
                        return null;
                    });
                    
            if (ContrastUtils.isHighContrastEnabled(NewAssistiveTechnologyActivity.this)) {
                ContrastUtils.applyContrastToDialog(dialog);
            }
            
            dialog.show();
        }else if(abilityLinks.size() == 0){
            MaterialDialog dialog = new MaterialDialog(NewAssistiveTechnologyActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                    .title(null, "Confirmation")
                    .message(null, "You have not linked this technology with any abilities, is this correct?", null)
                    .cancelable(false)
                    .negativeButton(null, "Yes", materialDialog -> {
                        saveAssistiveTechnology(technologyName, technologyDescription, technologyWebsite, technologyRRP, technologyManufacturerID, conditionLinks, abilityMap);
                        materialDialog.dismiss();
                        return null;
                    })
                    .positiveButton(null, "No", materialDialog -> {
                        materialDialog.dismiss();
                        return null;
                    });
                    
            if (ContrastUtils.isHighContrastEnabled(NewAssistiveTechnologyActivity.this)) {
                ContrastUtils.applyContrastToDialog(dialog);
            }
            
            dialog.show();
        }else{
            saveAssistiveTechnology(technologyName, technologyDescription, technologyWebsite, technologyRRP, technologyManufacturerID, conditionLinks, abilityMap);
        }
    }

    private void saveAssistiveTechnology(String technologyName, String technologyDescription, String technologyWebsite, double technologyRRP, int technologyManufacturerID, List<Integer> conditionLinks, Map<String, Object> abilityLinks){
        MaterialDialog progressDialog = Dialogs.generateProgressDialog(this, "Submitting...");
        if (ContrastUtils.isHighContrastEnabled(this)) {
            ContrastUtils.applyContrastToDialog(progressDialog);
        }
        progressDialog.show();

        AT4SENDAPIInterface apiService =
                AT4SENDAPIClient.getClient().create(AT4SENDAPIInterface.class);

        Call<StatusResponse> call = apiService.createTechnology(true, technologyName, technologyDescription, technologyWebsite, technologyRRP, technologyManufacturerID, conditionLinks, abilityLinks);
        call.enqueue(new Callback<StatusResponse>() {
            @Override
            public void onResponse(@NotNull Call<StatusResponse>call, @NotNull Response<StatusResponse> response) {
                if(response.body() != null) {
                    String status = response.body().getStatus();

                    if (status.equalsIgnoreCase("success")) {
                        Drawable drawable = ResourcesCompat.getDrawable(getResources(), R.drawable.ic_checkbox_marked, null);
                        if(drawable != null) {
                            drawable.setTintList(ColorStateList.valueOf(ResourcesCompat.getColor(getResources(), R.color.md_green_600, null)));
                        }
                        MaterialDialog dialog = new MaterialDialog(NewAssistiveTechnologyActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                                .title(null, "Success")
                                .icon(null, drawable)
                                .message(null, response.body().getMessage(), null)
                                .cancelable(false)
                                .positiveButton(null, "Finish", materialDialog -> {
                                    materialDialog.dismiss();
                                    finish();
                                    return null;
                                });
                                
                        if (ContrastUtils.isHighContrastEnabled(NewAssistiveTechnologyActivity.this)) {
                            ContrastUtils.applyContrastToDialog(dialog);
                        }
                        
                        dialog.show();
                    }else{
                        MaterialDialog dialog = new MaterialDialog(NewAssistiveTechnologyActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                                .title(null, "Error Occurred")
                                .message(null, String.format("%s%s%s", "An error occurred whilst submitting the new Assistive Technology: ", "\""+response.body().getMessage()+"\"", "Please try again later."), null)
                                .cancelable(false)
                                .positiveButton(null, "Dismiss", materialDialog -> {
                                    materialDialog.dismiss();
                                    return null;
                                });
                        if (ContrastUtils.isHighContrastEnabled(NewAssistiveTechnologyActivity.this)) {
                            ContrastUtils.applyContrastToDialog(dialog);
                        }
                        
                        dialog.show();
                    }
                }else{
                   MaterialDialog dialog =  new MaterialDialog(NewAssistiveTechnologyActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                            .title(null, "Error Occurred")
                            .message(null, "An error occurred whilst submitting the new Assistive Technology. Please try again later.", null)
                            .cancelable(false)
                            .positiveButton(null, "Dismiss", materialDialog -> {
                                materialDialog.dismiss();
                                return null;
                            });
                    if (ContrastUtils.isHighContrastEnabled(NewAssistiveTechnologyActivity.this)) {
                        ContrastUtils.applyContrastToDialog(dialog);
                    }
                    
                    dialog.show();
                }

                progressDialog.dismiss();
            }

            @Override
            public void onFailure(@NotNull Call<StatusResponse>call, @NotNull Throwable t) {
                // Log error here since request failed
                Log.e(TAG, t.toString());
                crashlytics.recordException(t);

                progressDialog.dismiss();

                MaterialDialog dialog = new MaterialDialog(NewAssistiveTechnologyActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                        .title(null, "Error Occurred")
                        .message(null, "An error occurred whilst submitting the new Assistive Technology. Please try again later.", null)
                        .cancelable(false)
                        .positiveButton(null, "Dismiss", materialDialog -> {
                            materialDialog.dismiss();
                            return null;
                        });
                if (ContrastUtils.isHighContrastEnabled(NewAssistiveTechnologyActivity.this)) {
                    ContrastUtils.applyContrastToDialog(dialog);
                }
                
                dialog.show();
            }
        });
    }

    private void returnToAdminHome(){
        Intent intent = new Intent(this, AdminHomeActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        startActivity(intent);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if(requestCode == REQUEST_MANUFACTURER){
            if(resultCode == RESULT_OK && data != null){
                Bundle extras = data.getExtras();
                if(extras != null) {
                    mManufacturer.setValue(extras.getString(ItemSearchAndSelectActivity.EXTRA_ITEM_CHOSEN_NAMES, ""));
                    mManufacturerSelected = extras.getInt(ItemSearchAndSelectActivity.EXTRA_ITEM_CHOSEN_IDS, -1);
                    mManufacturer.setAlertState(false);
                }
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        
        // Apply high contrast if enabled when returning to activity
        if (ContrastUtils.isHighContrastEnabled(this)) {
            applyHighContrastToRecyclerViews();
        }
    }
    
    /**
     * Applies high contrast settings to the RecyclerViews
     */
    private void applyHighContrastToRecyclerViews() {
        // Apply contrast to abilities RecyclerView
        if (mMainRecyclerViewAbilities != null) {
            ContrastUtils.applyContrastToViewHierarchy(mMainRecyclerViewAbilities);
            
            // Apply to visible items in abilities list
            for (int i = 0; i < mMainRecyclerViewAbilities.getChildCount(); i++) {
                View itemView = mMainRecyclerViewAbilities.getChildAt(i);
                if (itemView != null) {
                    ContrastUtils.applyContrastToViewHierarchy(itemView);
                }
            }
            
            // Force adapter to refresh all items
            if (mAbilitiesAdapter != null) {
                mAbilitiesAdapter.notifyDataSetChanged();
            }
        }
        
        // Apply contrast to conditions RecyclerView
        if (mMainRecyclerViewConditions != null) {
            ContrastUtils.applyContrastToViewHierarchy(mMainRecyclerViewConditions);
            
            // Apply to visible items in conditions list
            for (int i = 0; i < mMainRecyclerViewConditions.getChildCount(); i++) {
                View itemView = mMainRecyclerViewConditions.getChildAt(i);
                if (itemView != null) {
                    ContrastUtils.applyContrastToViewHierarchy(itemView);
                    
                    // Find and apply contrast to the checked text view
                    AppCompatCheckedTextView conditionText = itemView.findViewById(R.id.conditionText);
                    if (conditionText != null) {
                        conditionText.setTextColor(TextSizeUtils.getTextColor(this).getDefaultColor());
                        
                        // Apply tint to the checkmark drawable
                        Drawable checkmark = conditionText.getCheckMarkDrawable();
                        if (checkmark != null) {
                            checkmark.setTintList(ColorStateList.valueOf(TextSizeUtils.getTextColor(this).getDefaultColor()));
                            conditionText.setCheckMarkDrawable(checkmark);
                        }
                    }
                }
            }
            
            // Force adapter to refresh all items
            if (mConditionsAdapter != null) {
                mConditionsAdapter.notifyDataSetChanged();
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        TextToSpeechManager.getInstance(this).stop();
    }
}


