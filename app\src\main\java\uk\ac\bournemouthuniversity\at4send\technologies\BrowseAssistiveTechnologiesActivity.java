package uk.ac.bournemouthuniversity.at4send.technologies;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBar;
import uk.ac.bournemouthuniversity.at4send.BaseActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;

import com.gabrielepmattia.materialfields.fields.FieldGeneric;
import com.google.android.material.snackbar.Snackbar;

import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.common.ItemSearchAndSelectActivity;
import uk.ac.bournemouthuniversity.at4send.extensions.helpers.SnackbarHelper;
import uk.ac.bournemouthuniversity.at4send.utils.ReadAloudUtils;
import uk.ac.bournemouthuniversity.at4send.utils.TextToSpeechManager;
import android.util.Log;

public class BrowseAssistiveTechnologiesActivity extends BaseActivity {
    private static final String TAG = "BrowseTechnologies";

    private static final int REQUEST_MANUFACTURER = 10001;
    private static final int REQUEST_TECHNOLOGY = 10002;

    private CoordinatorLayout mMainView;

    private int selectedManufacturerID = -1;
    private int selectedTechnologyID = -1;


    private FieldGeneric mManufaturerSelection, mTechnologySelection;

    boolean searchAvailable = false;

    private final ActivityResultLauncher<Intent> manufacturerLauncher = registerForActivityResult(
        new ActivityResultContracts.StartActivityForResult(),
        result -> {
            if (result.getResultCode() == Activity.RESULT_OK && result.getData() != null) {
                // Handle result
            }
        }
    );

    private final ActivityResultLauncher<Intent> technologyLauncher = registerForActivityResult(
        new ActivityResultContracts.StartActivityForResult(),
        result -> {
            if (result.getResultCode() == Activity.RESULT_OK && result.getData() != null) {
                // Handle result
            }
        }
    );

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_browse_assistive_technologies);

        Toolbar toolbar = findViewById(R.id.toolbar);
        // TextView pageTitle = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        Log.e(TAG, "onCreate: " + toolbar.getTitle());

        // if (pageTitle != null) {
        //     ReadAloudUtils.addReadAloudButton(this, pageTitle);
        // }
        ActionBar ab = getSupportActionBar();
        if(ab != null){
            ab.setDisplayHomeAsUpEnabled(true);
            //ab.setHomeAsUpIndicator(R.drawable.ic_close);
        }

        mMainView = findViewById(R.id.mainView);

        mManufaturerSelection = findViewById(R.id.manufacturerSelection);
        mManufaturerSelection.setOnClickListener(v -> {
            Intent i = new Intent(this, ItemSearchAndSelectActivity.class);
            i.putExtra(ItemSearchAndSelectActivity.EXTRA_LIST_TYPE, ItemSearchAndSelectActivity.LIST_TYPE_MANUFACTURER);
            manufacturerLauncher.launch(i);
        });

        mTechnologySelection = findViewById(R.id.technologySelection);
        mTechnologySelection.setOnClickListener(v -> {
            Intent i = new Intent(this, ItemSearchAndSelectActivity.class);
            i.putExtra(ItemSearchAndSelectActivity.EXTRA_LIST_TYPE, ItemSearchAndSelectActivity.LIST_TYPE_TECHNOLOGY);
            if(selectedManufacturerID != -1){
                i.putExtra(ItemSearchAndSelectActivity.EXTRA_MANUFACTURER_FILTER, selectedManufacturerID);
                i.putExtra(ItemSearchAndSelectActivity.EXTRA_MANUFACTURER_FILTER_NAME, mManufaturerSelection.getValue());
            }
            technologyLauncher.launch(i);
        });

        // Add read-aloud button to instructions text
        TextView instructionsText = findViewById(R.id.browse_instructions);
        TextView browseAllInstructionsText = findViewById(R.id.browse_all_instructions);
        
        if (instructionsText != null) {
            ReadAloudUtils.addReadAloudButton(this, instructionsText);
        }

        if (browseAllInstructionsText != null) {
            ReadAloudUtils.addReadAloudButton(this, browseAllInstructionsText);
        }
        
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            super.onBackPressed();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    public void searchAssistiveTechnologies(View view){
        if(searchAvailable){
            Intent i;
            if(selectedTechnologyID != -1){
                i = new Intent(this, ViewAssistiveTechnologyActivity.class);
                i.putExtra(ViewAssistiveTechnologyActivity.EXTRA_TECHNOLOGY_ID, selectedTechnologyID);
            }else{
                i = new Intent(this, AssistiveTechnologyListActivity.class);
                i.putExtra(AssistiveTechnologyListActivity.EXTRA_MANUFACTURER_ID, selectedManufacturerID);
                i.putExtra(AssistiveTechnologyListActivity.EXTRA_MANUFACTURER_NAME, mManufaturerSelection.getValue());
            }
            startActivity(i);
        }else{
            Snackbar sB = Snackbar.make(mMainView, "Please select a manufacturer or a product to search for technologies", Snackbar.LENGTH_LONG);
            SnackbarHelper.configSnackbar(this, sB);
            sB.show();
        }
    }

    public void listAssistiveTechnologies(View view) {
        Intent i = new Intent(this, AssistiveTechnologyListActivity.class);
        startActivity(i);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if(requestCode == REQUEST_MANUFACTURER){
            if(resultCode == RESULT_OK && data != null){
                Bundle extras = data.getExtras();
                if(extras != null) {
                    mManufaturerSelection.setValue(extras.getString(ItemSearchAndSelectActivity.EXTRA_ITEM_CHOSEN_NAMES, ""));
                    selectedManufacturerID = extras.getInt(ItemSearchAndSelectActivity.EXTRA_ITEM_CHOSEN_IDS, -1);

                    if(selectedManufacturerID != -1) {
                        searchAvailable = true;

                        mTechnologySelection.setValue("");
                        selectedTechnologyID = -1;
                    }
                }
            }
        }else if(requestCode == REQUEST_TECHNOLOGY){
            if(resultCode == RESULT_OK && data != null){
                Bundle extras = data.getExtras();
                if(extras != null) {
                    mTechnologySelection.setValue(extras.getString(ItemSearchAndSelectActivity.EXTRA_ITEM_CHOSEN_NAMES, ""));
                    selectedTechnologyID = extras.getInt(ItemSearchAndSelectActivity.EXTRA_ITEM_CHOSEN_IDS, -1);

                    if(selectedTechnologyID != -1) {
                        searchAvailable = true;
                    }
                }
            }
        }
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Stop TTS when activity is destroyed
        TextToSpeechManager.getInstance(this).stop();
    }

    public void returnHome(View view){
        onBackPressed();
    }
}








