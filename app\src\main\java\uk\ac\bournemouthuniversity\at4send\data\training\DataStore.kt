package uk.ac.bournemouthuniversity.at4send.data.training

import uk.ac.bournemouthuniversity.at4send.models.training.AssistiveTechnologyArticle
import uk.ac.bournemouthuniversity.at4send.models.training.AssistiveTechnologyTraining
import uk.ac.bournemouthuniversity.at4send.models.training.AssistiveTechnologyVideo

object DataStore {
    val ASSISTIVE_TECHNOLOGY_ARTICLES = listOf<AssistiveTechnologyArticle>(
        AssistiveTechnologyArticle("Assistive technology applications for students with reading difficulties: special education teachers' experiences and perceptions", "Reading and writing applications (with text-to-speech, TTS and speech-to-text, STT functions), used as assistive technology (AT) for students with reading difficulties are increasingly used in education, however, research has not sufficiently enough evaluated its potential. The purpose of this study was to explore how assistive reading and writing applications were perceived to function with regard to students' possibilities to assimilate (i.e., “read”) and communicate (i.e., “write”) text.", "<PERSON>, <PERSON><PERSON>, <PERSON> & <PERSON><PERSON> (2019)", "https://www.tandfonline.com/doi/pdf/10.1080/17483107.2018.1499142"),
        AssistiveTechnologyArticle("Assistive technology products: a position paperfrom the first global research, innovation, andeducation on assistive technology (GREAT) summit", "This paper is based on work from the Global Research, Innovation, and Education on Assistive Technology(GREAT) Summit that was coordinated by WHO's Global Cooperation on Assistive Technology (GATE).", "Roger O. Smith, Marcia J. Scherer, Rory Cooper, Diane Bell, David A. Hobbs,Cecilia Pettersson, Nicky Seymour, Johan Borg, Michelle J. Johnson, Joseph P. Lane, S. Sujatha,PVM Rao, Qussai M. Obiedat, Malcolm MacLachlan & Stephen Bauer (2018)", "https://www.tandfonline.com/doi/pdf/10.1080/17483107.2018.1473895"),
        AssistiveTechnologyArticle("Assistive Technology to Help Students With Disabilities","This chapter reviews some of the low-tech as well as the high-tech devices available for a wide variety of students with special learning needs. A history of assistive technology devices is explored and defined. Assistive technology has increased in use in recent decades due to accessibility of computers and the digital age.","Mark Viner","https://www.researchgate.net/profile/Michael-Shaughnessy-2/publication/338497138_Assistive_Technology_to_Help_Students_With_Disabilities/links/5f661c78a6fdcc00862d84e6/Assistive-Technology-to-Help-Students-With-Disabilities.pdf"),
        AssistiveTechnologyArticle("Automatic Detection of User Abilities through the SmartAbility Framework", "This paper presents a proposed smartphone application for the unique SmartAbility Framework that supports interaction with technology for people with reduced physical ability, through focusing on the  actions  that  they  can  perform  independently.", "Paul Whittington, Huseyin Dogan, Nan Jiang, Keith Phalp", "http://eprints.bournemouth.ac.uk/30955/1/Automatic%20Detection%20of%20User%20Abilities%20through%20the%20SmartAbility%20Framework%20-%20BHCI-2018_paper_133.pdf"),
        AssistiveTechnologyArticle("Effects of assistive technology for students with reading and writing disabilities", "Assistive technology has been used to mitigate reading disabilities for almost three deca-des, and tablets with text-to-speech and speech-to-text apps have been introduced in recent years toscaffold reading and writing. Few scientifically rigorous studies, however, have investigated the benefitsof this technology.", "Idor Svensson, Thomas Nordström, Emma Lindeblad, Stefan Gustafson,Marianne Björn, Christina Sand, Gunilla Almgren/Bäck & Staffan Nilsson (2021)", "https://www.tandfonline.com/doi/pdf/10.1080/17483107.2019.1646821"),
        AssistiveTechnologyArticle("Perceptions of Using Assistive Technology for Students with Disabilities in the Classroom","In special education, professionals interact with families and individuals with special needs who seek support. One part of the support that professionals and educational agencies can provide is assistive  technology.  This  study  was  conducted  to  determine  education  professionals'''  opinions regarding  the  use  of  assistive  technology  in  the  classroom.","Areej Ahmed","https://files.eric.ed.gov/fulltext/EJ1184079.pdf"),
        AssistiveTechnologyArticle("SmartAbility: Detection of reduced physical abilities through smartphone sensors", "This paper describes a developed Android application to accompany the SmartAbility Framework, in order to recommend technologies for people with reduced physical ability. ", "Paul Whittington, Huseyin Dogan, Nan Jiang, Keith Phalp", "http://eprints.bournemouth.ac.uk/30954/1/SmartAbility%20-%20Detection%20of%20reduced%20physical%20abilities%20through%20smartphone%20sensors%20-%20BHCI-2018_paper_269.pdf"),
        AssistiveTechnologyArticle("The development and evaluation of the SmartAbility Android Application to detect users' abilities", "The  SmartAbility  Android  Application  recommends  Assistive  Technology  (AT)  for  people  with reduced physical ability, by focusing on the actions (abilities) that can be performed independently.", "Paul Whittington, Huseyin Dogan, Nan Jiang, Keith Phalp", "https://arxiv.org/ftp/arxiv/papers/1904/1904.06138.pdf"),
        AssistiveTechnologyArticle("The Success of Using Assistive Technology with Disabled and Non-Disabled Students","(AT) Assistive Technology helps disabled and non-disabled students to enhance their independence in learning, executing practical tasks, and boosting their activeness during group discussion.","Sameer Abuzandah","https://www.globalpresshub.com/index.php/AJSR/article/download/1124/978"),
        AssistiveTechnologyArticle("Using Assistive Technology Tools to Support Learning in the Inclusive Preschool Classroom Inclusive Preschool Classroom","For over a century, early childhood experts have discussed the importance of play for young children's growth and development.  Play is critical for the development of young children as it increases learning (Barton, 2015), supports young children in gaining social and communication skills (Dennis & Stockall, 2015), and leads to social awareness and empathy skills (Brown, 2009).","Lohmann, Marla J.; Hovey, Katrina A.; Gauvreau, Ariane N.; and Higgins, Johanna P. (2019)","https://scholarworks.lib.csusb.edu/cgi/viewcontent.cgi?article=1090&context=josea")
    )
    val ASSISTIVE_TECHNOLOGY_VIDEOS = listOf<AssistiveTechnologyVideo>(
        AssistiveTechnologyVideo("Amazing Tech to Help People With Disabilities!", "Here are some great gadgets to assist people with disabilities. The Xbox Adaptive Controller is amazing for gaming. The logitech mx vertical can assist with limited mobility! Watch for the full list!", "Matthew Moniz", "https://www.youtube.com/watch?v=75AOTEpz7mE"),
        AssistiveTechnologyVideo("Assistive Technology Apps", "This video goes over some of my favorite Assistive Technology (AT) that's easy to set up and FREE! While there are infinite amazing AT options, these are the AT I recommend most frequently to my students as a school-based Occupational Therapist :) Enjoy!", "Katrina Booth", "https://www.youtube.com/watch?v=X5BZx_ipgK0"),
        AssistiveTechnologyVideo("Assistive Technology During a Pandemic: Empowering people with disabilities", "Assistive technology is extremely important for people with disabilities and today we showcase just how empowering it can be during a pandemic. This pandemic is increasing isolation for millions of people with disabilities all over the world. Together we can conquer the curve!", "All Access Life", "https://www.youtube.com/watch?v=TD6nQeNbmL8"),
        AssistiveTechnologyVideo("Assistive Tools for iPad - Speech Features", "--In this video, I explore:\n--Speech to Text\n--Text to Speech\n--Pronunciation of words, phrases & names", "Matt Richards Creative Digital Learning", "https://www.youtube.com/watch?v=A2GuxVYLxs4"),
        AssistiveTechnologyVideo("Augmentative and Alternative Communication (AAC ) Devices", "Learn how Fairfax County Public Schools uses Augmentative and Alternative Communication (AAC) Devices to help give students a voice in their learning.", "Fairfax Network - Fairfax County Public Schools", "https://www.youtube.com/watch?v=qB2Fk0KdUuo"),
        AssistiveTechnologyVideo("Best FREE Assistive Technology for Students", "This video goes over some of my favorite Assistive Technology (AT) that's easy to set up and FREE! While there are infinite amazing AT options, these are the AT I recommend most frequently to my students as a school-based Occupational Therapist :) Enjoy!", "The OT Guide", "https://www.youtube.com/watch?v=WQL7_Gte4Tw"),
        AssistiveTechnologyVideo("Best of CES 2019 | Top 5 Assistive Technology Products", "Power On's CES Best Of show is here! Last week, TheCIL traveled to Las Vegas to attend the 2019 Consumer Electronics Show.  We went not for the ping-pong robot or even the Lamborghini Massage Chair.  On this episode of Power On, we'll be reviewing the top 5 assistive technology products for people with disabilities at CES 2019.", "TheCIL", "https://www.youtube.com/watch?v=TZBqNrl6shc"),
        AssistiveTechnologyVideo("How @Apple Is Helping Blind Users", "At this year's WWDC, Apple announced iOS 14, macOS 11 Big Sur, and more updates to their platforms. Along with these, Apple detailed some new Accessibility features for the Blind, Deaf, and Disabled communities. We discuss the importance of creating accessible applications if you're a developer and I share some notable features and discuss the impact these features like Sound Recognition and VoiceOver Recognition will have for their users.", "James Rath", "https://www.youtube.com/watch?v=p6ys7QbXQo8"),
        AssistiveTechnologyVideo("How to use AssistiveTouch on your iPhone — Apple Support", "With AssistiveTouch, you can customize how you navigate your iPhone by creating your own menu and gestures, which can be helpful if you have limited dexterity or hand strength. ", "Apple Support", "https://www.youtube.com/watch?v=a0IO_tqsO5g"),
        AssistiveTechnologyVideo("Three Assistive Technology Tools to Help Students with Dyslexia", "Welcome to the debut of The Joy Vlog! \uD83D\uDC4F In this episode, our  Instructional Technologist, Christine Dinh, will discuss three assistive technology tools that can help students with dyslexia.", "The Joy School", "https://www.youtube.com/watch?v=NUjWnf7Pj-E")
    )

    val ASSISTIVE_TECHNOLOGY_TRAINING_HARDWARE = listOf<AssistiveTechnologyTraining>(
        AssistiveTechnologyTraining(
            "Adaptive Switches",
            "<p>Adaptive switches help people with motor impairments access devices like computers, smartphones, wheelchairs, and smart home tech. The most common is a large round switch, usable by hand, head, elbow, or foot.</p><p>Types include:</p><ul><li><strong>Hand, Finger, Body Switches</strong>: Activated by small movements.</li><li><strong>Light Switches</strong>: Triggered by eye movement or blinking.</li><li><strong>Sound Switches</strong>: Voice-activated by specific sounds.</li><li><strong>Pillow Switches</strong>: Soft, tactile switches for head, shoulder, arm, or hand use.</li><li><strong>Plate Switches</strong>: Large, touch-sensitive for reduced dexterity.</li><li><strong>Saucer Switches</strong>: Require light touch, suitable for limited wrist/hand control.</li><li><strong>Mouth, Sip and Puff Switches</strong>: Operated by inhaling or exhaling.</li><li><strong>Wheelchair Switches</strong>: Mounted for activation by fingers, neck, shoulder, sound, or vibration.</li></ul><p>Most operating systems support switches natively, e.g., iOS Switch Control.</p>",
            "<ul><li>Increase independence for users with limited movement.</li><li>Improve communication and access to technology.</li><li>Support brain development and learning tasks.</li><li>Multiple switches can be used for complex tasks.</li></ul>",
            "<ul><li>Not suitable for users with good motor control—alternatives may be better.</li><li>May require time to learn and adapt.</li></ul>",
            ""
        ),
        AssistiveTechnologyTraining(
            "Alternative Keyboards",
            "<p>Standard keyboards are designed for two-handed, right-handed use. Alternative keyboards help users with disabilities. Types include:</p><ul><li><strong>Ergonomic Keyboards</strong>: Reduce strain with split or curved layouts.</li><li><strong>Compact Keyboards</strong>: Smaller, easier for single-handed or wheelchair users.</li><li><strong>Separate Numeric Keypads</strong>: Movable for left-handed use.</li><li><strong>Larger Key Keyboards</strong>: Bigger keys for easier targeting.</li><li><strong>High Contrast Keyboards</strong>: Easier to see with bold colors or stickers.</li><li><strong>Braille Keyboards</strong>: Tactile controls and reading for visually impaired users.</li><li><strong>On-screen Keyboards</strong>: Use touch, mouse, switch, or joystick.</li><li><strong>Voice Recognition</strong>: Control the computer by voice.</li><li><strong>Eye Tracking</strong>: Move the cursor and select by looking at the screen.</li><li><strong>Switches</strong>: Select letters/words by repeated activation.</li><li><strong>Head Tracking</strong>: Move cursor by head movement.</li><li><strong>Keyguards</strong>: Prevent accidental presses and allow resting hands.</li></ul>",
            "<ul><li>Improve access for users with limited dexterity.</li><li>Many types available for different needs.</li><li>Can improve posture and comfort.</li><li>Voice recognition helps users with learning disabilities.</li><li>One-handed typing possible with some keyboards.</li></ul>",
            "<ul><li>May take time to adjust.</li><li>Typing speed may still be limited.</li><li>Some may not be compatible with all devices.</li><li>Often less portable, designed for stationary use.</li></ul>",
            ""
        ),
        AssistiveTechnologyTraining(
            "Alternative Mice",
            "<p>Alternative mice help people with disabilities control computers more comfortably. Types include:</p><ul><li><strong>Ergonomic Mice</strong>: Left/right-handed, various sizes.</li><li><strong>Vertical Mice</strong>: Align wrist in a natural position.</li><li><strong>Adjustable Ergonomic Mice</strong>: Switch between vertical and horizontal use.</li><li><strong>Bar Mice</strong>: Operated with the thumb, placed in front of the keyboard.</li><li><strong>Trackball Mice</strong>: Move the cursor by rotating a ball.</li><li><strong>Touchpads</strong>: Control by sliding fingers and tapping.</li><li><strong>Pen Mice</strong>: Held like a pen, with click buttons.</li><li><strong>Joysticks</strong>: Move cursor and click using a joystick.</li></ul>",
            "<ul><li>Improve computer access for users with limited motor control.</li><li>Many types suit different needs.</li><li>Sensitivity can be adjusted.</li></ul>",
            "<ul><li>May take time to get used to.</li><li>Some may drain laptop batteries faster.</li></ul>",
            ""
        ),
        AssistiveTechnologyTraining(
            "Augmentative and Alternative Communication (AAC) Devices",
            "<p>AAC devices help people with speech or language impairments communicate. Types include No Tech (e.g., symbol boards), Lite-Tech (recorded messages), and High-Tech (speech synthesis).</p><p> Common devices:</p><ul><li>&nbsp;&nbsp;&nbsp;<strong>one-message</strong>and <strong>two-message communicators</strong>,<strong> progressive communicators </strong> (learn user’s speech), <strong> wearable devices</strong>, and <strong> classroom devices </strong>.</li></ul>",
            "<ul><li>Support communication for people with complex needs.</li><li>Useful for temporary or permanent speech loss.</li><li>Beneficial for conditions like autism, cerebral palsy, stroke, Down’s syndrome, aphasia, and paralysis.</li></ul>",
            "<ul><li>Using AAC devices can be slow.</li><li>The voice may not sound natural.</li></ul>",
            ""
        ),
        AssistiveTechnologyTraining(
            "Environmental Control Systems",
            "<p>Environmental Control Systems are devices that allow people with physical disabilities to operate electronic devices, including televisions, computers, lights and appliances. The devices can be grouped into two categories: stand-alone and computer-based devices.</p><p>Stand-alone Environmental Control Systems contain their own electronics and do not require a computer to function. Many of these units can be activated by a switch. Computer-based Environmental Control Systems consists of software that allow a computer to function as an Environmental Control:</p><p>There are four types of Environmental Control Systems available:</p><ul><li>&nbsp;&nbsp;&nbsp;<strong>AC Power: </strong>Uses existing electrical wiring where each item to be controlled is plugged into a control box, which in turn is plugged into an electrical outlet. The user has an input device that communicates with each control box and a different control box is needed for each appliance. These systems are inexpensive and easy to install.</li><li>&nbsp;&nbsp;&nbsp;<strong>Infrared:</strong> Sends an infrared signal to the control unit, which then sends another infrared signal to the appliance.</li><li>&nbsp;&nbsp;&nbsp;<strong>Radio Control:</strong> The remote sends radio waves to the control unit, which then sends the message to the appliance. The remote and the control box can be in different rooms and still work.</li><li>&nbsp;&nbsp;&nbsp;<strong>Ultrasound:</strong> Uses high frequency sound waves as the input and output signal. The input device and the control box must be in the same room to work.</li></ul>",
            "<ul><li>&nbsp;&nbsp;&nbsp;Environmental Control Systems enable persons with complex disabilities to have control over a range of appliances.</li><li>&nbsp;&nbsp;&nbsp;It is possible to use a person&rsquo;s most reliable method of operation, e.g. direct touch, switches, voice activation or Eye Gaze.</li><li>&nbsp;&nbsp;&nbsp;Provide independence and reduce the reliance on others to control a person&rsquo;s environment.</li></ul>",
            "<ul><li>&nbsp;&nbsp;&nbsp;Interference from other nearby control units is possible.</li><li>&nbsp;&nbsp;&nbsp;With any type of Environmental Control System, it is important to have a backup system in place in case of power failure.</li><li>&nbsp;&nbsp;&nbsp;Infrared Environmental Control Systems only operate when the remote is aimed directly at the control box with nothing blocking its path.</li></ul>",
            ""
        ),
        AssistiveTechnologyTraining(
            "Eye Tracking",
            "<p>An eye tracking device is tablet device that is controlled by eye movements instead of using your hands. An inbuilt light sensor, the tablet monitors reflections from the pupils, tracking eye movements and converting them into mouse movements and commands. By looking anywhere on the screen, the user&rsquo;s eye controls the cursor, while blinking performs a click. It can also accommodate right, double and left clicks, to gives the user more control. When paired with appropriate software, the synthesised voice will iterate words and sentences based on the input of the user. Eye tracking devices also have internet and Bluetooth connectivity, allowing for the sending and receiving of emails, text messages and controlling the surrounding environment.</p><p>Eye tracking tablet computers also accommodate those who have physical disabilities with inbuilt shake detection, which counteracts head movements and allows for better control of the mouse.</p>",
            "<ul><li>Eye tracking can be used by children and adults who have significant physical disabilities to be more independent.</li><li>It may provide them with a way to communicate, access the internet or use social media.</li><li>Provide opportunities to play and operate a TV, telephone and music through remote control.</li><li>The conditions who can benefit from Eye tracking devices include:<ul><li>Motor Neurone Disease</li><li>Cerebral Palsy</li><li>Muscular Dystrophy</li><li>Rett Syndrome</li><li>Strokes</li></ul></li></ul>",
            "<ul><li>Not all eyes can be tracked as contact lenses, glasses, and pupil colour can affect the camera's ability to detect eye movements</li><li>Eye tracking equipment (i.e., camera, computer, software) and training can be expensive.</li><li>Eye movements of some users are often involuntary and lead to incorrect control of the system.</li></ul>",
            ""
        ),
        AssistiveTechnologyTraining(
            "Screen Readers",
            "<p>A screen reader helps people who are visually impaired to access and interact with digital content, such as websites or applications via audio or touch. The technology reads out loud what is on the screen and users can customise it to suit their needs, e.g. decreasing the speed of speech or changing the language. Screen readers allow people to navigate through websites and applications via the speech output. Some screen readers can also be used with a Braille display. The user will need to learn some shortcut keys or touch gestures. While it is possible to use a screen reader by learning just a few commands, becoming an advanced user who can interact confidently does require more time to become familiar with their advanced features.</p><p>There are different screen readers available. Nearly all computers, Tablet computers and smartphones have a screen reader function built in. The most popular programs are JAWS and NVDA for Windows computers, VoiceOver for the Mac and iPhone, and TalkBack on Android. The best choice depends on:</p><ul><li>The type of computer and or mobile phone.</li><li>The preferred web browser as some combinations of browser and screen reader works better than others.</li><li>The apps you use; while all screen reader users work with word processing, email and the web browser, if you need a screen reader to work with specific applications you may be limited to one that can be programmed to work well with it.</li></ul>",
            "<ul><li>Screen readers allows people who are visually impaired to use the computer.</li><li>They allow people who are visually impaired to create a document using a word processor, read any article on the internet, write emails and engage with social networking.</li><li>Screen readers are compatible with computer games and audio editing software.</li></ul>",
            "<ul><li>As are visually impaired only listen to a screen reader reading the text displayed on the screen, they don&rsquo;t usually have the chance to know the correct spelling of a certain word.</li><li>Screen readers use a computer sounding voice and some people find this very boring.</li></ul>",
            ""
        ),
        AssistiveTechnologyTraining(
            "Tablet Computers",
            "<p>Recently, many pupils have started using assistive technology on mobile devices to help them with schoolwork. The two most popular mobile operating systems are Android and iOS. A variety of companies make mobile devices for Android (such as Samsung and HTC) and Apple makes iPhones and iPads for iOS. Tablet computers come with built-in accessibility tools, for example basic text-to-speech. Users who need further tools can download apps onto their Tablet computers from the iTunes App Store (iOS) and Google Play Store (Android). There are many apps that can help pupils with learning disabilities.</p>",
            "<ul><li>Tablet computers are portable.</li><li>Tablet computers have touchscreens, which some pupils with learning disabilities may prefer.</li><li>The camera on Tablet computers can be used to scan documents and add photos to projects.</li><li>Some Tablet computers are less expensive than desktop and laptop computers.</li></ul>",
            "<ul><li>Tablet computers generally have less storage space than desktops and laptops.</li><li>Tablet computers have smaller screens than most laptops and desktops, sometimes making it difficult to see an entire page. When using the device&rsquo;s onscreen keyboard, even less of a page is visible.</li><li>Mobile apps can make doing complex work more difficult.</li></ul>",
            ""
        )
    )

    val ASSISTIVE_TECHNOLOGY_TRAINING_SOFTWARE = listOf<AssistiveTechnologyTraining>(
        AssistiveTechnologyTraining(
            "Android Accessibility Features",
            "<p>Android devices can be customised by using different accessibility settings and apps. To interact with the device using touch and spoken feedback, the TalkBack screen reader can be switched on. TalkBack describes actions and informs of alerts and notifications. The TalkBack Braille keyboard can be used to enter 6-dot Braille on the screen. Alternative, BrailleBack can connect a refreshable Braille display via Bluetooth that works with TalkBack for a combined speech and Braille experience,.</p><p>If the user requires spoken feedback only at certain times, Select to Speak can be used where either items are selected on the screen to hear them read or described aloud, or point the camera at something in the real world.</p><p>People with reduced vision can use Magnification to temporarily zoom or magnify the screen or adjust contrast or colours, use high-contrast text, dark theme, colour inversion or colour correction.</p><p>There are many interaction controls to make Android easier to use:</p><ul><li><strong>Lookout</strong> uses computer vision to assist people who are blind or have low vision in gaining information about their surroundings.</li><li><strong>Voice Access</strong> controls the device with spoken commands to open apps, navigate and edit text hands-free.</li><li><strong>Switch Access</strong> allows the Android device to be controlled using one or more switches or a keyboard instead of the touchscreen.</li><li><strong>Action Blocks </strong>makes routine actions easier with customisable buttons on your Android homescreen.</li></ul><p>People with hearing impairments can use Live Captions to automatically captions speech on the device. Live Transcribe can capture speech and sound and display them as text on the screen. Using Sound Amplifier with wired or Bluetooth headphones can filter, augment and amplify the sounds in your environment or on your Android device. It is also possible to pair hearing aids with Android devices.</p>",
            "<ul><li>Android Accessibility Features allow people with certain disabilities to access applications on their smartphone and the internet with a lot more ease than if they were just using the standard features.</li><li>Android Accessibility Features give added capabilities to all users in situations where they have limited concentration or are not able to multi-task efficiently.</li></ul>",
            "<ul><li>Only Unified English Braille is currently supported with the TalkBack Braille keyboard.</li></ul>",
            ""
        ),
        AssistiveTechnologyTraining(
            "Curriculum Software",
            "<p>Curriculum software is used by teachers, speech and language therapists and parents to create custom teaching materials to ensure that materials are accessible for pupils with learning disabilities. Visual illustrations of schoolwork can improve the learning processes of pupils by up to 400%.</p><p>Story card software creates physical images for teachers to use for their pupils to support learning and communication. Numeracy software allows for the creation of mathematical equations and formulas directly on a computer, which can be added to a Microsoft Word document or Google Workspace apps. This is helpful for Maths and Science, where chemical compounds can also be written digitally.</p><p>Other types of software include memory tests, vocabulary exercises and reading activities.</p>",
            "<ul><li>Save time with searchable, ready-made material and curricula for learners of all ages and ability levels.</li><li>Engage pupils who use AAC devices more easily with activities that can be customised for their needs.</li><li>Switch access is compatible with most curriculum software for pupils who cannot use a mouse or touchscreen.</li><li>Curriculum software can benefit pupils with dyslexia, dyspraxia and Attention Deficit Hyperactivity Disorder.</li></ul>",
            "<ul><li>The software may not fully suit the taught curriculum.</li><li>Composing digital Maths can be initially more time consuming.</li></ul>",
            ""),
        AssistiveTechnologyTraining(
            "Dyslexia Tools",
            "<p>There is a wide range of software that is specifically designed to make life easier for people with dyslexia.&nbsp; Examples of the technology that are suitable for people of all ages are provided below.</p><p><strong>Speech recognition software </strong>allows users to dictate or talk to a computer that uses software to convert this to text.</p><p><strong>Text-to-speech software </strong>enables individuals to understand written material they are presented with and to proof-read or check their own work.</p><p><strong>Mind mapping software</strong> is specifically designed to allow people with dyslexia to plan their work more effectively.</p><p><strong>Scanning software</strong> and<strong> hand reading pens </strong>allow the user to store and listen to the text found in books and other documents.</p><p><strong>Spell checkers</strong> that are specifically designed for people with dyslexia automatically make corrections to written communications.</p><p><strong>Smartpens</strong> can be used to write text, but which track the text being written and recreate the notes in digital form. The pen can then upload the text to a smartphone, computer or tablet.</p><p><strong>Computer-based learning programs</strong> are specifically written for people with dyslexia and can help to increase their reading, writing, touch-typing and numeracy skills.</p>",
            "<ul><li>Assistive technology helps people with dyslexia to save time and overcome challenges, such as slow note-taking and poor handwriting</li><li>It can allow them to demonstrate their abilities in ways that were once unimaginable.</li><li>Speech recognition software benefits individuals that might otherwise have difficulty with spelling or writing emails, reports or other written communications.</li></ul>",
            "<ul><li>Speech detection will never be 100% accurate and therefore extra time has to be spent performing corrections.</li></ul>",
            ""
        ),
        AssistiveTechnologyTraining(
            "iOS Accessibility Features",
            "<p>The Apple iOS operating system provides many accessibility features to support vision, physical and motor, hearing and learning needs.</p><p>VoiceOver is a gesture-based screen reader that allows people to use a iOS device without seeing the screen. VoiceOver gives audible descriptions of what is displayed on the screen, including battery level, incoming calls and which app is selected.</p><p>The Zoom feature can zoom in or out on specific items, magnify the entire screen (Full Screen Zoom) or magnify only part of the screen with a resizable lens (Window Zoom).</p><p>People with physical disabilities can use Switch Control to operate iOS using one or more switches to select, tap, drag, type and draw freehand. It is possible to use a switch to select an item or location on the screen, then use the same (or a different) switch to choose an action.</p><p>Voice Control allows iOS to be used by voice. Speak commands to perform gestures, interact with screen elements or dictate and edit text.</p><p>iOS can display subtitles and captions for videos to assist people with hearing impairments. When a video is played in a supported app, subtitles and closed captions can be turned on. This usually shows standard subtitles and captions, but special accessible captions can be selected such as subtitles for the deaf and hard of hearing (SDH).</p>",
            "<ul><li>iOS accessibility features allow people with certain disabilities to access applications on their phone and the internet with a lot more ease than if they were just using the standard features.</li><li>The VoiceOver speaking rate and pitch can be adjusted to suit the user&rsquo;s needs.</li></ul>",
            "<ul><li>Voice Control may not understand people with speech impairments.</li><li>Not all video content is compatible with subtitles.</li></ul>",
            ""
        ),
        AssistiveTechnologyTraining(
            "Keyboard/Typing Skills Software",
            "<p>Touch typing can be one of the most valuable skills for children with dyslexia, dyspraxia and autism. It provides a very different option for written work and it can have a very significant effect, completely changing how they feel about written work.</p><p>When typing without looking down at the keyboard, the brain&rsquo;s physical skill centre automates operations and skills, so that once learned, the process part is unconscious.&nbsp; There are many advantages to this part of the brain being in control, including:</p><ul><li>Typing more accurately</li><li>Typing faster</li><li>Keeping your eyes on the screen, so that there is no dividing of the attention between the screen and the keyboard</li><li>Your mind is free to concentrate on content and quality of writing</li></ul><p>There are many typing skills software available that combine typing games, exercises, video tutorials and real-time progress tracking to help develop typing speed and accuracy. They are suitable for pupils and adults with many assessing skill levels and suggesting goals.</p>",
            "<ul><li>By developing typing skills, students will be able to handle written communication much faster and with higher accuracy. This can be beneficial in many professional and educational settings.</li><li>Typing faster while remaining accurate will save a great amount of time with schoolwork.</li><li>Improves posture and prevents Repetitive Strain Injuries</li></ul>",
            "<ul><li>It may not be possible for pupils with reduced fine motor control to type.</li><li>Some people may only be able to type with one hand.</li><li>Learning to touch type requires practise and can be time consuming.</li></ul>",
            ""
        ),
        AssistiveTechnologyTraining(
            "Visual Impairment Software",
            "<p>Visual Impairment Software can read text out loud and types what is being spoken. Intelligent personal assistants such as Siri and the Google Assistant &nbsp;can be useful for people with visual impairments as they can &lsquo;ask&rsquo; the assistants to carry out tasks including answering queries, sending messages and emails, adding events to your calendar, taking notes and setting reminders.</p><p>New computers and most smartphones have a screen reader such as Narrator for Windows and VoiceOver for Apple Mac, iPad and iPhone. There&rsquo;s also a range of specialist screen readers available for people who are blind. A larger screen is beneficial for people with low vision and where are also ways of magnifying on-screen information in web browsers and operating systems.</p><p>Many people with visual impairments can see some colour combinations (such as white text on a black background) better than others. Different colour options are available in most programmes. The majority of computer, tablet and smartphone operating systems have a wide range of pre-defined colour schemes as well as custom schemes.</p>",
            "<ul><li>Helps pupils with visual impairments to complete homework, write and connect socially more independently.</li><li>Improves confidence and efficiency in daily life.</li><li>Increases access to the general curriculum and improves academic performance.</li></ul>",
            "<ul><li>There can be accessibility and usability issues that pose major challenges with visual impairment software such as lack of spoken feedback,</li><li>This specialist software can have a high cost.</li><li>Learning is required to use new devices, keyboard commands and gestures.</li></ul>",
            ""
        ),
        AssistiveTechnologyTraining(
            "Windows Accessibility Tools",
            "<p>The Microsoft Educator Center website (<a href=\"https://education.microsoft.com/en-us\">https://education.microsoft.com/en-us</a>) has been designed for special education and accessibility resources, which lists and categorises their accessibility tools.</p><p><strong>Screen Reader</strong> is built into Windows 10, which allows people to have their first experience with screen reading. Reading with highlighting can be displayed on any text and colour filters can be used for colour blindness. Eye control is also built into standard Windows 10.</p><p><strong>Microsoft Narrator </strong>will read aloud items on the screen, such as buttons and menus, as you select them or as you navigate through them using the keyboard. Narrator will also read aloud any text that you select.</p><p><strong>Immersive Reader</strong> is a free tool that improves reading and writing for people, regardless of their age or ability. It is built into Microsoft Office, Microsoft Teams, Microsoft Forms, Flipgrid, and Microsoft Edge Browser. It is very customisable and there is also a picture dictionary built in.</p><p><strong>Microsoft Office Lens App</strong> can capture notes and information from whiteboards, menus, signs, handwritten memos, or anything with a lot of text. It&rsquo;s great for capturing sketches, drawings and equations too and even images without text. It can take a photo of a page of a book and send to Immersive Reader, which can then be read aloud. Office Lens can also translate the photo of page of a book to different languages.</p><p><strong>Speech Detect</strong> is high quality dictation is built into most of the Office apps. Dictation is in PowerPoint, Outlook and Windows.</p><p><strong>Keyboard accessibility features</strong> are built into Windows 10. Sticky Keys assist with using the modifier (Shift, Ctrl and Alt) functions. It allows the modifier keys to remain active even after when you're not pressing them. FilterKeys make the keyboard ignore brief or repeated keystrokes. Toggle Keys are designed for users with vision impairments or learning disabilities. When Toggle Keys are on, the computer provides sound cues when the locking keys (Caps Lock, Num Lock, or Scroll Lock) are pressed. A high pitched tone sounds when these keys are switched on and a low pitched tone sounds when they are turned off.</p>",
            "<ul><li>The Microsoft Educator Center website can be used to discover accessibility tools that can support pupils with disabilities to more easily engage with computers.</li><li>All of the accessibility tools are built into Windows 10 and are free.</li><li>Immersive Reader can help build confidence for readers learning to read at higher levels and offers text decoding solutions for students with learning disabilities such as dyslexia.</li><li>Sticky Keys can help users with physical impairments that have trouble pressing two keys at a time.</li><li>FilterKeys makes typing easier for users with hand tremors.</li><li>Toggle Keys inform the user when any of the locking keys are pressed.</li></ul>",
            "<ul><li>The Microsoft accessibility tools are only compatible with the Windows operating system.</li></ul>",
            ""
        )
    )

    val TRAINING_HELP_SLIDE_DATA = listOf(
        Pair("EduAbility", "The EduAbility Training Package has been designed to provide assistive technology training for teachers, teaching assistants and support staff in mainstream or special educational needs schools. This App provides training material, articles and videos, as well as multiple choice Quizzes to test your understanding."),
        Pair("User Accounts", "Sign in to the EduAbility Training Package using either an email address or a Google account. An EduAbility account will be used to store your training results. A password is required containing at least 6 characters including letters and numbers. The App will automatically stay signed into your account until you log out using the button on Training Home."),
        Pair("Navigation", "The bar at the top of the App allows you to move between the Training and Quiz sections. You can move to the Previous screen using either the '<' button in the top left corner of the screen. You can also 'Return to Training Home' at the bottom of the screen. In a Quiz, it is not possible to go back to the previous question."),
        Pair("Training Home", "This is the main menu of the EduAbility Training Package that allows you to access the various sections of the App."),
        Pair("Training", "The Training section contains information on the types of hardware and software assistive technologies that are typically used in educational environments. Each category contains a description, benefits and limitations of each assistive technology. The information provided forms the basis of multiple choice Quizzes and there is a Quiz available for each category."),
        Pair("Learn", "The Learn section can be used to find out more about assistive technologies through articles and books that are available online. These will describe some of the real world uses and benefits of assistive technologies. When an article is selected, EduAbility will download a PDF copy to your device."),
        Pair("Videos", "A number of YouTube links are provided to illustrate real world examples of assistive technologies, to provide context and further understanding to the training material. EduAbility will open the videos in either the YouTube app or the web browser of your device."),
        Pair("Training Results", "This menu provides a list of your Quiz results, indicating the date and time taken, your score and result."),
        Pair("Quizzes", "There is a Quiz for each assistive technology category which can be accessed using the bar at the top of the App. This displays a list of all of the Quizzes available and each Quiz contains 5 multiple choice questions, requiring one or two correct answers. At the end of the Quiz you will receive your result, which will be stored in your EduAbility account. The pass mark is 80% and Quizzes can be taken as many times as necessary."),
        Pair("Training Package Usage", "The EduAbility Training Package will be updated when new types of assistive technologies become available, providing new information and Quiz questions. It is recommended that training should be undertaken at least once a term, to keep up to date and continue learning about new assistive technologies.")
    )

}