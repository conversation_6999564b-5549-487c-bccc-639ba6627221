package com.gabrielepmattia.materialfields.fields

import android.content.Context
import android.content.res.TypedArray
import android.graphics.PorterDuff
import android.graphics.drawable.Drawable
import androidx.core.content.ContextCompat
import android.util.AttributeSet
import android.util.Log
import android.view.LayoutInflater
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.Toast
import com.gabrielepmattia.materialfields.R

/**
* @Project aj-android
* <AUTHOR>
* @Date 02/03/2018 21:16
*/
open class FieldGenericNoIcon : Field {

    /*
     * Constructors
     */

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet, defAttr: Int) : super(context, attrs, defAttr)
    constructor(context: Context, attrs: AttributeSet, defAttr: Int, defRes: Int) : super(context, attrs, defAttr, defRes)

    /*
     * Helpers
     */

    override fun initView(context: Context) {
        val i: LayoutInflater = context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        i.inflate(R.layout.component_field, this, true)
    }

    override fun initAttrs(attrs: AttributeSet) {
        val t: TypedArray = context.obtainStyledAttributes(attrs, R.styleable.FieldInputText) as TypedArray
        val req = t.getBoolean(R.styleable.FieldInputText_required, false)
        t.recycle()

        // init all base attrs
        super.initAttrs(attrs)

        // set required if passed as parameter
        if (req) setRequired(true)
    }

    override fun onFinishInflate() {
        super.onFinishInflate()
    }

    /*
     * Validation
     */

    /**
     * For a generic field a validator is a function that takes as input a string a returns a boolean
     * whether the validation passed or not. Remember to set the error message when setting a validator.
     */
    open protected var validator: ((_: String?) -> Boolean)? = null

    /**
     * Validate the field by calling the set validator
     *
     * @return Response of validation
     */
    fun validate(): Boolean {
        if (validator == null) {
            Log.w(TAG, "No validator set! Validation returns always true")
            return true
        }
        val res = validator!!(value)
        setAlertState(!res)
        // show a toast when validation does not succeed
        if (!res) {
            val t: Toast? = Toast.makeText(context, errorMessage, Toast.LENGTH_SHORT)
            t!!.show()
        }
        return res
    }
    /**
     * This function sets the fields as [required]. In other words, it sets a simple validator to the
     * field that checks if the field is not empty. If set to false it simply calls removeValidator
     */
    fun setRequired(required: Boolean) {
        if (required) {
            this.errorMessage = context.getString(R.string.required_tooltip, title)
            this.validator = { s: String? -> !(s == null || s.isEmpty()) }
        } else this.validator = null
    }


    companion object {
        private var TAG = FieldGenericNoIcon::class.java.simpleName
    }

}