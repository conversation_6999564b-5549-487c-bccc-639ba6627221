package uk.ac.bournemouthuniversity.at4send.api;

import java.util.List;
import java.util.Map;

import retrofit2.Call;
import retrofit2.http.Field;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;
import uk.ac.bournemouthuniversity.at4send.models.AssistiveTechnology;
import uk.ac.bournemouthuniversity.at4send.models.response.AbilitiesGroupResponse;
import uk.ac.bournemouthuniversity.at4send.models.response.ConditionsResponse;
import uk.ac.bournemouthuniversity.at4send.models.response.ManufacturerResponse;
import uk.ac.bournemouthuniversity.at4send.models.response.QuizResultsResponse;
import uk.ac.bournemouthuniversity.at4send.models.response.StatusResponse;
import uk.ac.bournemouthuniversity.at4send.models.response.TechnologiesResponse;

public interface AT4SENDAPIInterface {

    @FormUrlEncoded
    @POST("public/manufacturers.json.php")
    Call<ManufacturerResponse> getManufacturers(@Field("fromApp") boolean fromApp);

    @FormUrlEncoded
    @POST("public/technologies.json.php")
    Call<TechnologiesResponse> getTechnologies(@Field("fromApp") boolean fromApp);

    @FormUrlEncoded
    @POST("public/technologies.json.php")
    Call<TechnologiesResponse> getTechnologies(@Field("fromApp") boolean fromApp, @Field("manufacturer_id") int manufacturerID);

    @FormUrlEncoded
    @POST("public/technologies.json.php")
    Call<TechnologiesResponse> getTechnologiesForCondition(@Field("fromApp") boolean fromApp, @Field("condition_id") int conditionID);

    @FormUrlEncoded
    @POST("public/technologies.json.php")
    Call<TechnologiesResponse> getTechnologiesForConditions(@Field("fromApp") boolean fromApp, @Field("condition_ids[]") List<Integer> conditionIDs);

    @FormUrlEncoded
    @POST("public/technologies.json.php")
    Call<TechnologiesResponse> getTechnologiesForAbilities(@Field("fromApp") boolean fromApp, @Field("ability_ids[]") List<Integer> abilityIDs, @Field("ability_ratings[]") List<Integer> abilityRatings);

    @FormUrlEncoded
    @POST("public/conditions.json.php")
    Call<ConditionsResponse> getConditions(@Field("fromApp") boolean fromApp);

    @FormUrlEncoded
    @POST("public/abilities.json.php")
    Call<AbilitiesGroupResponse> getAbilitiesWithGroups(@Field("fromApp") boolean fromApp);

    @FormUrlEncoded
    @POST("public/technology.json.php")
    Call<AssistiveTechnology> getTechnology(@Field("fromApp") boolean fromApp, @Field("technology_id") int technologyID);

    @FormUrlEncoded
    @POST("public/new-technology.json.php")
    Call<StatusResponse> createTechnology(@Field("fromApp") boolean fromApp, @Field("technology_name") String technologyName, @Field("technology_description") String technologyDescription, @Field("technology_website") String technologyWebsite, @Field("technology_rrp") double technologyRRP, @Field("technology_manufacturer_id") int technologyManufacturerID, @Field("condition_links[]") List<Integer> conditionLinks, @FieldMap Map<String, Object> abilityLinks);

    @FormUrlEncoded
    @POST("public/update-technology.json.php")
    Call<StatusResponse> updateTechnology(@Field("fromApp") boolean fromApp, @Field("technology_id") int technologyID, @Field("technology_name") String technologyName, @Field("technology_description") String technologyDescription, @Field("technology_website") String technologyWebsite, @Field("technology_rrp") double technologyRRP, @Field("technology_manufacturer_id") int technologyManufacturerID, @Field("condition_links[]") List<Integer> conditionLinks, @FieldMap Map<String, Object> abilityLinks);

    @FormUrlEncoded
    @POST("public/delete-technologies.json.php")
    Call<StatusResponse> deleteTechnologies(@Field("fromApp") boolean fromApp, @Field("technology_ids[]") List<Integer> conditionLinks);


    @GET("public/training.json.php")
    Call<QuizResultsResponse> getTrainingResults(@Query("uid") String uid);

    @FormUrlEncoded
    @POST("public/training.json.php")
    Call<StatusResponse> storeTrainingResult(@Field("fromApp") boolean fromApp, @Field("fb_uid") String firebaseUserID, @Field("quiz_name") String quizName, @Field("quiz_score") double quizScore);
}
