<resources>
    <string name="app_name">EduAbility</string>
    <string name="title_activity_settings">Settings</string>

    <!-- Preference Titles -->
    <string name="messages_header">Messages</string>
    <string name="sync_header">Sync</string>
    <string name="security_header">Security</string>

    <!-- Messages Preferences -->
    <string name="signature_title">Your signature</string>
    <string name="reply_title">Default reply action</string>

    <!-- Sync Preferences -->
    <string name="sync_title">Sync email periodically</string>
    <string name="attachment_title">Download incoming attachments</string>
    <string name="attachment_summary_on">Automatically download attachments for incoming emails
    </string>
    <string name="attachment_summary_off">Only download attachments when manually requested</string>

    <!-- Common Strings -->
    <string name="preference_not_set">Not set</string>

    <!-- Administrator Strings -->
    <string name="ability_mapping_helper_description">Please select the <b>minimum ability level</b> required for interaction with this Assistive Technology or select N/A is the ability is not required.</string>
    <string name="condition_mapping_helper_description">Please select the conditions that are suitable for this Assistive Technology:</string>

    <string name="delete_assistive_technology_warning">Are you sure you wish to delete the selected Assistive Technologies? <br /><br />This will <b>permanently remove</b> them from the database.</string>
    <string name="search_hint">Search...</string>
    <string name="action_search">Search</string>

    <string name="tab_text_1">Assessments</string>
    <string name="tab_text_2">Training</string>
    <string name="tab_text_3">Quizzes</string>
    <string name="tab_text_4">Browse</string>

    <string-array name="contrast_theme_entries">
        <item>White on Black</item>
        <item>Yellow on Black</item>
    </string-array>

    <string-array name="contrast_theme_values">
        <item>white_on_black</item>
        <item>yellow_on_black</item>
    </string-array>

    <!-- Button size arrays -->
    <string-array name="button_size_entries">
        <item>Small</item>
        <item>Normal</item>
        <item>Large</item>
    </string-array>
    
    <string-array name="button_size_values">
        <item>small</item>
        <item>normal</item>
        <item>large</item>
    </string-array>

    <string name="read_aloud_button_description">Read text aloud</string>
    <string name="read_aloud_button_stop_description">Stop reading aloud</string>

    <!-- Voice Gender arrays -->
    <!-- <string-array name="voice_gender_entries">
        <item>Male Voice</item>
        <item>Female Voice</item>
    </string-array> -->
    
    <string-array name="voice_gender_values">
        <item>male</item>
        <item>female</item>
    </string-array>
</resources>
