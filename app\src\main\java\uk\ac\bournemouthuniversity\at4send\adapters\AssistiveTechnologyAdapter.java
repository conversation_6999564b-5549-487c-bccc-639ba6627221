package uk.ac.bournemouthuniversity.at4send.adapters;

import android.content.Context;
import android.content.res.ColorStateList;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.core.content.res.ResourcesCompat;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.models.AssistiveTechnology;
import uk.ac.bournemouthuniversity.at4send.utils.ContrastUtils;
import uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils;

public class AssistiveTechnologyAdapter extends RecyclerView.Adapter<AssistiveTechnologyAdapter.ViewHolder> {
    private static final String TAG = "AssistiveTechnologyAdapter";

    public static final int DISPLAY_LIST = 0;
    public static final int DISPLAY_SELECTION = 1;

    private List<AssistiveTechnology> technologyList;
    private Context context;
    private AssistiveTechnologyActionListener actionListener;

    private int displayType = DISPLAY_LIST;

    public class ViewHolder extends RecyclerView.ViewHolder {
        AppCompatTextView name, technologyShortDescription;
        AppCompatImageView technologyImage, selectionIcon;
        LinearLayout itemView;
        public boolean selected = false;

        public ViewHolder(View view) {
            super(view);
            itemView = view.findViewById(R.id.itemView);
            technologyImage = view.findViewById(R.id.assistiveTechnologyImage);
            name = view.findViewById(R.id.assistivename);
            technologyShortDescription = view.findViewById(R.id.assistiveTechnologyShortDescription);
            selectionIcon = view.findViewById(R.id.selectionIcon);
        }
    }

    public AssistiveTechnologyAdapter(Context context, List<AssistiveTechnology> technologyList, AssistiveTechnologyActionListener actionListener){
        this(context, technologyList, actionListener, DISPLAY_LIST);
    }

    public AssistiveTechnologyAdapter(Context context, List<AssistiveTechnology> technologyList, AssistiveTechnologyActionListener actionListener, int displayType) {
        this.technologyList = technologyList;
        this.context = context;
        this.actionListener = actionListener;
        this.displayType = displayType;
    }

    @NonNull
    @Override
    public AssistiveTechnologyAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_assistive_technology, parent, false);

        return new AssistiveTechnologyAdapter.ViewHolder(itemView);
    }

    @Override
    public void onBindViewHolder(@NonNull final AssistiveTechnologyAdapter.ViewHolder holder, final int position) {
        final AssistiveTechnology item = technologyList.get(position);

        holder.name.setText(item.getTechnologyName());
        holder.technologyShortDescription.setText(item.getTechnologyDescription());

        holder.itemView.setOnClickListener(v -> {
            if(displayType == DISPLAY_SELECTION){
                holder.selected = !holder.selected;
                if(holder.selected) {
                    holder.itemView.setBackgroundColor(ResourcesCompat.getColor(context.getResources(), R.color.red50, null));
                    holder.selectionIcon.setImageDrawable(ResourcesCompat.getDrawable(context.getResources(), R.drawable.ic_checkbox_marked, null));
                    holder.selectionIcon.setImageTintList(ColorStateList.valueOf(ResourcesCompat.getColor(context.getResources(), R.color.md_red_500, null)));
                }else{
                    holder.itemView.setBackgroundColor(ResourcesCompat.getColor(context.getResources(), R.color.white, null));
                    holder.selectionIcon.setImageDrawable(ResourcesCompat.getDrawable(context.getResources(), R.drawable.ic_checkbox_blank_outline, null));
                    holder.selectionIcon.setImageTintList(ColorStateList.valueOf(ResourcesCompat.getColor(context.getResources(), R.color.md_grey_500, null)));
                }
            }
            actionListener.onClick(item);
        });

        if(displayType == DISPLAY_SELECTION){
            holder.selectionIcon.setImageDrawable(ResourcesCompat.getDrawable(context.getResources(), R.drawable.ic_checkbox_blank_outline, null));
        }

        // Apply high contrast if enabled
        if (ContrastUtils.isHighContrastEnabled(context)) {
            // Apply text color to the name and description
            holder.name.setTextColor(TextSizeUtils.getTextColor(context).getDefaultColor());
            holder.technologyShortDescription.setTextColor(TextSizeUtils.getTextColor(context).getDefaultColor());
            
            // Apply background color to the item view
            if (!holder.selected) {
                holder.itemView.setBackgroundTintList(TextSizeUtils.getBackgroundColor(context));
            }
            
            // Apply tint to the selection icon if visible
            if (displayType == DISPLAY_SELECTION && holder.selectionIcon != null) {
                if (holder.selected) {
                    holder.selectionIcon.setColorFilter(TextSizeUtils.getTextColor(context).getDefaultColor());
                } else {
                    holder.selectionIcon.setColorFilter(TextSizeUtils.getTextColor(context).getDefaultColor());
                }
            }
        }
    }

    @Override
    public int getItemCount() {
        return technologyList.size();
    }


    public interface AssistiveTechnologyActionListener{
        void onClick(AssistiveTechnology technology);
    }
}

