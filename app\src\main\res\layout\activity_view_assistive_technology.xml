<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/md_grey_50"
    tools:context=".technologies.AssistiveTechnologyListActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="180dp"
        android:fitsSystemWindows="true"
        android:gravity="bottom"
        app:elevation="0dp">

        <com.google.android.material.appbar.CollapsingToolbarLayout
            android:id="@+id/toolbar_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fitsSystemWindows="true"
            app:contentScrim="?attr/colorPrimary"
            app:layout_scrollFlags="scroll|exitUntilCollapsed|snap"
            app:expandedTitleTextAppearance="@style/AT4SEND.AppTheme.TextAppearance.Title"
            app:collapsedTitleTextAppearance="@style/AT4SEND.AppTheme.TextAppearance.Title"
            app:toolbarId="@id/toolbar"
            app:collapsedTitleGravity="center|start"
            app:expandedTitleGravity="bottom|start"
            app:expandedTitleMarginStart="16dp"
            app:expandedTitleMarginBottom="22dp"
            app:title="Assistive Technology Name"
            app:theme="@style/AT4SEND.BlueTheme.NoActionBar.Toolbar">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="bottom"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingBottom="22dp">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/toolbar_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textAppearance="@style/AT4SEND.AppTheme.TextAppearance.Title"
                    android:textColor="@color/white"
                    tools:text="Assistive Technology Name"/>

            </LinearLayout>
            

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                app:contentInsetStartWithNavigation="0dp"
                app:titleTextAppearance="@style/AT4SEND.AppTheme.TextAppearance.Title"
                app:theme="@style/AT4SEND.BlueTheme.NoActionBar.Toolbar"
                app:layout_collapseMode="pin"
                />

        </com.google.android.material.appbar.CollapsingToolbarLayout>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingTop="24dp">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/md_white_1000"
                app:cardCornerRadius="0dp"
                app:strokeWidth="1dp"
                app:strokeColor="@color/md_grey_200"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="center_vertical"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:paddingTop="12dp"
                        android:paddingBottom="12dp">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textStyle="bold"
                            android:textSize="17sp"
                            android:text="Description"
                            android:layout_marginBottom="2dp"/>

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/fullTechnologyDescription"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            tools:text="Full Description which can cover multiple lines of text and support fully formatted rich text. Full Description which can cover multiple lines of text and support fully formatted rich text."/>
                    </LinearLayout>

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/md_white_1000"
                app:cardCornerRadius="0dp"
                app:strokeWidth="1dp"
                app:strokeColor="@color/md_grey_200"
                app:cardElevation="0dp"
                android:layout_marginTop="20dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="center_vertical"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:paddingTop="12dp"
                        android:paddingBottom="12dp">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textStyle="bold"
                            android:textSize="17sp"
                            android:text="Product Images"
                            android:layout_marginBottom="2dp"/>

                        <HorizontalScrollView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:fillViewport="true">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:clipToPadding="false"
                                android:clipChildren="false"
                                android:paddingTop="8dp"
                                android:paddingBottom="8dp">

                                <com.google.android.material.card.MaterialCardView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    app:cardBackgroundColor="@color/md_white_1000"
                                    app:cardCornerRadius="6dp"
                                    app:cardElevation="2dp"
                                    android:layout_marginEnd="20dp"
                                    android:foreground="?attr/selectableItemBackgroundBorderless"
                                    android:clickable="true"
                                    android:focusable="true">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:orientation="vertical">

                                        <androidx.appcompat.widget.AppCompatImageView
                                            android:layout_width="180dp"
                                            android:layout_height="120dp"
                                            android:scaleType="centerCrop"
                                            app:srcCompat="@drawable/illustration_learning_blackboard"
                                            />

                                    </LinearLayout>

                                </com.google.android.material.card.MaterialCardView>

                                <com.google.android.material.card.MaterialCardView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    app:cardBackgroundColor="@color/md_white_1000"
                                    app:cardCornerRadius="6dp"
                                    app:cardElevation="2dp"
                                    android:layout_marginEnd="20dp"
                                    android:foreground="?attr/selectableItemBackgroundBorderless"
                                    android:clickable="true"
                                    android:focusable="true">

                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:orientation="vertical">

                                        <androidx.appcompat.widget.AppCompatImageView
                                            android:layout_width="180dp"
                                            android:layout_height="120dp"
                                            android:scaleType="centerCrop"
                                            app:srcCompat="@drawable/illustration_working_desk"
                                            />

                                    </LinearLayout>

                                </com.google.android.material.card.MaterialCardView>
                            </LinearLayout>

                        </HorizontalScrollView>
                    </LinearLayout>

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/md_white_1000"
                app:cardCornerRadius="0dp"
                app:strokeWidth="1dp"
                app:strokeColor="@color/md_grey_200"
                app:cardElevation="0dp"
                android:layout_marginTop="20dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="center_vertical"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:paddingTop="12dp"
                        android:paddingBottom="12dp">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textStyle="bold"
                            android:textSize="17sp"
                            android:text="Manufacturer"
                            android:layout_marginBottom="2dp"/>

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/technologyManufacturerName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            tools:text="Manufacturer Name" />
                    </LinearLayout>

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:visibility="gone"
                app:cardBackgroundColor="@color/md_white_1000"
                app:cardCornerRadius="0dp"
                app:cardElevation="0dp"
                app:strokeColor="@color/md_grey_200"
                app:strokeWidth="1dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="vertical"
                        android:paddingStart="16dp"
                        android:paddingTop="12dp"
                        android:paddingEnd="16dp"
                        android:paddingBottom="12dp">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="2dp"
                            android:text="Product RRP"
                            android:textSize="17sp"
                            android:textStyle="bold" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/technologyRRP"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            tools:text="£100.00" />
                    </LinearLayout>

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/md_white_1000"
                app:cardCornerRadius="0dp"
                app:strokeWidth="1dp"
                app:strokeColor="@color/md_grey_200"
                app:cardElevation="0dp"
                android:layout_marginTop="20dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:id="@+id/technologyWebsiteClickArea"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="center_vertical"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:paddingTop="12dp"
                        android:paddingBottom="12dp"
                        android:background="?selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textStyle="bold"
                            android:textSize="17sp"
                            android:text="Visit Website"
                            android:layout_marginBottom="2dp"/>

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/technologyWebsite"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textColor="@color/md_blue_600"
                            tools:text="www.producturl.co.uk/product.xyz" />
                    </LinearLayout>

                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>