package uk.ac.bournemouthuniversity.at4send.models;

import androidx.annotation.ColorRes;

import com.google.firebase.firestore.Exclude;
import com.google.gson.annotations.SerializedName;

import uk.ac.bournemouthuniversity.at4send.R;

public class Ability {

    public static final int VIEW_TYPE_ITEM = 0;
    public static final int VIEW_TYPE_HEAD = 1;

    public static final int NO_ID = -1;
    public static final int NO_RATING = -1;

    @SerializedName("ability_id")
    int abilityID;

    @SerializedName("ability_name")
    String abilityName;

    @SerializedName("link_threshold")
    int abilityRating = NO_RATING;

    private int viewType = VIEW_TYPE_ITEM;

    public Ability(int abilityID, String abilityName, int abilityRating){
        this.abilityID = abilityID;
        this.abilityName = abilityName;
        this.abilityRating = abilityRating;
    }

    public Ability(int abilityID, String abilityName){
        this.abilityID = abilityID;
        this.abilityName = abilityName;
    }

    public Ability(String abilityName, int viewType){
        this.abilityID = NO_ID;
        this.abilityName = abilityName;
        this.viewType = viewType;
    }

    public String getAbilityName() {
        return abilityName;
    }

    public void setAbilityName(String abilityName) {
        this.abilityName = abilityName;
    }

    public int getViewType() {
        return viewType;
    }

    public void setViewType(int viewType) {
        this.viewType = viewType;
    }

    public int getAbilityRating() {
        return abilityRating;
    }

    public void setAbilityRating(int abilityRating) {
        this.abilityRating = abilityRating;
    }

    public int getAbilityID() {
        return abilityID;
    }

    public void setAbilityID(int abilityID) {
        this.abilityID = abilityID;
    }

    @Exclude
    @ColorRes
    public static int getRatingColor(int activityFatigueLevel) {
        switch(activityFatigueLevel){
            case 4:
                return R.color.md_red_500;
            case 3:
                return R.color.md_orange_500;
            case 2:
                return R.color.md_yellow_600;
            case 1:
                return R.color.md_light_green_400;
            case 0:
                return R.color.md_green_300;
            default:
                return R.color.md_grey_300;
        }
    }
}
