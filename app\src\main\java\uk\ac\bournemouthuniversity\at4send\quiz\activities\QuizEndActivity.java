package uk.ac.bournemouthuniversity.at4send.quiz.activities;

import android.app.Activity;
import android.content.res.ColorStateList;
import android.graphics.Typeface;
import android.os.Bundle;
import android.util.Log;
import android.view.Menu;
import android.view.View;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.appcompat.app.AppCompatActivity;
import androidx.activity.OnBackPressedCallback;

import com.afollestad.materialdialogs.MaterialDialog;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

import org.jetbrains.annotations.NotNull;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.api.AT4SENDAPIClient;
import uk.ac.bournemouthuniversity.at4send.api.AT4SENDAPIInterface;
import uk.ac.bournemouthuniversity.at4send.models.response.StatusResponse;
import uk.ac.bournemouthuniversity.at4send.quiz.functions.Quiz;
import uk.ac.bournemouthuniversity.at4send.quiz.helpers.QuizHolder;
import uk.ac.bournemouthuniversity.at4send.quiz.helpers.QuizSession;
import uk.ac.bournemouthuniversity.at4send.quiz.helpers.SessionUtils;
import uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils;
import uk.ac.bournemouthuniversity.at4send.BaseActivity;

public class QuizEndActivity extends BaseActivity {
    private static final String TAG = "QuizEndActivity";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_quiz_end);
        
        getOnBackPressedDispatcher().addCallback(this, new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                QuizHolder.getInstance().setExited(true);
                finish();
            }
        });
        
        showStats();
        saveResult();

        findViewById(R.id.restart).setOnClickListener(view -> restart());
        findViewById(R.id.returnHome).setOnClickListener(view -> {
            QuizHolder.getInstance().setExited(true);
            finish();
        });
    }

    public void showStats() {
        Quiz quiz = QuizHolder.getInstance();
        if (quiz != null) {
            QuizSession session = quiz.getSession();
            ((TextView)findViewById(R.id.quizName)).setText("Quiz: " + session.getQuizName());
            ((TextView)findViewById(R.id.totalAnswers)).setText("Total Answers: " + session.getTotalAttempts());
            ((TextView)findViewById(R.id.correctAnswers)).setText("Correct Answers: " + session.getCorrectAttempts());
            ((TextView)findViewById(R.id.correctAnswersRate)).setText("Correctness Rate: " + session.getCorrectnessRate());
            ((TextView)findViewById(R.id.bestSequence)).setText("Best Correct Sequence: " + session.getBestConsecutiveAttempts());
            ((TextView)findViewById(R.id.overallRecord)).setText("Record: " + quiz.getRecord());

            TextView summaryText = findViewById(R.id.quizSummary);
            // Replace the problematic setTextAppearance with direct style attributes
            summaryText.setTextSize(24); // Set an appropriate text size
            summaryText.setTypeface(null, Typeface.BOLD);
            
            if(session.getCorrectnessRateRaw() >= 80){
                summaryText.setText("Congratulations, you have passed this Quiz!");
                summaryText.setTextColor(ContextCompat.getColor(this, 
                    TextSizeUtils.isHighContrastEnabled(this) ? R.color.md_green_900 : R.color.md_green_800));
            } else {
                summaryText.setText("Sorry, you have failed this Quiz. Please try again.");
                summaryText.setTextColor(ContextCompat.getColor(this, 
                    TextSizeUtils.isHighContrastEnabled(this) ? R.color.md_red_900 : R.color.md_red_800));
            }
        }
    }

    public void restart() {
        SessionUtils.setSession(this, new QuizSession(QuizHolder.getInstance().getSession().getQuizName(), false));
        QuizHolder.getInstance().reset();
        finish();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        // Inflate the menu; this adds items to the action bar if it is present.
        // getMenuInflater().inflate(R.menu.main, menu);
        return true;
    }

    private void saveResult(){

        AT4SENDAPIInterface apiService =
                AT4SENDAPIClient.getClient().create(AT4SENDAPIInterface.class);

        Call<StatusResponse> call = apiService.storeTrainingResult(true, FirebaseAuth.getInstance().getCurrentUser().getUid(), QuizHolder.getInstance().getSession().getQuizName(), QuizHolder.getInstance().getSession().getCorrectnessRateRaw());
        call.enqueue(new Callback<StatusResponse>() {
            @Override
            public void onResponse(@NotNull Call<StatusResponse>call, @NotNull Response<StatusResponse> response) {
                findViewById(R.id.loader).setVisibility(View.GONE);
                if(response.body() != null) {
                    String status = response.body().getStatus();

                    if (status.equalsIgnoreCase("success")) {
                        findViewById(R.id.saved).setVisibility(View.VISIBLE);
                    }else{
                        new MaterialDialog(QuizEndActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                                .title(null, "Error Occurred")
                                .message(null, String.format("%s%s%s", "An error occurred whilst submitting your quiz results: ", "\""+response.body().getMessage()+"\"", "Please try again later."), null)
                                .cancelable(false)
                                .positiveButton(null, "Dismiss", materialDialog -> {
                                    materialDialog.dismiss();
                                    return null;
                                }).show();
                    }
                }else{
                    new MaterialDialog(QuizEndActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                            .title(null, "Error Occurred")
                            .message(null, "An error occurred whilst submitting your quiz results. Please try again later.", null)
                            .cancelable(false)
                            .positiveButton(null, "Dismiss", materialDialog -> {
                                materialDialog.dismiss();
                                return null;
                            }).show();
                }
            }

            @Override
            public void onFailure(@NotNull Call<StatusResponse>call, @NotNull Throwable t) {
                // Log error here since request failed
                Log.e(TAG, t.toString());
                FirebaseCrashlytics.getInstance().recordException(t);

                new MaterialDialog(QuizEndActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                        .title(null, "Error Occurred")
                        .message(null, "An error occurred whilst submitting your quiz results. Please try again later.", null)
                        .cancelable(false)
                        .positiveButton(null, "Dismiss", materialDialog -> {
                            materialDialog.dismiss();
                            return null;
                        }).show();
            }
        });
    }
}
