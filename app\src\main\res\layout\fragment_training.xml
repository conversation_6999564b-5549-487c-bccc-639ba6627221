<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior"
    tools:context=".fragments.HomeFragment">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/md_white_1000"
                app:cardCornerRadius="0dp"
                app:strokeWidth="1dp"
                app:strokeColor="@color/md_grey_200"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:paddingTop="12dp"
                        android:paddingBottom="12dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?attr/selectableItemBackground">

                        <FrameLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="12dp">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="64dp"
                                android:layout_height="64dp"
                                android:layout_gravity="center"
                                android:background="@drawable/ic_circle"
                                android:backgroundTint="@color/md_grey_200"
                                app:srcCompat="@drawable/ic_user_profile"
                                android:padding="6dp"
                                app:tint="@color/md_grey_500"/>

                            <com.google.android.material.imageview.ShapeableImageView
                                android:id="@+id/accountImage"
                                android:layout_width="64dp"
                                android:layout_height="64dp"
                                android:scaleType="centerCrop"
                                android:adjustViewBounds="true"
                                app:shapeAppearanceOverlay="@style/AT4SEND.ShapeableImageView.Circle" />

                        </FrameLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/accountName"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                tools:text="Kevin Redhead"
                                android:textSize="17sp"
                                android:textAppearance="@style/AT4SEND.AppTheme.TextAppearance.Subtitle"
                                android:textFontWeight="500"/>

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="EduAbility User"
                                />

                        </LinearLayout>

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/accountLogout"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                            android:text="Logout"
                            android:letterSpacing="0"
                            android:textColor="@color/md_red_700"
                            app:strokeColor="@color/md_red_500"
                            app:backgroundTint="@color/md_red_50"
                            app:rippleColor="@color/md_red_700"
                            android:layout_marginStart="8dp" />

                    </LinearLayout>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/md_white_1000"
                app:cardCornerRadius="0dp"
                app:strokeWidth="1dp"
                app:strokeColor="@color/md_grey_200"
                app:cardElevation="0dp"
                android:layout_marginTop="16dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/md_grey_200"/>

                    <LinearLayout
                        android:id="@+id/training_technologies"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:paddingTop="12dp"
                        android:paddingBottom="12dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?attr/selectableItemBackground">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            app:cardBackgroundColor="@color/md_purple_600"
                            app:cardCornerRadius="6dp"
                            app:cardElevation="0dp"
                            app:contentPadding="12dp"
                            android:layout_gravity="center"
                            android:layout_marginEnd="16dp">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                app:srcCompat="@drawable/ic_learn"
                                app:tint="@color/md_white_1000"
                                app:cornerRadius="8dp"
                                />

                        </com.google.android.material.card.MaterialCardView>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Training"
                                android:textSize="17sp"
                                android:textAppearance="@style/AT4SEND.AppTheme.TextAppearance.Subtitle"
                                android:textFontWeight="500"/>

                            <!--
                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Begin a new assessment."
                                />
                            -->

                        </LinearLayout>

                        <androidx.appcompat.widget.AppCompatImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            app:srcCompat="@drawable/ic_chevron_right"
                            app:tint="@color/md_grey_500"/>
                    </LinearLayout>


                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/md_grey_200"/>

                    <LinearLayout
                        android:id="@+id/training_learn"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:paddingTop="12dp"
                        android:paddingBottom="12dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?attr/selectableItemBackground">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            app:cardBackgroundColor="@color/md_red_700"
                            app:cardCornerRadius="6dp"
                            app:cardElevation="0dp"
                            app:contentPadding="12dp"
                            android:layout_gravity="center"
                            android:layout_marginEnd="16dp">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                app:srcCompat="@drawable/ic_pdf_file"
                                app:tint="@color/md_white_1000"
                                app:cornerRadius="8dp"
                                />

                        </com.google.android.material.card.MaterialCardView>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Learn"
                                android:textSize="17sp"
                                android:textAppearance="@style/AT4SEND.AppTheme.TextAppearance.Subtitle"
                                android:textFontWeight="500"/>

                            <!--
                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Begin a new assessment."
                                />
                            -->

                        </LinearLayout>

                        <androidx.appcompat.widget.AppCompatImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            app:srcCompat="@drawable/ic_chevron_right"
                            app:tint="@color/md_grey_500"/>
                    </LinearLayout>


                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/md_grey_200"/>

                    <LinearLayout
                        android:id="@+id/training_videos"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:paddingTop="12dp"
                        android:paddingBottom="12dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?attr/selectableItemBackground">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            app:cardBackgroundColor="@color/md_orange_600"
                            app:cardCornerRadius="6dp"
                            app:cardElevation="0dp"
                            app:contentPadding="12dp"
                            android:layout_gravity="center"
                            android:layout_marginEnd="16dp">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                app:srcCompat="@drawable/ic_video"
                                app:tint="@color/md_white_1000"
                                app:cornerRadius="8dp"
                                />

                        </com.google.android.material.card.MaterialCardView>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Videos"
                                android:textSize="17sp"
                                android:textAppearance="@style/AT4SEND.AppTheme.TextAppearance.Subtitle"
                                android:textFontWeight="500"/>

                            <!--
                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Begin a new assessment."
                                />
                            -->

                        </LinearLayout>

                        <androidx.appcompat.widget.AppCompatImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            app:srcCompat="@drawable/ic_chevron_right"
                            app:tint="@color/md_grey_500"/>
                    </LinearLayout>
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/md_grey_200"/>

                    <LinearLayout
                        android:id="@+id/training_results"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:paddingTop="12dp"
                        android:paddingBottom="12dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?attr/selectableItemBackground">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            app:cardBackgroundColor="@color/md_green_900"
                            app:cardCornerRadius="6dp"
                            app:cardElevation="0dp"
                            app:contentPadding="12dp"
                            android:layout_gravity="center"
                            android:layout_marginEnd="16dp">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                app:srcCompat="@drawable/ic_results"
                                app:tint="@color/md_white_1000"
                                app:cornerRadius="8dp"
                                />

                        </com.google.android.material.card.MaterialCardView>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Training Results"
                                android:textSize="17sp"
                                android:textAppearance="@style/AT4SEND.AppTheme.TextAppearance.Subtitle"
                                android:textFontWeight="500"/>

                            <!--
                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Begin a new assessment."
                                />
                            -->

                        </LinearLayout>

                        <androidx.appcompat.widget.AppCompatImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            app:srcCompat="@drawable/ic_chevron_right"
                            app:tint="@color/md_grey_500"/>
                    </LinearLayout>
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/md_grey_200"/>

                    <LinearLayout
                        android:id="@+id/training_feedback"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:paddingTop="12dp"
                        android:paddingBottom="12dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?attr/selectableItemBackground">

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/training_feedback_card"
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            app:cardBackgroundColor="@color/md_grey_600"
                            app:cardCornerRadius="6dp"
                            app:cardElevation="0dp"
                            app:contentPadding="12dp"
                            android:layout_gravity="center"
                            android:layout_marginEnd="16dp">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                app:srcCompat="@drawable/ic_feedback"
                                app:tint="@color/md_white_1000"
                                app:cornerRadius="8dp"
                                />

                        </com.google.android.material.card.MaterialCardView>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Feedback"
                                android:textSize="17sp"
                                android:textAppearance="@style/AT4SEND.AppTheme.TextAppearance.Subtitle"
                                android:textFontWeight="500"/>

                            <!--
                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Begin a new assessment."
                                />
                            -->

                        </LinearLayout>

                        <androidx.appcompat.widget.AppCompatImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            app:srcCompat="@drawable/ic_chevron_right"
                            app:tint="@color/md_grey_500"/>
                    </LinearLayout>
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/md_grey_200"/>

                    <LinearLayout
                        android:id="@+id/training_help"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:paddingTop="12dp"
                        android:paddingBottom="12dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?attr/selectableItemBackground">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            app:cardBackgroundColor="@color/md_blue_600"
                            app:cardCornerRadius="6dp"
                            app:cardElevation="0dp"
                            app:contentPadding="12dp"
                            android:layout_gravity="center"
                            android:layout_marginEnd="16dp">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                app:srcCompat="@drawable/ic_help"
                                app:tint="@color/md_white_1000"
                                app:cornerRadius="8dp"
                                />

                        </com.google.android.material.card.MaterialCardView>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Help"
                                android:textSize="17sp"
                                android:textAppearance="@style/AT4SEND.AppTheme.TextAppearance.Subtitle"
                                android:textFontWeight="500"/>

                            <!--
                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Begin a new assessment."
                                />
                            -->

                        </LinearLayout>

                        <androidx.appcompat.widget.AppCompatImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            app:srcCompat="@drawable/ic_chevron_right"
                            app:tint="@color/md_grey_500"/>
                    </LinearLayout>
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>