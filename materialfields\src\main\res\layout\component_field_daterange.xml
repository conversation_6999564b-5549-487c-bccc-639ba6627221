<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/field_container"
        android:layout_width="match_parent"
        android:layout_height="72dp"
        android:background="@color/white"
        android:clickable="true"
        android:focusable="true"
        android:foreground="?android:attr/selectableItemBackground">

        <LinearLayout
            android:layout_width="72dp"
            android:layout_height="72dp"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/field_image"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/calendar_range"
                android:tint="@color/grey600"
                app:srcCompat="@drawable/calendar_range" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/linearLayout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="72dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="16dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/field_switch"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/field_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="DateRange"
                android:textColor="@color/item_list_title_color"
                android:textSize="@dimen/item_list_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/field_subtitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="0dp"
                android:text="Value"
                android:textColor="@color/item_list_subtitle_color"
                android:textSize="@dimen/item_list_subtitle"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/module_type_title" />

        </LinearLayout>

        <Switch
            android:id="@+id/field_switch"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="8dp"
            app:layout_constraintEnd_toStartOf="@+id/field_alert_image"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/field_alert_image"
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:layout_marginEnd="16dp"
            android:src="@drawable/alert_circle"
            android:tint="@color/red500"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/alert_circle" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/field_daterange_dates_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/field_daterange_startdate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?android:attr/selectableItemBackground">

            <TextView
                android:layout_width="48dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="0dp"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp"
                android:text="From"
                android:textAlignment="viewEnd"
                android:textAllCaps="true"
                android:textColor="@color/grey600"
                android:textSize="@dimen/item_list_subsubtitle"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/field_daterange_startdate_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="72dp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"
                android:text="Sat, Mar 12, 2018"
                android:textColor="@color/black"
                android:textSize="@dimen/item_list_subtitle"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/field_daterange_startdate_time"
                android:layout_width="wrap_content"
                android:layout_height="19dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="16dp"
                android:text="10:00"
                android:textColor="@color/black"
                android:textSize="@dimen/item_list_subtitle"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/field_daterange_enddate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?android:attr/selectableItemBackground">

            <TextView
                android:layout_width="48dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="0dp"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp"
                android:text="To"
                android:textAlignment="viewEnd"
                android:textAllCaps="true"
                android:textColor="@color/grey600"
                android:textSize="@dimen/item_list_subsubtitle"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/field_daterange_enddate_date"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="72dp"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="16dp"
                android:text="Sat, Mar 12, 2018"
                android:textColor="@color/black"
                android:textSize="@dimen/item_list_subtitle"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/field_daterange_enddate_time"
                android:layout_width="wrap_content"
                android:layout_height="19dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="16dp"
                android:text="10:00"
                android:textColor="@color/black"
                android:textSize="@dimen/item_list_subtitle"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>

    <View
        android:id="@+id/field_bottom_line_separator"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginBottom="0dp"
        android:background="@color/grey300"
        app:layout_constraintBottom_toBottomOf="parent" />

</LinearLayout>