<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/mainView"
    android:background="@color/md_grey_50"
    tools:context=".technologies.AssistiveTechnologyListActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:elevation="0dp">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            app:contentInsetStartWithNavigation="0dp"
            app:titleTextAppearance="@style/AT4SEND.AppTheme.TextAppearance.Title"
            app:title="Quiz Results"
            app:titleTextColor="@color/white"
            app:theme="@style/AT4SEND.BlueTheme.NoActionBar.Toolbar">

        </androidx.appcompat.widget.Toolbar>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:fillViewport="true"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/returnHomeContainer">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardBackgroundColor="@color/md_white_1000"
                    app:cardCornerRadius="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="center_vertical"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:paddingTop="8dp"
                        android:paddingBottom="8dp">

                        <TextView
                            android:id="@+id/quizSummary"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Congratulations, you answered all the questions."
                            android:textSize="16sp"
                            android:layout_marginBottom="32dp"/>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Here are some statistics:"
                            android:textSize="16sp" />

                        <TextView
                            android:id="@+id/quizName"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:gravity="center_horizontal|bottom"
                            android:text=""
                            android:textAppearance="@android:style/TextAppearance.Medium"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/totalAnswers"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:gravity="center_horizontal|bottom"
                            android:text=""
                            android:textAppearance="@android:style/TextAppearance.Medium"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/correctAnswers"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:gravity="center_horizontal|bottom"
                            android:text=""
                            android:textAppearance="@android:style/TextAppearance.Medium"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/correctAnswersRate"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:gravity="center_horizontal|bottom"
                            android:text=""
                            android:textAppearance="@android:style/TextAppearance.Medium"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/bestSequence"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:gravity="center_horizontal|bottom"
                            android:text=""
                            android:textAppearance="@android:style/TextAppearance.Medium"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/overallRecord"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="10dp"
                            android:gravity="center_horizontal|bottom"
                            android:text=""
                            android:textAppearance="@android:style/TextAppearance.Medium"
                            android:textStyle="bold" />

                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <LinearLayout
                    android:id="@+id/loader"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center">
                    <me.zhanghai.android.materialprogressbar.MaterialProgressBar
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="@dimen/keyline_1_minus_8dp"/>

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Saving Progress..."
                        android:textColor="@color/md_grey_600" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/saved"
                    android:visibility="gone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center">
                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:layout_marginEnd="@dimen/keyline_1_minus_8dp"
                        app:srcCompat="@drawable/ic_checkbox_marked"
                        app:tint="@color/md_green_600"/>

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Progress Saved"
                        android:textColor="@color/md_green_600" />
                </LinearLayout>

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/returnHomeContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardBackgroundColor="@color/md_white_1000"
            app:cardCornerRadius="0dp"
            app:strokeWidth="1dp"
            app:strokeColor="@color/md_grey_200"
            app:cardElevation="0dp"
            android:layout_marginTop="20dp"
            app:layout_constraintBottom_toBottomOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center_horizontal"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingTop="12dp"
                android:paddingBottom="12dp">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/restart"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:letterSpacing="0"
                    style="@style/Widget.MaterialComponents.Button.UnelevatedButton"
                    android:text="RESTART QUIZ"
                    app:backgroundTint="@color/md_blue_600" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/returnHome"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:letterSpacing="0"
                    style="@style/Widget.MaterialComponents.Button.UnelevatedButton"
                    android:text="RETURN TO QUIZZES SCREEN"
                    app:backgroundTint="@color/grey700"/>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>