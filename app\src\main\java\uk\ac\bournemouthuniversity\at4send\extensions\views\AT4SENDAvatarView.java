package uk.ac.bournemouthuniversity.at4send.extensions.views;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import androidx.cardview.widget.CardView;

import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.appcompat.widget.TintTypedArray;
import androidx.core.content.res.ResourcesCompat;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.target.Target;

import com.google.android.material.imageview.ShapeableImageView;
import uk.ac.bournemouthuniversity.at4send.R;

public class AT4SENDAvatarView extends FrameLayout {

    ShapeableImageView imgAvatar;
    AppCompatTextView textAvatar;

    public AT4SENDAvatarView(Context context){
        super(context);
    }

    public AT4SENDAvatarView(Context context, AttributeSet attrs) {
        super(context, attrs);
        initAttrs(context, attrs);
    }

    public AT4SENDAvatarView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs);
        initAttrs(context, attrs);
    }

    private void initAttrs(Context context, AttributeSet attrs){
        View v = LayoutInflater.from(context).inflate(R.layout.avatar_view, this);
        imgAvatar = v.findViewById(R.id.round_img_avatar);
        textAvatar = v.findViewById(R.id.text_avatar_name);

        TintTypedArray styledAttrs = TintTypedArray.obtainStyledAttributes(context, attrs, R.styleable.AT4SENDAvatarView);
        int cornerRadius = styledAttrs.getDimensionPixelSize(R.styleable.AT4SENDAvatarView_avatarCornerRadius, 0);
        int bgColor = styledAttrs.getColor(R.styleable.AT4SENDAvatarView_avatarBackgroundColor, Color.parseColor("#964357"));
        int textColor = styledAttrs.getColor(R.styleable.AT4SENDAvatarView_avatarTextColor, Color.parseColor("#FFFFFF"));
        float textSize = styledAttrs.getDimension(R.styleable.AT4SENDAvatarView_avatarTextSize, 24f);
        String initials = styledAttrs.getString(R.styleable.AT4SENDAvatarView_avatarInitials);
        styledAttrs.recycle();

        //setting bg color and corner radius
        GradientDrawable drawable = (GradientDrawable) textAvatar.getBackground();
        drawable.setCornerRadius(cornerRadius);
        drawable.setColor(bgColor);
        textAvatar.invalidate();

        //text color and size
        textAvatar.setTextColor(textColor);
        textAvatar.setTextSize(TypedValue.COMPLEX_UNIT_SP, textSize);

        //text font
        if(!isInEditMode()) {
            textAvatar.setTypeface(ResourcesCompat.getFont(context, R.font.asap_medium));
        }

        if(initials != null) {
            String spacedInitials = initials.replaceAll(".(?=.)", "$0 ");
            bindInitials(spacedInitials);
        }

        // Set corner radius on CardView parent of ImageView
        View parent = (View) imgAvatar.getParent();
        if (parent instanceof CardView) {
            ((CardView) parent).setRadius(cornerRadius);
        }
    }

    public void bindInitials(String name) {
        bind(name, null);
    }

    public void bind(String name, String pic) {
        textAvatar.setVisibility(VISIBLE);
        if(!TextUtils.isEmpty(name)){
            String[] nameArray = name.split(" ");
            StringBuilder initial = new StringBuilder();
            for (String s : nameArray) {
                initial.append(s.charAt(0));
            }
            textAvatar.setText(initial.toString());
        }

        if (TextUtils.isEmpty(pic)) {
            imgAvatar.setImageDrawable(null);
        } else {
            Glide.with(getContext())
                    .load(pic)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .listener(new RequestListener<Drawable>() {
                        @Override
                        public boolean onLoadFailed(@Nullable GlideException e, Object model, Target<Drawable> target, boolean isFirstResource) {
                            return false;
                        }

                        @Override
                        public boolean onResourceReady(Drawable resource, Object model, Target<Drawable> target, DataSource dataSource, boolean isFirstResource) {
                            textAvatar.setVisibility(GONE);
                            return false;
                        }
                    })
                    .into(imgAvatar);
        }
    }
}


