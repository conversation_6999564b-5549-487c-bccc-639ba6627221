<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/trainingFragment">

    <fragment
        android:id="@+id/trainingFragment"
        android:name="com.eduability.at4send2.fragments.TrainingFragment"
        android:label="Training"
        tools:layout="@layout/fragment_training">
        <action
            android:id="@+id/action_trainingFragment_to_trainingFeedbackFragment"
            app:destination="@id/trainingFeedbackFragment" />
    </fragment>

    <fragment
        android:id="@+id/trainingFeedbackFragment"
        android:name="com.eduability.at4send2.fragments.TrainingFeedbackFragment"
        android:label="Feedback"
        tools:layout="@layout/fragment_training_feedback" />

</navigation> 