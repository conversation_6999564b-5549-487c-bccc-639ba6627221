package uk.ac.bournemouthuniversity.at4send.adapters;

import android.content.Context;
import android.content.res.ColorStateList;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.core.content.res.ResourcesCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.afollestad.materialdialogs.MaterialDialog;

import java.util.List;

import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.extensions.ui.UIUtils;
import uk.ac.bournemouthuniversity.at4send.extensions.views.indicatorseekbar.IndicatorSeekBar;
import uk.ac.bournemouthuniversity.at4send.extensions.views.indicatorseekbar.OnSeekChangeListener;
import uk.ac.bournemouthuniversity.at4send.extensions.views.indicatorseekbar.SeekParams;
import uk.ac.bournemouthuniversity.at4send.models.Ability;
import uk.ac.bournemouthuniversity.at4send.utils.ContrastUtils;
import uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils;

public class AbilitiesAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    private static final String TAG = "AbilitiesAdapter";

    private List<Ability> sectionList;
    private Context context;
    private boolean isAdmin;
    private OnAbilityItemChangeListener onAbilityItemChangeListener;

    public class ItemViewHolder extends RecyclerView.ViewHolder {
        AppCompatTextView abilityItemName;
        public IndicatorSeekBar ratingSeekBar;
        AppCompatImageView helpIcon;

        public ItemViewHolder(View view) {
            super(view);
            abilityItemName = view.findViewById(R.id.abilityItemName);
            ratingSeekBar = view.findViewById(R.id.rating_seek_bar);
            helpIcon = view.findViewById(R.id.helpIcon);
        }
    }


    public class HeaderViewHolder extends RecyclerView.ViewHolder {
        AppCompatTextView abilitySectionName;

        public HeaderViewHolder(View view) {
            super(view);
            abilitySectionName = view.findViewById(R.id.abilitySectionName);
        }
    }

    public AbilitiesAdapter(Context context, List<Ability> sectionList){
        this(context,  sectionList, null,false);
    }

    public AbilitiesAdapter(Context context, List<Ability> sectionList, OnAbilityItemChangeListener itemChangeListener){
        this(context,  sectionList, itemChangeListener,false);
    }

    public AbilitiesAdapter(Context context, List<Ability> sectionList, OnAbilityItemChangeListener itemChangeListener, boolean isAdmin) {
        this.sectionList = sectionList;
        this.context = context;
        this.isAdmin = isAdmin;
        this.onAbilityItemChangeListener = itemChangeListener;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        if(viewType == Ability.VIEW_TYPE_ITEM) {
            View itemView;
            if(isAdmin) {
                 itemView = LayoutInflater.from(parent.getContext())
                        .inflate(R.layout.item_ability_rating_scale_admin, parent, false);
            }else{
                itemView = LayoutInflater.from(parent.getContext())
                        .inflate(R.layout.item_ability_rating_scale,parent, false);
            }
            return new ItemViewHolder(itemView);
        }else{
            View itemView;
            if(isAdmin) {
                itemView = LayoutInflater.from(parent.getContext())
                        .inflate(R.layout.item_ability_section_header_admin, parent, false);
            }else{
                itemView = LayoutInflater.from(parent.getContext())
                        .inflate(R.layout.item_ability_section_header, parent, false);
            }

            return new HeaderViewHolder(itemView);
        }
    }

    @Override
    public void onBindViewHolder(@NonNull final RecyclerView.ViewHolder holder, final int position) {
        final Ability section = sectionList.get(position);

        if(getItemViewType(position) == Ability.VIEW_TYPE_HEAD){
            setUpHeaderItem(section, (HeaderViewHolder) holder);
        }else if(getItemViewType(position) == Ability.VIEW_TYPE_ITEM){
            setUpSectionItem(section, (ItemViewHolder) holder);
        }

        // Apply high contrast if enabled
        if (ContrastUtils.isHighContrastEnabled(context)) {
            // Apply text color to all text elements
            if (holder instanceof ItemViewHolder) {
                ItemViewHolder itemHolder = (ItemViewHolder) holder;
                itemHolder.abilityItemName.setTextColor(TextSizeUtils.getTextColor(context).getDefaultColor());
                
                // Apply text color to the tick texts in the seek bar
                if (itemHolder.ratingSeekBar != null) {
                    itemHolder.ratingSeekBar.tickTextsColor(TextSizeUtils.getTextColor(context).getDefaultColor());
                }
            }
            
            if (holder instanceof HeaderViewHolder) {
                HeaderViewHolder headerHolder = (HeaderViewHolder) holder;
                headerHolder.abilitySectionName.setTextColor(TextSizeUtils.getTextColor(context).getDefaultColor());
            }
            
            // Apply contrast to any help icons
            if (holder instanceof ItemViewHolder) {
                ItemViewHolder itemHolder = (ItemViewHolder) holder;
                if (itemHolder.helpIcon != null) {
                    itemHolder.helpIcon.setColorFilter(
                        TextSizeUtils.getTextColor(context).getDefaultColor(),
                        android.graphics.PorterDuff.Mode.SRC_IN
                    );
                }
            }
            
            // Apply background color to the item view
            holder.itemView.setBackgroundTintList(TextSizeUtils.getBackgroundColor(context));
        }
    }

    private void setUpHeaderItem(Ability section, HeaderViewHolder holder){
        holder.abilitySectionName.setText(section.getAbilityName());
    }

    private void setUpSectionItem(Ability section, ItemViewHolder holder) {
        final boolean[] isAbilityRatingSet = {false};

        holder.abilityItemName.setText(section.getAbilityName());
        
        // Add help icon click listener
        holder.helpIcon.setOnClickListener(v -> {
            String descriptionResourceName = "ability_desc_" + 
                section.getAbilityName().toLowerCase().replace(" ", "_").replace("/", "_");
            int descriptionResId = context.getResources().getIdentifier(
                descriptionResourceName, "string", context.getPackageName());
            
            if (descriptionResId != 0) {
                MaterialDialog dialog = new MaterialDialog(context, MaterialDialog.getDEFAULT_BEHAVIOR())
                    .title(null, section.getAbilityName())
                    .message(null, context.getString(descriptionResId), null)
                    .positiveButton(null, "Got it", materialDialog -> {
                        materialDialog.dismiss();
                        return null;
                    });
                
                // Apply high contrast to the dialog if enabled
                dialog.show();
                
                if (ContrastUtils.isHighContrastEnabled(context)) {
                    // Apply contrast after the dialog is shown
                    ContrastUtils.applyContrastToDialog(dialog);
                }
            }
        });

        // Apply high contrast to the tick texts if enabled
        if (ContrastUtils.isHighContrastEnabled(context)) {
            // Get the text color from high contrast settings
            int textColor = TextSizeUtils.getTextColor(context).getDefaultColor();
            
            // Apply text color to the tick texts
            holder.ratingSeekBar.tickTextsColor(textColor);
        }

        if(!isAdmin) {
            section.setAbilityRating(-1);

            holder.ratingSeekBar.setThumbDrawable(ResourcesCompat.getDrawable(context.getResources(), R.drawable.transparent_thumb, null));
            holder.ratingSeekBar.setOnSeekChangeListener(new OnSeekChangeListener() {
                @Override
                public void onSeeking(SeekParams seekParams) {
                    int color = ResourcesCompat.getColor(context.getResources(), Ability.getRatingColor(seekParams.progress), null);
                    holder.ratingSeekBar.setProgressTrackColor(color);
                    holder.ratingSeekBar.setThumbDrawable(UIUtils.makeThumbDrawable(context.getResources(), context.getTheme(), color));
                    holder.ratingSeekBar.tickMarksColor(UIUtils.makeTickMarkColorStateList(context.getResources(), color));

                    //TODO: Adapt "Indicator" (Tooltip) to the color of the rating (v2)
                    holder.ratingSeekBar.setIndicatorColor(color);
                    
                    // Apply high contrast to the tick texts if enabled
                    if (ContrastUtils.isHighContrastEnabled(context)) {
                        holder.ratingSeekBar.tickTextsColor(TextSizeUtils.getTextColor(context).getDefaultColor());
                    }
                }

                @Override
                public void onStartTrackingTouch(IndicatorSeekBar seekBar) {
                    if (!isAbilityRatingSet[0]) {
                        holder.ratingSeekBar.setThumbDrawable(ResourcesCompat.getDrawable(context.getResources(), R.drawable.thumb, context.getTheme()));
                    }
                    isAbilityRatingSet[0] = true;
                }

                @Override
                public void onStopTrackingTouch(IndicatorSeekBar seekBar) {
                    int color = ResourcesCompat.getColor(context.getResources(), Ability.getRatingColor(seekBar.getProgress()), null);
                    holder.ratingSeekBar.setProgressTrackColor(color);
                    holder.ratingSeekBar.setThumbDrawable(UIUtils.makeThumbDrawable(context.getResources(), context.getTheme(), color));
                    holder.ratingSeekBar.tickMarksColor(UIUtils.makeTickMarkColorStateList(context.getResources(), color));

                    //TODO: Adapt "Indicator" (Tooltip) to the color of the rating (v2)
                    holder.ratingSeekBar.setIndicatorColor(color);
                    section.setAbilityRating(seekBar.getProgress());

                    if(onAbilityItemChangeListener != null) {
                        onAbilityItemChangeListener.onAbilityItemChanged();
                    }
                }
            });
        }else{
            holder.ratingSeekBar.setIndicatorTextFormat("${TICK_TEXT}");
            holder.ratingSeekBar.setOnSeekChangeListener(new OnSeekChangeListener() {
                @Override
                public void onSeeking(SeekParams seekParams) {
                    int color = ResourcesCompat.getColor(context.getResources(), Ability.getRatingColor(seekParams.progress), null);
                    holder.ratingSeekBar.setProgressTrackColor(color);
                    holder.ratingSeekBar.setThumbDrawable(UIUtils.makeThumbDrawable(context.getResources(), context.getTheme(), color));
                    holder.ratingSeekBar.tickMarksColor(UIUtils.makeTickMarkColorStateList(context.getResources(), color));

                    //TODO: Adapt "Indicator" (Tooltip) to the color of the rating (v2)
                    holder.ratingSeekBar.setIndicatorColor(color);
                    
                    // Apply high contrast to the tick texts if enabled
                    if (ContrastUtils.isHighContrastEnabled(context)) {
                        holder.ratingSeekBar.tickTextsColor(TextSizeUtils.getTextColor(context).getDefaultColor());
                    }
                }

                @Override
                public void onStartTrackingTouch(IndicatorSeekBar seekBar) { }

                @Override
                public void onStopTrackingTouch(IndicatorSeekBar seekBar) {
                    int color = ResourcesCompat.getColor(context.getResources(), Ability.getRatingColor(seekBar.getProgress()), null);
                    holder.ratingSeekBar.setProgressTrackColor(color);
                    holder.ratingSeekBar.setThumbDrawable(UIUtils.makeThumbDrawable(context.getResources(), context.getTheme(), color));
                    holder.ratingSeekBar.tickMarksColor(UIUtils.makeTickMarkColorStateList(context.getResources(), color));

                    //TODO: Adapt "Indicator" (Tooltip) to the color of the rating (v2)
                    holder.ratingSeekBar.setIndicatorColor(color);

                    section.setAbilityRating(seekBar.getProgress());

                    onAbilityItemChangeListener.onAbilityItemChanged();
                }
            });
        }
    }

    @Override
    public int getItemCount() {
        return sectionList.size();
    }

    @Override
    public int getItemViewType(int position) {
        return sectionList.get(position).getViewType();
    }

    public interface OnAbilityItemChangeListener {
        void onAbilityItemChanged();
    }

    /**
     * Applies high contrast settings to a MaterialDialog
     */
    private void applyHighContrastToDialog(MaterialDialog dialog) {
        if (dialog == null) return;
        
        // Get the dialog view
        View dialogView = dialog.getView();
        if (dialogView == null) return;
        
        // Get high contrast colors
        int textColor = TextSizeUtils.getTextColor(context).getDefaultColor();
        ColorStateList backgroundColor = TextSizeUtils.getBackgroundColor(context);
        
        // Apply background color to the dialog
        dialogView.setBackgroundTintList(backgroundColor);
        
        // Find and apply text color to title
        TextView titleView = dialogView.findViewById(com.afollestad.materialdialogs.R.id.md_text_title);
        if (titleView != null) {
            titleView.setTextColor(textColor);
        }
        
        // Find and apply text color to message
        TextView messageView = dialogView.findViewById(com.afollestad.materialdialogs.R.id.md_text_message);
        if (messageView != null) {
            messageView.setTextColor(textColor);
        }
        
        // Find and apply text color to buttons
        Button positiveButton = dialogView.findViewById(com.afollestad.materialdialogs.R.id.md_button_positive);
        if (positiveButton != null) {
            positiveButton.setTextColor(textColor);
        }
        
        Button negativeButton = dialogView.findViewById(com.afollestad.materialdialogs.R.id.md_button_negative);
        if (negativeButton != null) {
            negativeButton.setTextColor(textColor);
        }
        
        Button neutralButton = dialogView.findViewById(com.afollestad.materialdialogs.R.id.md_button_neutral);
        if (neutralButton != null) {
            neutralButton.setTextColor(textColor);
        }
        
        // Apply contrast to all views in the dialog
        ContrastUtils.applyContrastToViewHierarchy(dialogView);
    }
}

