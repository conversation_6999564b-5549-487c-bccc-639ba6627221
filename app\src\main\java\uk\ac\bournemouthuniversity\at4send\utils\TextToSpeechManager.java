package uk.ac.bournemouthuniversity.at4send.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.speech.tts.TextToSpeech;
import android.speech.tts.UtteranceProgressListener;
import android.speech.tts.Voice;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.style.BackgroundColorSpan;
import android.util.Log;
import android.widget.TextView;
import android.widget.Toast;

import androidx.core.content.ContextCompat;
import androidx.preference.PreferenceManager;
import uk.ac.bournemouthuniversity.at4send.R;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Set;

public class TextToSpeechManager {
    private static final String TAG = "TextToSpeechManager";
    private static TextToSpeechManager instance;
    private TextToSpeech textToSpeech;
    private boolean isInitialized = false;
    private boolean isSpeaking = false;
    private Context context;
    private SharedPreferences preferences;
    
    // New fields for word tracking
    private TextView currentTextView;
    private String[] words;
    private List<Integer> wordStartIndices;
    private List<Integer> wordEndIndices;
    private int highlightColor;
    private Spannable originalText;
    private int currentWordIndex = 0;

    private TextToSpeechManager(Context context) {
        this.context = context;
        this.preferences = PreferenceManager.getDefaultSharedPreferences(context);
        highlightColor = ContextCompat.getColor(context, R.color.colorAccent);
        
        textToSpeech = new TextToSpeech(context, status -> {
            if (status == TextToSpeech.SUCCESS) {
                int result = textToSpeech.setLanguage(Locale.UK);
                isInitialized = (result != TextToSpeech.LANG_MISSING_DATA && 
                                result != TextToSpeech.LANG_NOT_SUPPORTED);
                
                if (isInitialized) {
                    // Set up voice based on preference
                    updateVoiceGender();
                }
                
                // Set up progress listener for word tracking
                textToSpeech.setOnUtteranceProgressListener(new UtteranceProgressListener() {
                    @Override
                    public void onStart(String utteranceId) {
                        // Reset to first word when starting
                        currentWordIndex = 0;
                        highlightCurrentWord();
                    }

                    @Override
                    public void onDone(String utteranceId) {
                        // Clear highlighting when done
                        resetHighlighting();
                        isSpeaking = false;
                    }

                    @Override
                    public void onError(String utteranceId) {
                        // Clear highlighting on error
                        resetHighlighting();
                        isSpeaking = false;
                    }
                    
                    @Override
                    public void onRangeStart(String utteranceId, int start, int end, int frame) {
                        // This is called when each range (word) starts
                        // Find which word this corresponds to
                        for (int i = 0; i < wordStartIndices.size(); i++) {
                            if (wordStartIndices.get(i) <= start && wordEndIndices.get(i) >= start) {
                                currentWordIndex = i;
                                highlightCurrentWord();
                                break;
                            }
                        }
                    }
                });
            } else {
                Log.e(TAG, "TTS initialization failed with status: " + status);
            }
        });
        
        // Register preference change listener
        preferences.registerOnSharedPreferenceChangeListener(preferenceChangeListener);
    }

    private final SharedPreferences.OnSharedPreferenceChangeListener preferenceChangeListener = 
        (sharedPreferences, key) -> {
            if (key.equals("voice_gender")) {
                updateVoiceGender();
            }
        };

    private void updateVoiceGender() {
        if (!isInitialized) {
            Log.d(TAG, "TTS not initialized, cannot update voice gender");
            return;
        }
        
        String preferredGender = preferences.getString("voice_gender", "female");
        Log.d(TAG, "Updating voice gender. Preferred gender: " + preferredGender);
        
        Set<Voice> voices = textToSpeech.getVoices();
        if (voices == null || voices.isEmpty()) {
            Log.e(TAG, "No voices available from TTS engine");
            return;
        }

        Voice bestVoice = null;
        int bestQuality = -1;

        for (Voice voice : voices) {
            Log.d(TAG, "Checking voice: " + voice.getName() + 
                      ", Locale: " + voice.getLocale() + 
                      ", Quality: " + voice.getQuality());
            
            // Check if voice matches our locale and gender requirements
            if (voice.getLocale().getLanguage().equals(Locale.UK.getLanguage())) {
                String voiceName = voice.getName().toLowerCase();
                boolean isMatchingGender = 
                    (preferredGender.equals("male") && voiceName.contains("male")) ||
                    (preferredGender.equals("female") && (voiceName.contains("female") || (!voiceName.contains("male"))));
                
                if (isMatchingGender && voice.getQuality() > bestQuality) {
                    bestVoice = voice;
                    bestQuality = voice.getQuality();
                    Log.d(TAG, "Found better matching voice: " + voice.getName());
                }
            }
        }

        if (bestVoice != null) {
            Log.d(TAG, "Setting voice to: " + bestVoice.getName());
            int result = textToSpeech.setVoice(bestVoice);
            if (result == TextToSpeech.SUCCESS) {
                Log.d(TAG, "Successfully set voice to: " + bestVoice.getName());
            } else {
                Log.e(TAG, "Failed to set voice. Error code: " + result);
                // Fallback to basic pitch adjustment
                adjustVoiceCharacteristics(preferredGender);
            }
        } else {
            Log.e(TAG, "No suitable voice found for gender: " + preferredGender);
            // Fallback to basic pitch adjustment
            adjustVoiceCharacteristics(preferredGender);
        }
    }

    private void adjustVoiceCharacteristics(String gender) {

        // Set language first
        textToSpeech.setLanguage(Locale.UK);

        Set<String> a=new HashSet<>();
        a.add("male");//here you can give male if you want to select male voice.
        Voice v=new Voice("en-in-x-ene-network",new Locale("en","GB"),10,10,true, a);
        Log.d(TAG, "Voice type: " + v.getName());
        textToSpeech.setVoice(v);
        textToSpeech.setSpeechRate(0.8f);

        Log.d(TAG, "Adjusted voice characteristics - Gender: " + gender );
    }

    public static synchronized TextToSpeechManager getInstance(Context context) {
        if (instance == null) {
            instance = new TextToSpeechManager(context.getApplicationContext());
        }
        return instance;
    }

    public void speak(String text, TextView textView) {
        if (!isInitialized) {
            Toast.makeText(context, "Text-to-speech not available", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (isSpeaking) {
            stop();
        }
        
        // Store the TextView and prepare for word tracking
        currentTextView = textView;
        prepareWordTracking(text);
        
        // Set up parameters for utterance progress
        Bundle params = new Bundle();
        params.putInt(TextToSpeech.Engine.KEY_PARAM_UTTERANCE_ID, 1);
        
        // Start speaking with utterance ID for tracking
        textToSpeech.speak(text, TextToSpeech.QUEUE_FLUSH, params, "WordTrackingUtterance");
        isSpeaking = true;
    }
    
    // Keep the original speak method for backward compatibility
    public void speak(String text) {
        if (isInitialized) {
            if (isSpeaking) {
                stop();
            }
            textToSpeech.speak(text, TextToSpeech.QUEUE_FLUSH, null, null);
            isSpeaking = true;
        } else {
            Toast.makeText(context, "Text-to-speech not available", Toast.LENGTH_SHORT).show();
        }
    }

    public void stop() {
        if (isInitialized && textToSpeech.isSpeaking()) {
            textToSpeech.stop();
            resetHighlighting();
            isSpeaking = false;
        }
    }
    
    private void prepareWordTracking(String text) {
        // Store original text as spannable for highlighting
        originalText = new SpannableString(text);
        currentTextView.setText(originalText);
        
        // Split text into words and store their positions
        words = text.split("\\s+");
        wordStartIndices = new ArrayList<>();
        wordEndIndices = new ArrayList<>();
        
        int currentIndex = 0;
        for (String word : words) {
            int startIndex = text.indexOf(word, currentIndex);
            if (startIndex >= 0) {
                int endIndex = startIndex + word.length() - 1;
                wordStartIndices.add(startIndex);
                wordEndIndices.add(endIndex);
                currentIndex = endIndex + 1;
            }
        }
    }
    
    private void highlightCurrentWord() {
        if (currentTextView == null || originalText == null || 
            currentWordIndex < 0 || currentWordIndex >= wordStartIndices.size()) {
            return;
        }
        
        // Create a new spannable to avoid accumulating spans
        Spannable spannableText = new SpannableString(originalText);
        
        // Apply highlight to current word
        int start = wordStartIndices.get(currentWordIndex);
        int end = wordEndIndices.get(currentWordIndex) + 1;
        spannableText.setSpan(new BackgroundColorSpan(highlightColor), 
                             start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        
        // Update the TextView on the UI thread
        if (currentTextView != null) {
            currentTextView.post(() -> currentTextView.setText(spannableText));
        }
    }
    
    private void resetHighlighting() {
        if (currentTextView != null && originalText != null) {
            currentTextView.post(() -> currentTextView.setText(originalText));
        }
    }

    public boolean isSpeaking() {
        return isSpeaking;
    }

    public void shutdown() {
        if (textToSpeech != null) {
            textToSpeech.stop();
            textToSpeech.shutdown();
            isSpeaking = false;
        }
    }

    @Override
    protected void finalize() throws Throwable {
        if (preferences != null) {
            preferences.unregisterOnSharedPreferenceChangeListener(preferenceChangeListener);
        }
        super.finalize();
    }
}



