:com.eduability.at4send2.fragments.TrainingFeedbackFragment2com.eduability.at4send2.fragments.TrainingFragment;uk.ac.bournemouthuniversity.at4send.data.quiz.QuizDataStore;<EMAIL>/uk.ac.bournemouthuniversity.at4send.BuildConfig:uk.ac.bournemouthuniversity.at4send.email.models.EmailTypeMuk.ac.bournemouthuniversity.at4send.email.templates.NotificationEmailTemplateAuk.ac.bournemouthuniversity.at4send.email.templates.EmailTemplate>uk.ac.bournemouthuniversity.at4send.email.SendGridEmailServiceLuk.ac.bournemouthuniversity.at4send.email.SendGridEmailService.EmailCallback>uk.ac.bournemouthuniversity.at4send.email.models.EmailResponseDuk.ac.bournemouthuniversity.at4send.activities.EmailSettingsActivityHuk.ac.bournemouthuniversity.at4send.email.templates.SupportEmailTemplateEuk.ac.bournemouthuniversity.at4send.email.validation.ValidationResultOuk.ac.bournemouthuniversity.at4send.email.EmailConfigurationHelper.TestCallbackAuk.ac.bournemouthuniversity.at4send.email.error.EmailErrorHandlerCuk.ac.bournemouthuniversity.at4send.email.validation.EmailValidator=uk.ac.bournemouthuniversity.at4send.email.error.ErrorSeverity=uk.ac.bournemouthuniversity.at4send.email.models.EmailRequestBuk.ac.bournemouthuniversity.at4send.email.EmailConfigurationHelper>uk.ac.bournemouthuniversity.at4send.email.error.EmailErrorTypeGuk.ac.bournemouthuniversity.at4send.email.templates.SystemEmailTemplateIuk.ac.bournemouthuniversity.at4send.email.templates.FeedbackEmailTemplate8uk.ac.bournemouthuniversity.at4send.email.SendGridConfigOuk.ac.bournemouthuniversity.at4send.email.templates.TrainingResultEmailTemplate:uk.ac.bournemouthuniversity.at4send.email.error.EmailErrorHuk.ac.bournemouthuniversity.at4send.email.templates.EmailTemplateManager                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            