package uk.ac.bournemouthuniversity.at4send.quiz.helpers;

import uk.ac.bournemouthuniversity.at4send.quiz.functions.Quiz;

public class QuizHolder {
    private static Quiz instance = null;
    
    public static Quiz getInstance() {
        if (instance == null) {
            // Instead of throwing IllegalAccessError, return null
            return null;
        }
        return instance;
    }

    public static void setInstance(Quiz quiz) {
        instance = quiz;
    }

    public static boolean isInit() {
        return instance != null;
    }
}
