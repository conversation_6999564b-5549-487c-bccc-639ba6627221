package uk.ac.bournemouthuniversity.at4send.fragments;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.appcompat.widget.AppCompatImageView;
import androidx.fragment.app.Fragment;

import com.afollestad.materialdialogs.MaterialDialog;
import com.bumptech.glide.Glide;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseUser;

import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.BaseFragment;
import uk.ac.bournemouthuniversity.at4send.SplashScreenActivity;
import uk.ac.bournemouthuniversity.at4send.extensions.helpers.PreferenceManager;
import uk.ac.bournemouthuniversity.at4send.extensions.utils.Network;
import uk.ac.bournemouthuniversity.at4send.training.LearnActivity;
import uk.ac.bournemouthuniversity.at4send.training.TrainingActivity;
import uk.ac.bournemouthuniversity.at4send.training.TrainingFeedbackActivity;
import uk.ac.bournemouthuniversity.at4send.training.TrainingHelpActivity;
import uk.ac.bournemouthuniversity.at4send.training.TrainingResultsActivity;
import uk.ac.bournemouthuniversity.at4send.training.VideoActivity;
import uk.ac.bournemouthuniversity.at4send.utils.ContrastUtils;
import uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils;

public class TrainingFragment extends BaseFragment {

    public TrainingFragment() {
        // Required empty public constructor
    }

    public static TrainingFragment newInstance() {
        return new TrainingFragment();
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View view = inflater.inflate(R.layout.fragment_training, container, false);

        FirebaseUser user = FirebaseAuth.getInstance().getCurrentUser();
        ((TextView) view.findViewById(R.id.accountName)).setText(user.getDisplayName());
        Glide.with(requireContext()).load(user.getPhotoUrl()).into(((AppCompatImageView)view.findViewById(R.id.accountImage)));

        view.findViewById(R.id.accountLogout).setOnClickListener(v -> {
            new MaterialDialog(requireContext(), MaterialDialog.getDEFAULT_BEHAVIOR())
                    .title(null, "Logout Confirmation")
                    .message(null, "Are you sure you wish to logout of EduAbility?", null)
                    .cancelable(false)
                    .negativeButton(null, "Yes", materialDialog -> {
                        materialDialog.dismiss();
                        FirebaseAuth.getInstance().signOut();
                        new PreferenceManager(requireContext()).clear();
                        requireActivity().finish();
                        requireContext().startActivity(new Intent(requireContext(), SplashScreenActivity.class));
                        return null;
                    })
                    .positiveButton(null, "No", materialDialog -> {
                        materialDialog.dismiss();
                        return null;
                    }).show();
        });

        // Set up click listeners for training buttons
        view.findViewById(R.id.training_technologies).setOnClickListener(v -> requireContext().startActivity(new Intent(requireContext(), TrainingActivity.class)));
        view.findViewById(R.id.training_learn).setOnClickListener(v -> requireContext().startActivity(new Intent(requireContext(), LearnActivity.class)));
        view.findViewById(R.id.training_videos).setOnClickListener(v -> requireContext().startActivity(new Intent(requireContext(), VideoActivity.class)));
        view.findViewById(R.id.training_results).setOnClickListener(v -> requireContext().startActivity(new Intent(requireContext(), TrainingResultsActivity.class)));
        view.findViewById(R.id.training_help).setOnClickListener(v -> requireContext().startActivity(new Intent(requireContext(), TrainingHelpActivity.class)));

        // Set up click listener for feedback option
        view.findViewById(R.id.training_feedback).setOnClickListener(v -> {
            if(!Network.isNetworkAvailable(requireActivity())){
                new MaterialDialog(requireContext(), MaterialDialog.getDEFAULT_BEHAVIOR())
                        .title(null, "Network Access Required")
                        .message(null, "An active network connection is required for this function. Please connect to the internet and try again.", null)
                        .cancelable(false)
                        .positiveButton(null, "Dismiss", materialDialog -> {
                            materialDialog.dismiss();
                            return null;
                        }).show();
                return;
            }
            
            Intent i = new Intent(requireActivity(), TrainingFeedbackActivity.class);
            startActivity(i);
        });

        return view;
    }
}
