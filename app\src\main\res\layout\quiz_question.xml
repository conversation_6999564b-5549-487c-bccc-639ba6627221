<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/question_layout"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:layout_weight="3"
    android:gravity="center|center_horizontal"
    android:orientation="horizontal"
    android:baselineAligned="false">

    <FrameLayout
        android:id="@+id/container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone" />

    <LinearLayout
        android:layout_width="0dip"
        android:layout_weight="1"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/questionImage"
            android:layout_width="180dp"
            android:layout_height="180dp"
            android:layout_marginBottom="24dp"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/question"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            tools:text="Hello"
            android:textSize="22sp"
            android:textAppearance="@android:style/TextAppearance.Large" />
    </LinearLayout>

</LinearLayout>