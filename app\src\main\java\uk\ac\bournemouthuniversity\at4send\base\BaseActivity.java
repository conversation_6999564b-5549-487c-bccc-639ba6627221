
package uk.ac.bournemouthuniversity.at4send.base;

import android.content.res.Configuration;
import android.os.Bundle;
import androidx.appcompat.app.AppCompatActivity;
import uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils;

public class BaseActivity extends AppCompatActivity {
    private boolean isInitialCreate = true;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        if (isInitialCreate) {
            applyTextSize();
            isInitialCreate = false;
        }
        super.onCreate(savedInstanceState);
    }

    @Override
    protected void onResume() {
        super.onResume();
        applyTextSize();
    }

    private void applyTextSize() {
        float scale = TextSizeUtils.getTextSizeScale(this);
        Configuration configuration = getResources().getConfiguration();
        if (Math.abs(configuration.fontScale - scale) >= 0.01f) {
            TextSizeUtils.applyTextSize(this);
        }
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        // Only apply text size if it's not a font scale change
        if (Math.abs(newConfig.fontScale - TextSizeUtils.getTextSizeScale(this)) >= 0.01f) {
            applyTextSize();
        }
    }
}



