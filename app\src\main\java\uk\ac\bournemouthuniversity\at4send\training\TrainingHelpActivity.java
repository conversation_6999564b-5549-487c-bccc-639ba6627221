package uk.ac.bournemouthuniversity.at4send.training;

import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateInterpolator;
import android.animation.Animator;
import android.animation.TimeInterpolator;
import android.animation.ValueAnimator;

import androidx.fragment.app.FragmentActivity;
import androidx.preference.PreferenceManager;
import androidx.viewpager2.widget.ViewPager2;

import com.google.android.material.button.MaterialButton;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import kotlin.Pair;
import me.relex.circleindicator.CircleIndicator3;
import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.adapters.training.TrainingHelpSlideAdapter;
import uk.ac.bournemouthuniversity.at4send.data.training.DataStore;
import uk.ac.bournemouthuniversity.at4send.extensions.ui.transforms.DepthPageTransformation;
import uk.ac.bournemouthuniversity.at4send.utils.ContrastUtils;
import uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils;
import uk.ac.bournemouthuniversity.at4send.utils.ButtonSizeUtils;
import uk.ac.bournemouthuniversity.at4send.utils.TextToSpeechManager;
import uk.ac.bournemouthuniversity.at4send.utils.ReadAloudUtils;

public class TrainingHelpActivity extends FragmentActivity {

    private final List<Pair<String, String>> data = DataStore.INSTANCE.getTRAINING_HELP_SLIDE_DATA();
    private TrainingHelpSlideAdapter mAdapter;
    private ViewPager2 viewPager;
    private MaterialButton nextBtn;
    private MaterialButton prevBtn;
    private SharedPreferences preferences;
    private SharedPreferences.OnSharedPreferenceChangeListener preferenceChangeListener;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Apply text size before setting content view
        applyTextSize();
        
        setContentView(R.layout.activity_training_help);

        // Initialize preferences
        preferences = PreferenceManager.getDefaultSharedPreferences(this);
        
        // Set up preference change listener
        preferenceChangeListener = new SharedPreferences.OnSharedPreferenceChangeListener() {
            @Override
            public void onSharedPreferenceChanged(SharedPreferences sharedPreferences, String key) {
                if (key.equals("text_size") || key.equals("high_contrast") || key.equals("contrast_theme")) {
                    // Refresh the adapter to apply new text size or contrast settings
                    if (mAdapter != null) {
                        mAdapter.notifyDataSetChanged();
                    }
                    
                    // Apply high contrast if enabled
                    if (ContrastUtils.isHighContrastEnabled(TrainingHelpActivity.this)) {
                        applyHighContrastToUI();
                    }
                }
                
                // Apply button size when preference changes
                if (key.equals("button_size")) {
                    applyButtonSize();
                }
            }
        };
        
        // Register the listener
        preferences.registerOnSharedPreferenceChangeListener(preferenceChangeListener);

        init();
        
        // Apply high contrast if enabled
        if (ContrastUtils.isHighContrastEnabled(this)) {
            applyHighContrastToUI();
        }
        
        // Apply button size
        applyButtonSize();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Unregister the preference change listener
        if (preferences != null && preferenceChangeListener != null) {
            preferences.unregisterOnSharedPreferenceChangeListener(preferenceChangeListener);
        }
        // Stop TTS when activity is destroyed
        TextToSpeechManager.getInstance(this).stop();
    }
    
    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        // Apply text size if it's not a font scale change
        if (Math.abs(newConfig.fontScale - TextSizeUtils.getTextSizeScale(this)) >= 0.01f) {
            applyTextSize();
            
            // Refresh the adapter to apply new text size
            if (mAdapter != null) {
                mAdapter.notifyDataSetChanged();
            }
        }
    }
    
    private void applyTextSize() {
        float scale = TextSizeUtils.getTextSizeScale(this);
        Configuration configuration = getResources().getConfiguration();
        if (Math.abs(configuration.fontScale - scale) >= 0.01f) {
            TextSizeUtils.applyTextSize(this);
        }
    }

    private void init(){
        mAdapter = new TrainingHelpSlideAdapter(data);

        viewPager = findViewById(R.id.slideArea);
        viewPager.setAdapter(mAdapter);
        viewPager.registerOnPageChangeCallback(pageChangeCallback);
        viewPager.setPageTransformer(new DepthPageTransformation());

        // Add post-initialization callback to ensure read-aloud buttons are visible
        viewPager.post(() -> {
            // Force adapter to refresh and initialize read-aloud buttons
            mAdapter.notifyDataSetChanged();
            
            // Get the current page and force layout update
            View currentPage = viewPager.getChildAt(0);
            if (currentPage != null) {
                currentPage.post(() -> {
                    ViewGroup slideContent = currentPage.findViewById(R.id.slideContent);
                    if (slideContent != null) {
                        // Add read-aloud buttons to all TextViews in the slide
                        ReadAloudUtils.addReadAloudButtonsToViewGroup(this, slideContent);
                        slideContent.requestLayout();
                    }
                });
            }
        });

        CircleIndicator3 circleIndicator = findViewById(R.id.indicator);
        circleIndicator.setViewPager(viewPager);

        nextBtn = findViewById(R.id.nextBtn);
        nextBtn.setOnClickListener(v -> {
            int current = viewPager.getCurrentItem() + 1;
            if (current < data.size()) {
                setCurrentItem(viewPager, current, 800, new AccelerateInterpolator());
            } else {
                finish();
            }
        });

        prevBtn = findViewById(R.id.prevBtn);
        prevBtn.setOnClickListener(v -> {
            int current = viewPager.getCurrentItem() - 1;
            if(current >= 0){  // Changed from <= 0 to >= 0
                setCurrentItem(viewPager, current, 800, new AccelerateInterpolator());
            }
        });

        findViewById(R.id.close).setOnClickListener(v -> finish());
    }

    /**
     * Applies high contrast settings to the UI elements
     */
    private void applyHighContrastToUI() {
        // Apply contrast to the main view hierarchy
        ContrastUtils.applyContrastToViewHierarchy(findViewById(android.R.id.content));
        
        // Apply contrast to navigation buttons
        if (nextBtn != null) {
            nextBtn.setTextColor(TextSizeUtils.getTextColor(this).getDefaultColor());
            nextBtn.setBackgroundTintList(TextSizeUtils.getBackgroundColor(this));
        }
        
        if (prevBtn != null) {
            prevBtn.setTextColor(TextSizeUtils.getTextColor(this).getDefaultColor());
            prevBtn.setBackgroundTintList(TextSizeUtils.getBackgroundColor(this));
        }
        
        // Apply contrast to close button
        View closeBtn = findViewById(R.id.close);
        if (closeBtn != null) {
            closeBtn.setBackgroundTintList(TextSizeUtils.getBackgroundColor(this));
            if (closeBtn instanceof android.widget.ImageView) {
                ((android.widget.ImageView) closeBtn).setColorFilter(
                    TextSizeUtils.getTextColor(this).getDefaultColor(),
                    android.graphics.PorterDuff.Mode.SRC_IN
                );
            }
        }
        
        // Apply contrast to indicator
        CircleIndicator3 indicator = findViewById(R.id.indicator);
        if (indicator != null) {
            // Force adapter to refresh all items which will apply contrast to slides
            if (mAdapter != null) {
                mAdapter.notifyDataSetChanged();
            }
        }
        
        // Apply contrast to the root layout
        View rootLayout = findViewById(R.id.trainingHelpRootLayout);
        if (rootLayout != null) {
            rootLayout.setBackgroundTintList(TextSizeUtils.getBackgroundColor(this));
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        // Reapply high contrast when returning to activity
        if (ContrastUtils.isHighContrastEnabled(this)) {
            applyHighContrastToUI();
        }
    }

    public void setCurrentItem(ViewPager2 viewPager2, int item, long duration, TimeInterpolator interpolator){
        int pxToDrag = viewPager2.getWidth() * (item - viewPager2.getCurrentItem());
        ValueAnimator animator = ValueAnimator.ofInt(0, pxToDrag);
        AtomicInteger previousValue = new AtomicInteger();
        animator.addUpdateListener(animation -> {
            int currentValue = (int) animation.getAnimatedValue();
            float currentPxToDrag = (float) (currentValue - previousValue.get());
            viewPager2.fakeDragBy(-currentPxToDrag);
            previousValue.set(currentValue);
        });
        animator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {
                viewPager2.beginFakeDrag();
            }

            @Override
            public void onAnimationEnd(Animator animation) {
                viewPager2.endFakeDrag();
            }

            @Override
            public void onAnimationCancel(Animator animation) { }

            @Override
            public void onAnimationRepeat(Animator animation) { }
        });
        animator.setInterpolator(interpolator);
        animator.setDuration(duration);
        animator.start();
    }

    ViewPager2.OnPageChangeCallback pageChangeCallback = new ViewPager2.OnPageChangeCallback() {
        @Override
        public void onPageSelected(int position) {
            super.onPageSelected(position);

            if (position == data.size() - 1) {
                // last page. make button text to GOT IT
                ((MaterialButton) findViewById(R.id.nextBtn)).setText("FINISH");
            } else {
                // still pages are left
                ((MaterialButton) findViewById(R.id.nextBtn)).setText("NEXT");
            }

            if(position == 0){
                // First page, hide previous button
                prevBtn.setVisibility(View.INVISIBLE);
            } else {
                // Not on first page, show previous button
                prevBtn.setVisibility(View.VISIBLE);
            }

            // Ensure read-aloud buttons are visible on the current page
            viewPager.post(() -> {
                View currentPage = viewPager.getChildAt(0);
                if (currentPage != null) {
                    ViewGroup slideContent = currentPage.findViewById(R.id.slideContent);
                    if (slideContent != null) {
                        // Add read-aloud buttons to all TextViews in the slide
                        ReadAloudUtils.addReadAloudButtonsToViewGroup(TrainingHelpActivity.this, slideContent);
                        slideContent.requestLayout();
                    }
                }
            });
        }
    };

    /**
     * Applies button size settings to all buttons in the activity
     */
    private void applyButtonSize() {
        // Apply button size to the entire view hierarchy
        ButtonSizeUtils.applyButtonSizeToView(findViewById(android.R.id.content));
        
        // Specifically apply to navigation buttons
        if (nextBtn != null) {
            ButtonSizeUtils.forceButtonResize(nextBtn);
        }
        
        if (prevBtn != null) {
            ButtonSizeUtils.forceButtonResize(prevBtn);
        }
    }
}
