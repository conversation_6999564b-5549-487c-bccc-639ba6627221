<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/md_grey_50"
    tools:context=".admin.AdminHomeActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:elevation="0dp">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            app:contentInsetStartWithNavigation="0dp"
            app:titleTextAppearance="@style/AT4SEND.AppTheme.TextAppearance.Title"
            app:title="Administration Menu"
            app:theme="@style/AT4SEND.BlueTheme.NoActionBar.Toolbar">

        </androidx.appcompat.widget.Toolbar>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/bottomView">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardBackgroundColor="@color/md_white_1000"
                    app:cardCornerRadius="0dp"
                    app:strokeWidth="1dp"
                    app:strokeColor="@color/md_grey_200"
                    app:cardElevation="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:gravity="center_vertical"
                            android:paddingStart="16dp"
                            android:paddingEnd="16dp"
                            android:paddingTop="8dp"
                            android:paddingBottom="8dp">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Select the action you would like to perform:"
                                android:id="@+id/admin_instructions"
                                android:textSize="16sp"/>
                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="@color/md_grey_200"/>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:paddingStart="16dp"
                            android:paddingEnd="16dp"
                            android:paddingTop="12dp"
                            android:paddingBottom="12dp"
                            android:clickable="true"
                            android:focusable="true"
                            android:foreground="?attr/selectableItemBackground"
                            android:onClick="newAssistiveTechnology">

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                app:cardBackgroundColor="@color/md_blue_600"
                                app:cardCornerRadius="6dp"
                                app:cardElevation="0dp"
                                app:contentPadding="12dp"
                                android:layout_gravity="center"
                                android:layout_marginEnd="16dp">

                                <androidx.appcompat.widget.AppCompatImageView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    app:srcCompat="@drawable/ic_plus"
                                    app:tint="@color/md_white_1000"
                                    app:cornerRadius="8dp"
                                    />

                            </com.google.android.material.card.MaterialCardView>

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical">

                                <androidx.appcompat.widget.AppCompatTextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="Add New Assistive Technology"
                                    android:textSize="18sp"
                                    android:textAppearance="@style/AT4SEND.AppTheme.TextAppearance.Subtitle"
                                    android:textFontWeight="500"/>

                                <!--
                                <androidx.appcompat.widget.AppCompatTextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="Begin a new ability-based assessment."
                                    />
                                -->

                            </LinearLayout>

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                app:srcCompat="@drawable/ic_chevron_right"
                                app:tint="@color/md_grey_500"/>
                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginStart="16dp"
                            android:background="@color/md_grey_200"/>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:paddingStart="16dp"
                            android:paddingEnd="16dp"
                            android:paddingTop="12dp"
                            android:paddingBottom="12dp"
                            android:clickable="true"
                            android:focusable="true"
                            android:foreground="?attr/selectableItemBackground"
                            android:onClick="editAssistiveTechnology">

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                app:cardBackgroundColor="@color/md_amber_500"
                                app:cardCornerRadius="6dp"
                                app:cardElevation="0dp"
                                app:contentPadding="12dp"
                                android:layout_gravity="center"
                                android:layout_marginEnd="16dp">

                                <androidx.appcompat.widget.AppCompatImageView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    app:srcCompat="@drawable/ic_lead_pencil"
                                    app:tint="@color/md_white_1000"
                                    app:cornerRadius="8dp"
                                    />

                            </com.google.android.material.card.MaterialCardView>

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical">

                                <androidx.appcompat.widget.AppCompatTextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="Edit Existing Assistive Technologies"
                                    android:textSize="18sp"
                                    android:textAppearance="@style/AT4SEND.AppTheme.TextAppearance.Subtitle"
                                    android:textFontWeight="500"/>

                                <!--
                                <androidx.appcompat.widget.AppCompatTextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="Begin a new condition-based assessment."
                                    />
                                -->

                            </LinearLayout>

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                app:srcCompat="@drawable/ic_chevron_right"
                                app:tint="@color/md_grey_500"/>
                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_marginStart="16dp"
                            android:background="@color/md_grey_200"/>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:paddingStart="16dp"
                            android:paddingEnd="16dp"
                            android:paddingTop="12dp"
                            android:paddingBottom="12dp"
                            android:clickable="true"
                            android:focusable="true"
                            android:foreground="?attr/selectableItemBackground"
                            android:onClick="deleteAssistiveTechnology">

                            <com.google.android.material.card.MaterialCardView
                                android:layout_width="48dp"
                                android:layout_height="48dp"
                                app:cardBackgroundColor="@color/md_red_500"
                                app:cardCornerRadius="6dp"
                                app:cardElevation="0dp"
                                app:contentPadding="12dp"
                                android:layout_gravity="center"
                                android:layout_marginEnd="16dp">

                                <androidx.appcompat.widget.AppCompatImageView
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    app:srcCompat="@drawable/ic_delete"
                                    app:tint="@color/md_white_1000"
                                    />

                            </com.google.android.material.card.MaterialCardView>

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical">

                                <androidx.appcompat.widget.AppCompatTextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="Delete Existing Assistive Technologies"
                                    android:textSize="18sp"
                                    android:textAppearance="@style/AT4SEND.AppTheme.TextAppearance.Subtitle"
                                    android:textFontWeight="500"/>

                            </LinearLayout>

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="24dp"
                                android:layout_height="24dp"
                                app:srcCompat="@drawable/ic_chevron_right"
                                app:tint="@color/md_grey_500"/>
                        </LinearLayout>

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/bottomView"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardBackgroundColor="@color/md_white_1000"
            app:cardCornerRadius="0dp"
            app:strokeWidth="1dp"
            app:strokeColor="@color/md_grey_200"
            app:cardElevation="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center_vertical"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingTop="12dp"
                android:paddingBottom="12dp">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/returnHome"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:letterSpacing="0"
                    style="@style/Widget.MaterialComponents.Button.UnelevatedButton"
                    android:text="RETURN TO HOME SCREEN"
                    android:onClick="returnHome"
                    app:backgroundTint="@color/md_orange_500"/>

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>