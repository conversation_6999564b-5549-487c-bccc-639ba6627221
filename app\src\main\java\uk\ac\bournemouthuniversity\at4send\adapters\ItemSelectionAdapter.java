package uk.ac.bournemouthuniversity.at4send.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckedTextView;
import android.widget.Filter;
import android.widget.Filterable;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.extensions.utils.ArrayListAnySize;
import uk.ac.bournemouthuniversity.at4send.extensions.utils.Triple;
import uk.ac.bournemouthuniversity.at4send.utils.ContrastUtils;
import uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils;

public class ItemSelectionAdapter extends RecyclerView.Adapter<ItemSelectionAdapter.ItemViewHolder> implements Filterable {
    private static final String TAG = "ItemSelectionAdapter";

    private List<Triple<Integer, String, Boolean>> itemList;
    private List<Triple<Integer, String, Boolean>> filteredItemList;

    private List<Integer> selectedIds = new ArrayListAnySize<>();
    private List<String> selectedNames = new ArrayListAnySize<>();

    private OnItemClickedListener onItemClickedListener;

    private boolean multipleSelection;

    public class ItemViewHolder extends RecyclerView.ViewHolder {
        public TextView itemName;
        public LinearLayout itemView;

        public ItemViewHolder(View view) {
            super(view);
            itemName = view.findViewById(R.id.itemText);
            itemView = view.findViewById(R.id.itemView);
        }
    }

    public ItemSelectionAdapter(List<Triple<Integer, String, Boolean>> itemList, OnItemClickedListener onItemClickedListener, boolean multipleSelection) {
        this.itemList = itemList;
        this.filteredItemList = itemList;
        this.onItemClickedListener = onItemClickedListener;
        this.multipleSelection = multipleSelection;
    }

    @NonNull
    @Override
    public ItemViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View itemView;

        if (multipleSelection) {
            itemView = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_multi_selection, parent, false);
        } else {
            itemView = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_selection, parent, false);
        }

        return new ItemViewHolder(itemView);
    }

    @Override
    public void onBindViewHolder(@NonNull final ItemViewHolder holder, final int position) {
        final Triple<Integer, String, Boolean> item = filteredItemList.get(position);

        //FIX i/172: Reference to original list to store/preserve check state
        final Triple<Integer, String, Boolean> rootItem = itemList.get(itemList.indexOf(item));

        holder.itemName.setText(item.getSecond());

        if(multipleSelection){
            CheckedTextView checkedTextViewRef = ((CheckedTextView) holder.itemName);
            checkedTextViewRef.setChecked(item.getThird());

            holder.itemView.setOnClickListener(view -> {
                boolean newState = !checkedTextViewRef.isChecked();

                checkedTextViewRef.setChecked(newState);
                item.setThird(newState);

                //FIX i/172: Preserve checked state within root list (for filtering/search)
                rootItem.setThird(newState);

                if(checkedTextViewRef.isChecked()){
                    selectedIds.add(item.getFirst());
                    selectedNames.add(item.getSecond());
                }else{
                    selectedIds.remove(item.getFirst());
                    selectedNames.remove(item.getSecond());
                }

                onItemClickedListener.onMultiItemClicked(selectedIds, selectedNames);
            });
        } else {
            holder.itemView.setOnClickListener(view -> onItemClickedListener.onItemClicked(item));
        }

        // Apply high contrast if enabled
        if (ContrastUtils.isHighContrastEnabled(holder.itemView.getContext())) {
            // Apply text color to the item name
            holder.itemName.setTextColor(TextSizeUtils.getTextColor(holder.itemView.getContext()).getDefaultColor());
            
            // Apply background color to the item view
            holder.itemView.setBackgroundTintList(TextSizeUtils.getBackgroundColor(holder.itemView.getContext()));
            
            // For CheckedTextView in multiple selection mode, apply contrast to the checkbox
            if (multipleSelection && holder.itemName instanceof CheckedTextView) {
                CheckedTextView checkedTextView = (CheckedTextView) holder.itemName;
                // This will tint the checkbox to match the text color
                int textColor = TextSizeUtils.getTextColor(holder.itemView.getContext()).getDefaultColor();
                checkedTextView.setCheckMarkTintList(android.content.res.ColorStateList.valueOf(textColor));
            }
        }
    }

    @Override
    public int getItemCount() {
        return filteredItemList.size();
    }

    @Override
    public Filter getFilter() {
        return new Filter() {
            @Override
            protected FilterResults performFiltering(CharSequence charSequence) {
                String charString = charSequence.toString();
                if (charString.isEmpty()) {
                    filteredItemList = itemList;
                } else {
                    List<Triple<Integer, String, Boolean>> filteredList = new ArrayList<>();
                    for (Triple<Integer, String, Boolean> item : itemList) {
                        if (item.getSecond().toLowerCase().contains(charString.toLowerCase())) {
                            filteredList.add(item);
                        }
                    }

                    filteredItemList = filteredList;
                }

                FilterResults filterResults = new FilterResults();
                filterResults.values = filteredItemList;
                return filterResults;
            }

            @SuppressWarnings("unchecked")
            @Override
            protected void publishResults(CharSequence charSequence, FilterResults filterResults) {
                filteredItemList = ((ArrayList<Triple<Integer, String, Boolean>>) filterResults.values);

                // refresh the list with filtered data
                notifyDataSetChanged();
            }
        };
    }


    public abstract static class OnItemClickedListener {
        public void onItemClicked(Triple<Integer, String, Boolean> item){}
        public void onMultiItemClicked(List<Integer> ids, List<String> names){}
    }
}


