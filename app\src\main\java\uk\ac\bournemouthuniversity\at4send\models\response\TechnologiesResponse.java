package uk.ac.bournemouthuniversity.at4send.models.response;

import com.google.gson.annotations.SerializedName;

import java.util.List;

import uk.ac.bournemouthuniversity.at4send.models.AssistiveTechnology;

public class TechnologiesResponse {

    @SerializedName("technologies")
    private List<AssistiveTechnology> assistiveTechnologies;

    @SerializedName("count")
    private Integer count;

    public TechnologiesResponse(List<AssistiveTechnology> assistiveTechnologies, Integer count) {
        this.assistiveTechnologies = assistiveTechnologies;
        this.count = count;
    }

    public List<AssistiveTechnology> getAssistiveTechnologies() {
        return assistiveTechnologies;
    }

    public void setAssistiveTechnologies(List<AssistiveTechnology> assistiveTechnologies) {
        this.assistiveTechnologies = assistiveTechnologies;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }
}

