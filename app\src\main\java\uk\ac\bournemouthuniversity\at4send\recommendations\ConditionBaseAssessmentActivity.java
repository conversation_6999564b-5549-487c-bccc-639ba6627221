package uk.ac.bournemouthuniversity.at4send.recommendations;

import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBar;
import uk.ac.bournemouthuniversity.at4send.BaseActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.activity.OnBackPressedCallback;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import android.app.Activity;

import com.afollestad.materialdialogs.MaterialDialog;
import com.gabrielepmattia.materialfields.fields.FieldGeneric;
import com.google.android.material.snackbar.Snackbar;

import java.util.ArrayList;
import java.util.List;

import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.common.ItemSearchAndSelectActivity;
import uk.ac.bournemouthuniversity.at4send.extensions.helpers.SnackbarHelper;
import uk.ac.bournemouthuniversity.at4send.extensions.utils.Network;
import uk.ac.bournemouthuniversity.at4send.technologies.AssistiveTechnologyListActivity;
import uk.ac.bournemouthuniversity.at4send.utils.ReadAloudUtils;
import uk.ac.bournemouthuniversity.at4send.utils.TextToSpeechManager;

import android.transition.Slide;
import android.widget.TextView;

public class ConditionBaseAssessmentActivity extends BaseActivity {
    public static final String TAG = "ConditionBased";

    private static final int REQUEST_CONDITION = 10001;

    private CoordinatorLayout mMainView;
    private FieldGeneric mConditionSelection;

    private List<Integer> selectedConditionIDs = new ArrayList<>();

    boolean searchAvailable = false;

    private final ActivityResultLauncher<Intent> conditionLauncher = registerForActivityResult(
        new ActivityResultContracts.StartActivityForResult(),
        result -> {
            if (result.getResultCode() == Activity.RESULT_OK && result.getData() != null) {
                Bundle extras = result.getData().getExtras();
                if (extras != null) {
                    ArrayList<String> values = extras.getStringArrayList(ItemSearchAndSelectActivity.EXTRA_ITEM_CHOSEN_NAMES);
                    if (values != null) {
                        StringBuilder stringBuilder = new StringBuilder();
                        if (values.size() != 1) {
                            for (String value : values.subList(0, values.size() - 1)) {
                                stringBuilder.append(value);
                                stringBuilder.append(", ");
                            }
                        }
                        stringBuilder.append(values.get(values.size() - 1));
                        mConditionSelection.setValue(stringBuilder.toString());
                    }

                    selectedConditionIDs = extras.getIntegerArrayList(ItemSearchAndSelectActivity.EXTRA_ITEM_CHOSEN_IDS);

                    if (selectedConditionIDs != null && !selectedConditionIDs.isEmpty()) {
                        searchAvailable = true;
                    }
                }
            }
        }
    );

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_condition_based_assessment);
        
        // Set up toolbar
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        
        // Add read-aloud button to instructions text
        TextView instructionsText = findViewById(R.id.assessment_instructions);
        if (instructionsText != null) {
            ReadAloudUtils.addReadAloudButton(this, instructionsText);
        }
        
        mMainView = findViewById(R.id.mainView);

        mConditionSelection = findViewById(R.id.conditionInput);
        mConditionSelection.setOnClickListener(v -> {
            Intent i = new Intent(this, ItemSearchAndSelectActivity.class);
            i.putExtra(ItemSearchAndSelectActivity.EXTRA_LIST_TYPE, ItemSearchAndSelectActivity.LIST_TYPE_CONDITION);
            conditionLauncher.launch(i);
        });

        getOnBackPressedDispatcher().addCallback(this, new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                finish();
            }
        });
    }

    @Override
    protected void onResume() {
        super.onResume();

        if(!Network.isNetworkAvailable(this)){
            new MaterialDialog(ConditionBaseAssessmentActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                    .title(null, "Network Access Required")
                    .message(null, "An active network connection is required for this function. Please connect to the internet and try again.", null)
                    .cancelable(false)
                    .positiveButton(null, "Dismiss", materialDialog -> {
                        materialDialog.dismiss();
                        finish();
                        return null;
                    }).show();
        }
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        int itemId = item.getItemId();

        if(itemId == android.R.id.home){
            super.onBackPressed();
            return true;
        }

        return super.onOptionsItemSelected(item);
    }

    @Override
    public void finish() {
        super.finish();
        getWindow().setEnterTransition(new Slide());
        getWindow().setExitTransition(new Slide());
    }

    public void conditionSubmit(View view){
        if(searchAvailable){
            Intent i = new Intent(this, AssistiveTechnologyListActivity.class);
            i.putIntegerArrayListExtra(AssistiveTechnologyListActivity.EXTRA_CONDITION_IDS, (ArrayList<Integer>) selectedConditionIDs);
            i.putExtra(AssistiveTechnologyListActivity.EXTRA_CONDITION_NAME, mConditionSelection.getValue());

            startActivity(i);
        }else{
            Snackbar sB = Snackbar.make(mMainView, "Please select a condition to search for appropriate Assistive Technologies.", Snackbar.LENGTH_LONG);
            SnackbarHelper.configSnackbar(this, sB);
            sB.show();
        }
    }

    public void returnHome(View view){
        onBackPressed();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Stop TTS when activity is destroyed
        TextToSpeechManager.getInstance(this).stop();
    }
}
