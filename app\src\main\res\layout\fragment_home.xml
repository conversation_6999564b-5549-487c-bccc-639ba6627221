<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior"
    tools:context=".fragments.HomeFragment">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/md_white_1000"
                app:cardCornerRadius="0dp"
                app:strokeWidth="1dp"
                app:strokeColor="@color/md_grey_200"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:paddingTop="8dp"
                        android:paddingBottom="8dp">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Technology Assessments"
                            android:textAppearance="@style/AT4SEND.AppTheme.TextAppearance.Title"
                            android:textStyle="bold"
                        />

                        <com.google.android.material.button.MaterialButton
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                            app:backgroundTint="@color/md_blue_50"
                            app:strokeColor="@color/md_blue_500"
                            android:text="View All"
                            android:textColor="@color/md_blue_500"
                            app:rippleColor="@color/md_blue_600"
                            android:letterSpacing="0" />
                    </LinearLayout>


                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/md_grey_200" />

                    <!-- Remove this entire LinearLayout block -->
                    <!--
                    <LinearLayout
                        android:id="@+id/newAssessment"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:paddingTop="12dp"
                        android:paddingBottom="12dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?attr/selectableItemBackground">
                        ...
                    </LinearLayout>
                    -->

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="16dp"
                        android:background="@color/md_grey_200" />

                    <LinearLayout
                        android:id="@+id/abilityBasedAssessment"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:paddingTop="12dp"
                        android:paddingBottom="12dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?attr/selectableItemBackground">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            app:cardBackgroundColor="@color/md_blue_600"
                            app:cardCornerRadius="6dp"
                            app:cardElevation="0dp"
                            app:contentPadding="12dp"
                            android:layout_marginEnd="16dp">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                app:srcCompat="@drawable/ic_plus"
                                app:tint="@color/md_white_1000" />
                        </com.google.android.material.card.MaterialCardView>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="New Ability-Based Assessment"
                                android:textSize="17sp"
                                android:textAppearance="@style/AT4SEND.AppTheme.TextAppearance.Subtitle"
                                android:textFontWeight="500" />
                        </LinearLayout>

                        <androidx.appcompat.widget.AppCompatImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            app:srcCompat="@drawable/ic_chevron_right"
                            app:tint="@color/md_grey_500" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="16dp"
                        android:background="@color/md_grey_200" />

                    <LinearLayout
                        android:id="@+id/conditionBasedAssessment"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:paddingTop="12dp"
                        android:paddingBottom="12dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?attr/selectableItemBackground">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            app:cardBackgroundColor="@color/md_amber_500"
                            app:cardCornerRadius="6dp"
                            app:cardElevation="0dp"
                            app:contentPadding="12dp"
                            android:layout_gravity="center"
                            android:layout_marginEnd="16dp">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                app:srcCompat="@drawable/ic_plus"
                                app:tint="@color/md_white_1000"
                                app:cornerRadius="8dp"
                            />
                        </com.google.android.material.card.MaterialCardView>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="New Condition-Based Assessment"
                                android:textSize="17sp"
                                android:textAppearance="@style/AT4SEND.AppTheme.TextAppearance.Subtitle"
                                android:textFontWeight="500" />
                        </LinearLayout>

                        <androidx.appcompat.widget.AppCompatImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            app:srcCompat="@drawable/ic_chevron_right"
                            app:tint="@color/md_grey_500" />

                    </LinearLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="16dp"
                        android:background="@color/md_grey_200" />
                    <!--
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginStart="16dp"
                        android:background="@color/md_grey_200"/>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:paddingTop="12dp"
                        android:paddingBottom="12dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?attr/selectableItemBackground">

                        <uk.co.verscreative.at4send.extensions.views.AT4SENDAvatarView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            app:avatarCornerRadius="6dp"
                            android:layout_marginEnd="16dp"
                            app:avatarInitials="DM" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">
                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Danielle Marie"
                                android:textSize="17sp"
                                android:textAppearance="@style/AT4SEND.AppTheme.TextAppearance.Subtitle"
                                android:textFontWeight="500"/>

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="View Danielle's assessment."
                                />
                        </LinearLayout>

                        <androidx.appcompat.widget.AppCompatImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            app:srcCompat="@drawable/ic_chevron_right"
                            app:tint="@color/md_grey_500"/>
                    </LinearLayout>
                    -->
                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@color/md_white_1000"
                app:cardCornerRadius="0dp"
                app:strokeWidth="1dp"
                app:strokeColor="@color/md_grey_200"
                app:cardElevation="0dp"
                android:layout_marginTop="20dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="center_vertical"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:paddingTop="12dp"
                        android:paddingBottom="8dp">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Administrator Options"
                            android:textAppearance="@style/AT4SEND.AppTheme.TextAppearance.Title"
                            android:textStyle="bold"
                        />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/administrationMenu"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:paddingStart="16dp"
                        android:paddingEnd="16dp"
                        android:paddingTop="12dp"
                        android:paddingBottom="12dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?attr/selectableItemBackground">

                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            app:cardBackgroundColor="@color/md_red_500"
                            app:cardCornerRadius="6dp"
                            app:cardElevation="0dp"
                            app:contentPadding="12dp"
                            android:layout_gravity="center"
                            android:layout_marginEnd="16dp">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                app:srcCompat="@drawable/ic_admin"
                                app:tint="@color/md_white_1000"
                            />

                        </com.google.android.material.card.MaterialCardView>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Administration"
                                android:textSize="17sp"
                                android:textAppearance="@style/AT4SEND.AppTheme.TextAppearance.Subtitle"
                                android:textFontWeight="500" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Perform administrative functions."
                            />

                        </LinearLayout>

                        <androidx.appcompat.widget.AppCompatImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            app:srcCompat="@drawable/ic_chevron_right"
                            app:tint="@color/md_grey_500" />
                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>


        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>
