package uk.ac.bournemouthuniversity.at4send.training;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import uk.ac.bournemouthuniversity.at4send.BaseActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;
import androidx.viewpager2.widget.ViewPager2;

import android.content.res.ColorStateList;
import android.graphics.Color;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.google.android.material.tabs.TabLayoutMediator;

import org.jetbrains.annotations.NotNull;

import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.training.fragments.AssistiveTechnologiesHardwareFragment;
import uk.ac.bournemouthuniversity.at4send.training.fragments.AssistiveTechnologiesSoftwareFragment;
import uk.ac.bournemouthuniversity.at4send.utils.ContrastUtils;
import uk.ac.bournemouthuniversity.at4send.utils.ReadAloudUtils;
import uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils;
import uk.ac.bournemouthuniversity.at4send.utils.TextToSpeechManager;

public class TrainingActivity extends BaseActivity {

    private final String[] tabTitles = new String[]{
            "Hardware",
            "Software"
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_training);

        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        ActionBar ab = getSupportActionBar();

        if(ab != null){
            ab.setDisplayHomeAsUpEnabled(true);
        }

        findViewById(R.id.returnHome).setOnClickListener(v -> super.onBackPressed());

        ViewPager2 viewPager = findViewById(R.id.view_pager);
        viewPager.setAdapter(new ViewPagerFragmentAdapter(this));

        // Get reference to TabLayout
        com.google.android.material.tabs.TabLayout tabLayout = findViewById(R.id.tab_layout);
        
        // attaching tab mediator
        new TabLayoutMediator(tabLayout, viewPager, (tab, position) -> {
            tab.setText(tabTitles[position]);
        }).attach();
        
        // Apply high contrast if enabled
        if (ContrastUtils.isHighContrastEnabled(this)) {
            applyHighContrastToTabs(tabLayout);
        }

        // Add read-aloud button to instructions text
        TextView instructionsText = findViewById(R.id.training_instructions);
        if (instructionsText != null) {
            ReadAloudUtils.addReadAloudButton(this, instructionsText);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        // Reapply contrast when returning to activity
        if (ContrastUtils.isHighContrastEnabled(this)) {
            com.google.android.material.tabs.TabLayout tabLayout = findViewById(R.id.tab_layout);
            if (tabLayout != null) {
                applyHighContrastToTabs(tabLayout);
            }
        }
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        int itemId = item.getItemId();

        if(itemId == android.R.id.home){
            super.onBackPressed();
            findViewById(R.id.returnHome).setOnClickListener(v -> finish());
            return true;
        }

        return super.onOptionsItemSelected(item);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Stop TTS when activity is destroyed
        TextToSpeechManager.getInstance(this).stop();
    }

    private class ViewPagerFragmentAdapter extends FragmentStateAdapter {

        public ViewPagerFragmentAdapter(@NonNull FragmentActivity fragmentActivity) {
            super(fragmentActivity);
        }

        @NotNull
        @Override
        public Fragment createFragment(int position) {
            switch (position) {
                case 0:
                    return AssistiveTechnologiesHardwareFragment.newInstance();
                case 1:
                    return AssistiveTechnologiesSoftwareFragment.newInstance();
            }
            return AssistiveTechnologiesHardwareFragment.newInstance();
        }

        @Override
        public int getItemCount() {
            return tabTitles.length;
        }
    }

    /**
     * Applies high contrast settings to the TabLayout and its tabs
     */
    private void applyHighContrastToTabs(com.google.android.material.tabs.TabLayout tabLayout) {
        // Apply background color to TabLayout
        tabLayout.setBackgroundTintList(TextSizeUtils.getBackgroundColor(this));
        
        // Get text color from high contrast settings
        int textColor = TextSizeUtils.getTextColor(this).getDefaultColor();
        
        // Override tab text colors for both selected and unselected states
        tabLayout.setTabTextColors(textColor, textColor);
        
        // Set indicator color
        tabLayout.setSelectedTabIndicatorColor(textColor);
        
        // Set tab ripple color to transparent to prevent color changes on click
        tabLayout.setTabRippleColor(ColorStateList.valueOf(Color.TRANSPARENT));
        
        // Apply text color to all tabs to ensure it overrides any default behavior
        try {
            // Use reflection to access the internal TextView
            for (int i = 0; i < tabLayout.getTabCount(); i++) {
                com.google.android.material.tabs.TabLayout.Tab tab = tabLayout.getTabAt(i);
                if (tab != null) {
                    // Get the tab view directly from the TabLayout
                    ViewGroup tabView = (ViewGroup) tabLayout.getChildAt(0);
                    if (tabView != null && i < tabView.getChildCount()) {
                        ViewGroup tabViewChild = (ViewGroup) tabView.getChildAt(i);
                        // Find TextView within the tab view
                        for (int j = 0; j < tabViewChild.getChildCount(); j++) {
                            View view = tabViewChild.getChildAt(j);
                            if (view instanceof TextView) {
                                TextView textView = (TextView) view;
                                textView.setTextColor(textColor);
                                
                                // Create a ColorStateList that uses the same color for all states
                                ColorStateList csl = new ColorStateList(
                                    new int[][] {
                                        new int[] { android.R.attr.state_selected },
                                        new int[] { -android.R.attr.state_selected },
                                        new int[] {}  // Default state
                                    },
                                    new int[] {
                                        textColor,
                                        textColor,
                                        textColor
                                    }
                                );
                                textView.setTextColor(csl);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            // Fallback method if the above doesn't work
            // Apply contrast to the entire TabLayout which should affect text colors
            ContrastUtils.applyContrastToViewHierarchy(tabLayout);
        }
        
        // Apply contrast to the rest of the view hierarchy
        ContrastUtils.applyContrastToViewHierarchy(findViewById(android.R.id.content));
    }
}
