package uk.ac.bournemouthuniversity.at4send.extensions.ui.transforms;

import android.view.View;

import androidx.viewpager2.widget.ViewPager2;

public class TossTransformation implements ViewPager2.PageTransformer {
    @Override
    public void transformPage(View page, float position) {
        page.setTranslationX(-position * page.getWidth());
        page.setCameraDistance(20000);

        if (position < 0.5 && position > -0.5) {
            page.setVisibility(View.VISIBLE);
        } else {
            page.setVisibility(View.INVISIBLE);
        }

        if (position < -1) {
            page.setAlpha(0);
        } else {
            float max = Math.max(0.4f, (1 - Math.abs(position)));
            if (position <= 0) {
                page.setAlpha(1);
                page.setScaleX(max);
                page.setScaleY(max);
                page.setRotationX(1080 * (1 - Math.abs(position) + 1));
                page.setTranslationY(-1000*Math.abs(position));
            } else if (position <= 1) {
                page.setAlpha(1);
                page.setScaleX(max);
                page.setScaleY(max);
                page.setRotationX(-1080 * (1 - Math.abs(position) + 1));
                page.setTranslationY(-1000*Math.abs(position));

            }else {
                page.setAlpha(0);
            }
        }
    }
}
