package uk.ac.bournemouthuniversity.at4send.security;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import uk.ac.bournemouthuniversity.at4send.BaseActivity;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.appcompat.widget.Toolbar;
import androidx.core.content.res.ResourcesCompat;

import android.content.Intent;
import android.content.res.ColorStateList;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.inputmethod.InputMethodManager;

import com.afollestad.materialdialogs.MaterialDialog;

import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.extensions.helpers.PreferenceManager;

/**
 * Create Pin Code Activity
 *
 * @version 1.1.0
 * @owner  David Passmore
 * <AUTHOR> Creative UK
 * @copyright 2019
 */
public class CreatePinCodeActivity extends BaseActivity {

    boolean confirm = false;
    String pinCodeForConfirm;

    AppCompatEditText pinCodeAutoFocus;
    AppCompatImageView[] pinDigits;

    PreferenceManager pM;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_create_pin_code);

        pM = new PreferenceManager(this);

        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        ActionBar ab = getSupportActionBar();
        if(ab != null){
            ab.setDisplayHomeAsUpEnabled(true);
        }

        pinCodeAutoFocus = findViewById(R.id.pinCodeAutoFocus);
        pinDigits = new AppCompatImageView[]{
                findViewById(R.id.pinDigit1),
                findViewById(R.id.pinDigit2),
                findViewById(R.id.pinDigit3),
                findViewById(R.id.pinDigit4),
                findViewById(R.id.pinDigit5),
                findViewById(R.id.pinDigit6)
        };

        AppCompatTextView instructions = findViewById(R.id.createPinCodeInstruction);
        AppCompatTextView instructionsV = findViewById(R.id.createPinCodeInstructionVerbatim);

        pinCodeAutoFocus.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) { }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                for(int i = 0; i < 6; i++){
                    if(s.length() - 1 >= i) {
                        pinDigits[i].setImageDrawable(ResourcesCompat.getDrawable(getResources(), R.drawable.ic_circle, null));
                        pinDigits[i].setSupportImageTintList(ColorStateList.valueOf(ResourcesCompat.getColor(getResources(), R.color.md_blue_600, null)));
                    }else{
                        pinDigits[i].setImageDrawable(ResourcesCompat.getDrawable(getResources(), R.drawable.ic_circle_outline, null));
                        pinDigits[i].setSupportImageTintList(ColorStateList.valueOf(ResourcesCompat.getColor(getResources(), R.color.md_grey_500, null)));
                    }
                }

                if(!confirm){
                    if(s.length() == 6) {
                        confirm = true;
                        pinCodeForConfirm = s.toString();

                        pinCodeAutoFocus.setText("");
                        resetPinDigits();

                        instructions.setText("Please re-enter your Pin Code");
                        instructionsV.setText("Please re-enter your 6 digit pin code for confirmation.");
                    }
                }else{
                    if(s.length() == 6) {
                        if(pinCodeForConfirm.equals(s.toString())) {
                            InputMethodManager imm = (InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);

                            if (imm != null) {
                                imm.hideSoftInputFromWindow(pinCodeAutoFocus.getWindowToken(), 0);
                            }

                            pM.setSharedStringPreference("at4ed_securePinCode", pinCodeForConfirm, true);
                            finish();
                        }else{
                            new MaterialDialog(CreatePinCodeActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                                    .title(null, "Error")
                                    .message(null, "The pin codes you entered did not match. Please try again.", null)
                                    .positiveButton(null, "OK", materialDialog -> {
                                        materialDialog.dismiss();
                                        startActivity(new Intent(CreatePinCodeActivity.this, CreatePinCodeActivity.class));
                                        finish();
                                        return null;
                                    })
                                    .cancelable(false)
                                    .show();
                        }
                    }
                }
            }

            @Override
            public void afterTextChanged(Editable s) { }
        });
    }

    private void resetPinDigits(){
        for(int i = 0; i < 6; i++){
            pinDigits[i].setImageDrawable(ResourcesCompat.getDrawable(getResources(), R.drawable.ic_circle_outline, null));
            pinDigits[i].setSupportImageTintList(ColorStateList.valueOf(ResourcesCompat.getColor(getResources(), R.color.md_grey_500, null)));
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        MenuInflater menuInflater = getMenuInflater();
        menuInflater.inflate(R.menu.menu_create_pin_activity, menu);
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        int itemId = item.getItemId();

        if(itemId == android.R.id.home){
            finish();
        }else if(itemId == R.id.create_pin_menu_show_keyboard){
            InputMethodManager imm = (InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);

            if (imm != null) {
                View view = getCurrentFocus();
                if (view != null) {
                    imm.showSoftInput(view, InputMethodManager.SHOW_IMPLICIT);
                }
            }
        }
        return super.onOptionsItemSelected(item);
    }
}


