package uk.ac.bournemouthuniversity.at4send.adapters.training;

import android.content.Context;
import android.content.res.ColorStateList;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.models.training.QuizResult;

public class QuizResultsAdapter extends RecyclerView.Adapter<QuizResultsAdapter.ViewHolder> {
    private static final String TAG = "AssistiveTechnologyTrainingAdapter";

    private List<QuizResult> quizResultList;
    private Context context;

    public class ViewHolder extends RecyclerView.ViewHolder {
        AppCompatTextView quizName, quizInformation, quizPassFail;
        LinearLayout itemView;
        public boolean selected = false;

        public ViewHolder(View view) {
            super(view);
            itemView = view.findViewById(R.id.itemView);
            quizName = view.findViewById(R.id.quizName);
            quizInformation = view.findViewById(R.id.quizInformation);
            quizPassFail = view.findViewById(R.id.quizPassFail);
        }
    }

    public QuizResultsAdapter(Context context, List<QuizResult> quizResultList) {
        this.quizResultList = quizResultList;
        this.context = context;
    }

    @NonNull
    @Override
    public QuizResultsAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_quiz_result, parent, false);

        return new QuizResultsAdapter.ViewHolder(itemView);
    }

    @Override
    public void onBindViewHolder(@NonNull final QuizResultsAdapter.ViewHolder holder, final int position) {
        final QuizResult item = quizResultList.get(position);

        holder.quizName.setText(item.getQuizName());
        holder.quizInformation.setText("Taken: " + item.getQuizTaken() + "  |  Score: " + item.getQuizScore());

        if(item.getQuizPassed() == 1) {
            holder.quizPassFail.setText("Passed");
            holder.quizPassFail.setTextColor(ColorStateList.valueOf(ContextCompat.getColor(context, R.color.md_green_600)));
        }else{
            holder.quizPassFail.setText("Failed");
            holder.quizPassFail.setTextColor(ColorStateList.valueOf(ContextCompat.getColor(context, R.color.md_red_600)));
        }

        // Apply high contrast if enabled
        if (uk.ac.bournemouthuniversity.at4send.utils.ContrastUtils.isHighContrastEnabled(context)) {
            // Apply text color to all text views
            holder.quizName.setTextColor(uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils.getTextColor(context).getDefaultColor());
            holder.quizInformation.setTextColor(uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils.getTextColor(context).getDefaultColor());
            holder.quizPassFail.setTextColor(uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils.getTextColor(context).getDefaultColor());
            
            // Apply background color to the item view
            holder.itemView.setBackgroundTintList(uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils.getBackgroundColor(context));
        }
    }

    @Override
    public int getItemCount() {
        return quizResultList.size();
    }

}

