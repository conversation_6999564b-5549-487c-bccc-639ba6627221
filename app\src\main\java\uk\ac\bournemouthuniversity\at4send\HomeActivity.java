package uk.ac.bournemouthuniversity.at4send;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.Toolbar;
import androidx.viewpager2.widget.ViewPager2;

import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;

import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;

import uk.ac.bournemouthuniversity.at4send.BaseActivity;
import uk.ac.bournemouthuniversity.at4send.adapters.SectionsPagerAdapter;
import uk.ac.bournemouthuniversity.at4send.fragments.HomeFragment;
import uk.ac.bournemouthuniversity.at4send.fragments.BrowseFragment;
import uk.ac.bournemouthuniversity.at4send.fragments.TrainingFragment;
import uk.ac.bournemouthuniversity.at4send.fragments.QuizFragment;

/**
 * Home Activity
 *
 * @since 2.0.0
 * @owner <PERSON>
 * <AUTHOR> Creative UK
 * @copyright 2021
 */

public class HomeActivity extends BaseActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_home);

        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        SectionsPagerAdapter sectionsPagerAdapter = new SectionsPagerAdapter(this);
        ViewPager2 viewPager = findViewById(R.id.view_pager);
        viewPager.setAdapter(sectionsPagerAdapter);

        // Add your fragments
        sectionsPagerAdapter.addFragment(new HomeFragment(), getString(R.string.tab_text_1));
        sectionsPagerAdapter.addFragment(new TrainingFragment(), getString(R.string.tab_text_2));
        sectionsPagerAdapter.addFragment(new QuizFragment(), getString(R.string.tab_text_3));
        sectionsPagerAdapter.addFragment(new BrowseFragment(), getString(R.string.tab_text_4));

        TabLayout tabs = findViewById(R.id.tabs);
        new TabLayoutMediator(tabs, viewPager,
            (tab, position) -> tab.setText(sectionsPagerAdapter.getPageTitle(position))
        ).attach();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        MenuInflater inflater = getMenuInflater();
        inflater.inflate(R.menu.menu_home_activity, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        int itemId = item.getItemId();

        if(itemId == R.id.home_menu_settings){
            startActivity(new Intent(this, SettingsActivity.class));
        }

        return super.onOptionsItemSelected(item);
    }


}




