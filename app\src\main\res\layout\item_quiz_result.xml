<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/itemView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:paddingTop="12dp"
        android:paddingBottom="12dp">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginEnd="16dp">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/quizName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                tools:text="Quiz Name"
                android:textSize="17sp"
                android:textAppearance="@style/AT4SEND.AppTheme.TextAppearance.Subtitle"
                android:textFontWeight="500"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/quizInformation"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                tools:text="Taken: 23/07/2021  |  Score: 95%"
                android:maxLines="2"
                android:ellipsize="end" />

        </LinearLayout>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/quizPassFail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="Pass"
            android:textSize="20sp"
            android:textColor="@color/md_green_600"
            app:fontFamily="@font/asap_medium"/>
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginStart="16dp"
        android:background="@color/md_grey_200"/>

</LinearLayout>