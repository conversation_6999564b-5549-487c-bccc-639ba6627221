package uk.ac.bournemouthuniversity.at4send.extensions.utils;

import androidx.annotation.Nullable;

/**
 * a helper class to provide a triple class, to store 3 values and return them from a method
 *
 * @param <T>
 * @param <U>
 * @param <V>
 */
public class Triple<T, U, V> {
    public T first;
    public U second;
    public V third;

    public Triple(T first, @Nullable U second, @Nullable V third) {
        this.first = first;
        this.second = second;
        this.third = third;
    }

    public T getFirst() {
        return first;
    }

    public void setFirst(T first) {
        this.first = first;
    }

    public U getSecond() {
        return second;
    }

    public void setSecond(@Nullable U second) {
        this.second = second;
    }

    public V getThird() {
        return third;
    }

    public void setThird(@Nullable V third) {
        this.third = third;
    }
}