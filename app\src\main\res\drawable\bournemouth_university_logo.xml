<vector android:height="24dp" android:viewportHeight="484"
    android:viewportWidth="471.5" android:width="24dp"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:aapt="http://schemas.android.com/aapt" xmlns:android="http://schemas.android.com/apk/res/android">
    <path android:fillColor="#FF000000" android:pathData="M84.6,79.4v45h34.2c21.7,0 24,-16.3 24,-24.3c0,-8 -4.1,-20.7 -24.7,-20.7H84.6zM84.6,187.2v54.9h41.8c27.2,0 31.2,-19.9 31.2,-29.6c0,-9.8 -5.2,-25.3 -31,-25.3H84.6zM14.1,13.2c0,0 60.1,-0.1 108.5,-0.1c48.4,0 91.9,24.9 91.9,74.6s-33.2,61.5 -33.2,61.5s47.7,20 47.7,71.2c0,51.1 -41.5,86.4 -105.7,86.4H14.1V13.2z"/>
    <path android:fillColor="#FF000000" android:fillType="evenOdd" android:pathData="M36.5,420h-6v25.7c0,4.2 0.8,7.3 2.4,9.2c1.6,1.9 4.1,2.9 7.4,2.9c3.4,0 5.9,-1 7.5,-2.9c1.6,-1.9 2.4,-5 2.4,-9.2V420H44v-7.5h22.1v7.5h-6v25.5c0,6.8 -1.6,11.9 -4.9,15.2c-3.2,3.3 -8.2,5 -14.9,5c-6.7,0 -11.7,-1.6 -14.9,-5c-3.2,-3.3 -4.9,-8.4 -4.9,-15.2V420h-6.1v-7.5h22.1V420z"/>
    <path android:fillColor="#FF000000" android:fillType="evenOdd" android:pathData="M67.4,464.5v-7.4h4.5v-22.4h-4.5v-7.2h13.7v5.9c1.5,-2.4 3.2,-4.1 5.1,-5.2c1.9,-1.1 4.2,-1.6 6.8,-1.6c3.9,0 6.8,1.1 8.7,3.4c1.9,2.3 2.8,5.9 2.8,10.7v16.4h4.4v7.4H90v-7.4h5.1v-16.4c0,-2 -0.5,-3.6 -1.5,-4.8c-1,-1.2 -2.4,-1.8 -4.1,-1.8c-2.4,0 -4.4,0.9 -5.8,2.7c-1.5,1.8 -2.2,4.1 -2.2,7v13.3h5.1v7.4H67.4z"/>
    <path android:fillColor="#FF000000" android:fillType="evenOdd" android:pathData="M123.7,422.1h-9.9v-10.5h9.9V422.1zM114.3,457.1v-22.4h-4.5v-7.2h14v29.6h4.7v7.4h-18.8v-7.4H114.3z"/>
    <path android:fillColor="#FF000000" android:fillType="evenOdd" android:pathData="M127.4,434.6l0,-7.1l18.5,0l0,7.1l-4.7,0l7,21.4l7.3,-21.4l-4.8,0l0,-7.1l17.3,0l0,7.1l-3.4,0l-11.3,29.9l-11.3,0l-10.9,-29.9z"/>
    <path android:fillColor="#FF000000" android:fillType="evenOdd" android:pathData="M178,442.2h14.4v-0.8c0,-2.2 -0.7,-4 -2,-5.4c-1.3,-1.4 -3.1,-2.1 -5.2,-2.1c-2.1,0 -3.9,0.8 -5.2,2.3C178.7,437.7 178,439.7 178,442.2zM192.6,452.9h8.8c-0.8,4.2 -2.5,7.3 -5,9.4c-2.6,2.1 -6,3.1 -10.5,3.1c-5.6,0 -9.9,-1.7 -12.9,-5.1c-3.1,-3.4 -4.6,-8.2 -4.6,-14.3c0,-6 1.5,-10.8 4.5,-14.3c3,-3.5 7.1,-5.2 12.3,-5.2c5.3,0 9.4,1.6 12.3,4.9c2.9,3.3 4.3,8 4.3,14.1c0,0.4 0,0.8 0,1.3c0,0.5 -0.1,1.2 -0.1,2l-23.7,0c0,2.9 0.6,5.1 1.9,6.7c1.3,1.6 3.1,2.3 5.6,2.3c1.8,0 3.2,-0.4 4.4,-1.2C191.1,455.9 192,454.6 192.6,452.9z"/>
    <path android:fillColor="#FF000000" android:fillType="evenOdd" android:pathData="M208.2,457.1v-22.4h-4.5v-7.2h13.9v6.7c1.3,-2.7 2.8,-4.7 4.5,-6c1.7,-1.3 3.6,-1.9 5.9,-1.9c0.5,0 1.1,0 1.7,0.1c0.6,0.1 1.3,0.2 2.2,0.4v8.8c-0.6,-0.1 -1.2,-0.2 -1.9,-0.3c-0.6,-0.1 -1.3,-0.1 -1.9,-0.1c-3.6,0 -6.2,1.1 -7.9,3.4c-1.7,2.3 -2.5,5.8 -2.5,10.6v7.8h4.7v7.4h-18.8v-7.4H208.2z"/>
    <path android:fillColor="#FF000000" android:fillType="evenOdd" android:pathData="M239.1,464.7H232v-13.4h7.1c0.6,2.2 1.4,3.8 2.6,4.9c1.2,1.1 2.7,1.6 4.6,1.6c1.4,0 2.5,-0.3 3.4,-1c0.9,-0.7 1.3,-1.6 1.3,-2.6c0,-1.8 -2.6,-3.3 -7.8,-4.7c-0.5,-0.1 -0.8,-0.2 -1,-0.3c-4.2,-1.1 -7,-2.5 -8.4,-4.1c-1.4,-1.6 -2.1,-4 -2.1,-7.2c0,-3.5 1.1,-6.2 3.3,-8.3c2.2,-2 5.1,-3.1 8.9,-3.1c2,0 3.7,0.4 5.1,1.2c1.5,0.8 2.7,2 3.7,3.5v-3.7h6.8v11.9h-6.8c-0.3,-1.8 -1,-3.3 -2.2,-4.3c-1.2,-1 -2.7,-1.6 -4.6,-1.6c-1.3,0 -2.4,0.3 -3.2,1c-0.8,0.6 -1.2,1.5 -1.2,2.5c0,1.9 2.8,3.6 8.3,4.9c0.7,0.2 1.2,0.3 1.6,0.4c3.3,0.8 5.7,2 7.1,3.8c1.5,1.8 2.2,4.2 2.2,7.4c0,3.6 -1.1,6.5 -3.3,8.6c-2.2,2.2 -5.1,3.2 -8.7,3.2c-2.3,0 -4.2,-0.4 -5.8,-1.3c-1.6,-0.9 -3,-2.2 -4,-4V464.7z"/>
    <path android:fillColor="#FF000000" android:fillType="evenOdd" android:pathData="M276.2,422.1h-9.9v-10.5h9.9V422.1zM266.7,457.1v-22.4h-4.5v-7.2h14v29.6h4.7v7.4H262v-7.4H266.7z"/>
    <path android:fillColor="#FF000000" android:fillType="evenOdd" android:pathData="M303.3,434.8h-8.8v19.6c0,1.2 0.2,2.1 0.6,2.6c0.4,0.5 1,0.8 1.7,0.8c1,0 1.7,-0.4 2.2,-1.2c0.5,-0.8 0.7,-2 0.7,-3.5v-1h5.8c0,0.6 0.1,1.1 0.1,1.4c0,0.3 0,0.6 0,0.9c0,3.5 -0.9,6.3 -2.7,8.2c-1.8,1.9 -4.3,2.8 -7.6,2.8c-3.6,0 -6.2,-0.8 -7.8,-2.4c-1.7,-1.6 -2.5,-4.1 -2.5,-7.5v-20.8h-5.3v-7.3h5.3v-7.3l9.5,-3.8v11.1h8.8V434.8z"/>
    <path android:fillColor="#FF000000" android:fillType="evenOdd" android:pathData="M304.7,434.6v-7.2h18.5v7.2h-4.7l6.9,19.4l7.4,-19.4H328v-7.2h17.3v7.2h-3.4l-15.6,36.8c-1.2,2.9 -2.6,4.8 -4.1,5.9c-1.5,1 -3.7,1.6 -6.6,1.6c-0.7,0 -1.6,0 -2.4,-0.1c-0.9,-0.1 -1.9,-0.2 -3,-0.4v-7.8c0.9,0.2 1.7,0.3 2.4,0.4c0.7,0.1 1.4,0.1 2,0.1c1.6,0 2.9,-0.5 3.7,-1.5c0.9,-1 1.6,-2.7 2.1,-5l-12,-29.8H304.7z"/>
    <path android:fillColor="#FF000000" android:fillType="evenOdd" android:pathData="M30.4,365.6h6.9c2.4,0 4.2,-0.6 5.4,-1.9c1.2,-1.3 1.8,-3.1 1.8,-5.6c0,-2.3 -0.6,-4 -1.9,-5.1c-1.3,-1.1 -3.2,-1.7 -5.8,-1.7h-6.4V365.6zM30.4,388.2h6.9c2.9,0 5,-0.7 6.5,-2c1.4,-1.3 2.1,-3.3 2.1,-6c0,-2.7 -0.7,-4.6 -2.1,-5.8c-1.4,-1.2 -3.7,-1.7 -7,-1.7h-6.4V388.2zM14.4,395.9v-7.7h6.1v-36.8h-6.1V344h24.8c5.5,0 9.5,1 11.9,3c2.4,2 3.6,5.2 3.6,9.7c0,3.4 -0.8,6.1 -2.3,8c-1.5,1.9 -3.8,3.2 -6.9,3.8c3.5,0.6 6.1,2 7.9,4.1c1.8,2.1 2.7,4.9 2.7,8.5c0,5.1 -1.4,8.9 -4.1,11.3c-2.7,2.4 -7,3.6 -12.8,3.6H14.4z"/>
    <path android:fillColor="#FF000000" android:fillType="evenOdd" android:pathData="M68.8,377.3c0,4 0.7,7 2,9c1.3,2 3.2,3 5.8,3c2.6,0 4.5,-1 5.8,-3.1c1.3,-2 2,-5 2,-9c0,-4 -0.6,-6.9 -1.9,-8.9c-1.3,-2 -3.2,-3 -5.8,-3c-2.6,0 -4.5,1 -5.8,3C69.5,370.4 68.8,373.4 68.8,377.3zM59,377.4c0,-6 1.6,-10.7 4.7,-14.2c3.2,-3.5 7.5,-5.2 12.9,-5.2c5.5,0 9.7,1.7 12.9,5.2c3.1,3.5 4.7,8.2 4.7,14.2c0,6 -1.6,10.8 -4.7,14.3c-3.1,3.5 -7.4,5.2 -12.9,5.2c-5.4,0 -9.7,-1.7 -12.8,-5.2C60.6,388.2 59,383.4 59,377.4z"/>
    <path android:fillColor="#FF000000" android:fillType="evenOdd" android:pathData="M99.7,382.7v-16.5h-4.2v-7.3h13.7v23.8c0,2 0.5,3.6 1.5,4.7c1,1.2 2.4,1.7 4.1,1.7c2.4,0 4.4,-0.9 5.8,-2.6c1.5,-1.8 2.2,-4.1 2.2,-6.9v-13.4h-4.2v-7.3h13.6v29.8h3.5v7.2h-12.6V390c-1.6,2.4 -3.3,4.1 -5.1,5.2c-1.8,1.1 -3.9,1.6 -6.4,1.6c-4.2,0 -7.2,-1.1 -9.1,-3.4C100.6,391.3 99.7,387.7 99.7,382.7z"/>
    <path android:fillColor="#FF000000" android:fillType="evenOdd" android:pathData="M142.8,388.5v-22.4h-4.5v-7.2h13.9v6.7c1.3,-2.7 2.8,-4.7 4.5,-6c1.7,-1.3 3.6,-1.9 5.9,-1.9c0.5,0 1.1,0 1.7,0.1c0.6,0.1 1.3,0.2 2.2,0.4v8.8c-0.6,-0.1 -1.2,-0.2 -1.9,-0.3c-0.6,-0.1 -1.3,-0.1 -1.9,-0.1c-3.6,0 -6.2,1.1 -7.9,3.4c-1.7,2.3 -2.5,5.8 -2.5,10.6v7.8h4.7v7.4H138v-7.4H142.8z"/>
    <path android:fillColor="#FF000000" android:fillType="evenOdd" android:pathData="M167.8,395.9v-7.4h4.5v-22.4h-4.5v-7.2h13.7v5.9c1.5,-2.4 3.2,-4.1 5.1,-5.2c1.9,-1.1 4.2,-1.6 6.8,-1.6c3.9,0 6.8,1.1 8.7,3.4c1.9,2.3 2.8,5.9 2.8,10.7v16.4h4.4v7.4h-19v-7.4h5.1v-16.4c0,-2 -0.5,-3.6 -1.5,-4.8c-1,-1.2 -2.4,-1.8 -4.1,-1.8c-2.4,0 -4.4,0.9 -5.8,2.7c-1.5,1.8 -2.2,4.1 -2.2,7v13.3h5.1v7.4H167.8z"/>
    <path android:fillColor="#FF000000" android:fillType="evenOdd" android:pathData="M220.1,373.7h14.4v-0.8c0,-2.2 -0.7,-4 -2,-5.4c-1.3,-1.4 -3.1,-2.1 -5.2,-2.1c-2.1,0 -3.9,0.8 -5.2,2.3C220.8,369.1 220.1,371.1 220.1,373.7zM234.7,384.4h8.8c-0.8,4.2 -2.5,7.3 -5,9.4c-2.6,2.1 -6,3.1 -10.5,3.1c-5.6,0 -9.9,-1.7 -12.9,-5.1c-3.1,-3.4 -4.6,-8.2 -4.6,-14.3c0,-6 1.5,-10.8 4.5,-14.3c3,-3.5 7.1,-5.2 12.3,-5.2c5.3,0 9.4,1.6 12.3,4.9c2.9,3.3 4.3,8 4.3,14.1c0,0.4 0,0.8 0,1.3c0,0.5 -0.1,1.2 -0.1,2l-23.7,0c0,2.9 0.6,5.1 1.9,6.7c1.3,1.6 3.2,2.3 5.6,2.3c1.8,0 3.2,-0.4 4.4,-1.2C233.1,387.3 234,386.1 234.7,384.4z"/>
    <path android:fillColor="#FF000000" android:fillType="evenOdd" android:pathData="M245.8,395.9v-7.4h4.5v-22.4h-4.5v-7.2h13.7v5.9c1.5,-2.3 3.2,-4 5,-5.2c1.8,-1.1 3.8,-1.7 6.1,-1.7c2.6,0 4.8,0.6 6.8,1.8c1.9,1.2 3.4,2.9 4.3,5c1.3,-2.4 2.8,-4.1 4.5,-5.2c1.7,-1.1 3.9,-1.6 6.4,-1.6c4.1,0 7.2,1.1 9,3.3c1.9,2.2 2.8,5.8 2.8,10.8v16.4h4.4v7.4h-18.4v-7.4h4.7v-15.3c0,-2.5 -0.5,-4.4 -1.4,-5.7c-0.9,-1.3 -2.3,-1.9 -4.2,-1.9c-2.1,0 -3.8,0.9 -5.1,2.7c-1.4,1.8 -2,4.1 -2,6.9v13.3h4.5v7.4h-18.4v-7.4h4.5v-15.3c0,-2.5 -0.5,-4.4 -1.4,-5.7c-1,-1.3 -2.4,-1.9 -4.3,-1.9c-2.1,0 -3.9,0.9 -5.3,2.7c-1.4,1.8 -2.1,4.1 -2.1,6.9v13.3h5.1v7.4H245.8z"/>
    <path android:fillColor="#FF000000" android:fillType="evenOdd" android:pathData="M319.6,377.3c0,4 0.7,7 2,9c1.3,2 3.2,3 5.8,3c2.6,0 4.5,-1 5.8,-3.1c1.3,-2 2,-5 2,-9c0,-4 -0.6,-6.9 -1.9,-8.9c-1.3,-2 -3.2,-3 -5.8,-3c-2.6,0 -4.5,1 -5.8,3C320.2,370.4 319.6,373.4 319.6,377.3zM309.8,377.4c0,-6 1.6,-10.7 4.7,-14.2c3.2,-3.5 7.5,-5.2 12.9,-5.2c5.5,0 9.7,1.7 12.9,5.2c3.1,3.5 4.7,8.2 4.7,14.2c0,6 -1.6,10.8 -4.7,14.3c-3.1,3.5 -7.4,5.2 -12.9,5.2c-5.4,0 -9.7,-1.7 -12.8,-5.2C311.3,388.2 309.8,383.4 309.8,377.4z"/>
    <path android:fillColor="#FF000000" android:fillType="evenOdd" android:pathData="M350.4,382.7v-16.5h-4.2v-7.3h13.7v23.8c0,2 0.5,3.6 1.5,4.7c1,1.2 2.4,1.7 4.1,1.7c2.4,0 4.3,-0.9 5.8,-2.6c1.5,-1.8 2.2,-4.1 2.2,-6.9v-13.4h-4.2v-7.3H383v29.8h3.5v7.2h-12.6V390c-1.6,2.4 -3.3,4.1 -5.1,5.2c-1.8,1.1 -3.9,1.6 -6.4,1.6c-4.2,0 -7.2,-1.1 -9.1,-3.4C351.3,391.3 350.4,387.7 350.4,382.7z"/>
    <path android:fillColor="#FF000000" android:fillType="evenOdd" android:pathData="M410.9,366.3h-8.8v19.6c0,1.2 0.2,2.1 0.6,2.6c0.4,0.5 1,0.8 1.7,0.8c1,0 1.7,-0.4 2.2,-1.2c0.5,-0.8 0.7,-2 0.7,-3.5v-1h5.8c0,0.6 0.1,1.1 0.1,1.4c0,0.3 0,0.6 0,0.9c0,3.5 -0.9,6.3 -2.7,8.2c-1.8,1.9 -4.3,2.8 -7.6,2.8c-3.6,0 -6.2,-0.8 -7.8,-2.4c-1.7,-1.6 -2.5,-4.1 -2.5,-7.5v-20.8h-5.3v-7.3h5.3v-7.3l9.5,-3.8v11.1h8.8V366.3z"/>
    <path android:fillColor="#FF000000" android:fillType="evenOdd" android:pathData="M413.9,395.9v-7.4h4.5v-37.3h-4.5V344h13.7v20.8c1.5,-2.4 3.2,-4.1 5.1,-5.2c1.9,-1.1 4.2,-1.6 6.8,-1.6c3.9,0 6.8,1.1 8.7,3.4c1.9,2.3 2.8,5.9 2.8,10.7v16.4h4.4v7.4h-19v-7.4h5.1v-16.4c0,-2 -0.5,-3.6 -1.5,-4.8c-1,-1.2 -2.4,-1.7 -4.1,-1.7c-2.4,0 -4.4,0.9 -5.8,2.7c-1.5,1.8 -2.2,4.1 -2.2,7v13.3h5.1v7.4H413.9z"/>
    <path android:fillType="evenOdd" android:pathData="M237.2,16.4c0,0 0,96.7 0,195.1c0,67.1 49.8,103.4 108.1,103.4c59.7,0 108.5,-40.1 108.5,-103.4c0,-98.4 0,-195.1 0,-195.1h-47.2c0,0 0,150.3 0,195.1c0,40.9 -29.8,58.9 -61.3,58.9s-61.1,-16.3 -61.1,-58.9c0,-42.6 0,-152.5 0,-152.5h5c0,0 0,119.9 0,152.5c0,38.7 30.6,53.3 56.1,53.3c25.4,0 56.4,-15.4 56.4,-53.3c0,-40.3 0,-154.2 0,-154.2h-20.8c0,0 0,132.1 0,154.2c0,22.1 -18.4,33.1 -35.5,33.1c-17.1,0 -35.2,-9.4 -35.2,-33.1c0,-23.8 0,-174.6 0,-174.6h-46.5c0,0 0,128.2 0,174.6c0,46.4 32.5,76.9 81.7,76.9c49.2,0 82.2,-29.4 82.2,-76.9c0,-47.5 0,-173.3 0,-173.3h6c0,0 0,129.1 0,173.3c0,42.5 -27,82.3 -88.2,82.3c-61.3,0 -86.9,-40.3 -86.9,-82.3c0,-37.6 0,-195.1 0,-195.1H237.2z">
        <aapt:attr name="android:fillColor">
            <gradient android:endX="453.793" android:endY="165.6182"
                android:startX="237.2373" android:startY="165.6182" android:type="linear">
                <item android:color="#FFF26F39" android:offset="0.005618"/>
                <item android:color="#FFE9316B" android:offset="1"/>
            </gradient>
        </aapt:attr>
    </path>
</vector>
