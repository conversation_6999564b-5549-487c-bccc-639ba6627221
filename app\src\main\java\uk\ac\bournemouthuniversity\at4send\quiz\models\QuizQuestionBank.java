package uk.ac.bournemouthuniversity.at4send.quiz.models;

import androidx.core.util.Pair;

import java.util.List;

import uk.ac.bournemouthuniversity.at4send.extensions.utils.ArrayListAnySize;

public class QuizQuestionBank {
    public List<Pair<Pair<String, String>, List<Answer>>> qas;
    public List<String> categories;

    public QuizQuestionBank(List<Pair<Pair<String, String>, List<Answer>>> qas, List<String> categories) {
        this.qas = qas;
        this.categories = categories;
    }

    public List<Question> getQuizQuestionBank(){
        List<Question> quizQuestions = new ArrayListAnySize<>();
        for(int i = 0; i < this.qas.size(); i++){
            quizQuestions.add(new Question(this.qas.get(i).first.first, this.qas.get(i).first.second, this.qas.get(i).second, this.categories));
        }
        return quizQuestions;
    }
}
