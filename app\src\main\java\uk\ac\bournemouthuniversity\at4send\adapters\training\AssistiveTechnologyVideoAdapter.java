package uk.ac.bournemouthuniversity.at4send.adapters.training;

import android.content.Context;
import android.net.Uri;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.browser.customtabs.CustomTabsIntent;
import androidx.core.content.res.ResourcesCompat;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.models.training.AssistiveTechnologyVideo;

public class AssistiveTechnologyVideoAdapter extends RecyclerView.Adapter<AssistiveTechnologyVideoAdapter.ViewHolder> {
    private static final String TAG = "AssistiveTechnologyVideoAdapter";

    private List<AssistiveTechnologyVideo> videoList;
    private Context context;

    public class ViewHolder extends RecyclerView.ViewHolder {
        AppCompatTextView videoTitle, videoDescription, videoCreator;
        LinearLayout itemView;
        public boolean selected = false;

        public ViewHolder(View view) {
            super(view);
            itemView = view.findViewById(R.id.itemView);
            videoTitle = view.findViewById(R.id.videoTitle);
            videoDescription = view.findViewById(R.id.videoShortDescription);
            videoCreator = view.findViewById(R.id.videoCreator);
        }
    }

    public AssistiveTechnologyVideoAdapter(Context context, List<AssistiveTechnologyVideo> technologyList) {
        this.videoList = technologyList;
        this.context = context;
    }

    @NonNull
    @Override
    public AssistiveTechnologyVideoAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_video, parent, false);

        return new AssistiveTechnologyVideoAdapter.ViewHolder(itemView);
    }

    @Override
    public void onBindViewHolder(@NonNull final AssistiveTechnologyVideoAdapter.ViewHolder holder, final int position) {
        final AssistiveTechnologyVideo item = videoList.get(position);

        holder.videoTitle.setText(item.getVideoTitle());
        holder.videoDescription.setText(item.getVideoShortDescription());
        holder.videoCreator.setText(item.getVideoCreator());

        holder.itemView.setOnClickListener(v -> {
            CustomTabsIntent.Builder builder = new CustomTabsIntent.Builder();
            builder.setToolbarColor(ResourcesCompat.getColor(context.getResources(), R.color.md_blue_500, null));
            builder.setSecondaryToolbarColor(ResourcesCompat.getColor(context.getResources(), R.color.md_blue_600, null));
            CustomTabsIntent customTabsIntent = builder.build();
            customTabsIntent.launchUrl(context, Uri.parse(item.getVideoLink()));
        });
    }

    @Override
    public int getItemCount() {
        return videoList.size();
    }

}
