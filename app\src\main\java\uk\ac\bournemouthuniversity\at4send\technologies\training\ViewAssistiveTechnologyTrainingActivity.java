package uk.ac.bournemouthuniversity.at4send.technologies.training;

import android.os.Bundle;
import android.view.MenuItem;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import uk.ac.bournemouthuniversity.at4send.BaseActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;
import androidx.viewpager2.widget.ViewPager2;

import com.google.android.material.appbar.CollapsingToolbarLayout;
import com.google.android.material.tabs.TabLayoutMediator;

import org.jetbrains.annotations.NotNull;

import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.technologies.training.fragments.AssistiveTechnologyDescriptionFragment;
import uk.ac.bournemouthuniversity.at4send.utils.ReadAloudUtils;
import uk.ac.bournemouthuniversity.at4send.utils.TextToSpeechManager;

public class ViewAssistiveTechnologyTrainingActivity extends BaseActivity {
    private static final String TAG = "ViewTechnologyTraining";

    public static final String EXTRA_TECHNOLOGY = "technologySerialized";

    private String[] mTechnologyInformation;

    private final String[] tabTitles = new String[]{
            "Description",
            "Benefits",
            "Limitations"
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_view_assistive_technology_training);

        Bundle extras = getIntent().getExtras();

        if(extras != null) {
            String mTechnologySerialized = extras.getString(EXTRA_TECHNOLOGY);
            mTechnologyInformation = mTechnologySerialized.split("__");

            if(mTechnologyInformation.length == 0){
                finish();
                return;
            }
        }

        CollapsingToolbarLayout toolbarLayout = findViewById(R.id.toolbar_layout);
        toolbarLayout.setTitle("");  // Clear the CollapsingToolbarLayout title

        // Set up the title TextView with read-aloud functionality
        TextView titleTextView = findViewById(R.id.toolbar_title);
        titleTextView.setText(mTechnologyInformation[0]);
        ReadAloudUtils.addReadAloudButton(this, titleTextView);

        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        ActionBar ab = getSupportActionBar();

        if(ab != null){
            ab.setDisplayHomeAsUpEnabled(true);
            ab.setDisplayShowTitleEnabled(false);  // Hide the app name in the toolbar
        }

        ((ViewPager2)findViewById(R.id.view_pager)).setAdapter(new ViewPagerFragmentAdapter(this));

        // attaching tab mediator
        new TabLayoutMediator(findViewById(R.id.tab_layout), findViewById(R.id.view_pager), (tab, position) -> {
            tab.setText(tabTitles[position]);
        }).attach();
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        int itemId = item.getItemId();

        if(itemId == android.R.id.home){
            finish();
            return true;
        }

        return super.onOptionsItemSelected(item);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Stop TTS when activity is destroyed
        TextToSpeechManager.getInstance(this).stop();
    }

    private class ViewPagerFragmentAdapter extends FragmentStateAdapter {

        public ViewPagerFragmentAdapter(@NonNull FragmentActivity fragmentActivity) {
            super(fragmentActivity);
        }

        @NotNull
        @Override
        public Fragment createFragment(int position) {
            switch (position) {
                case 0:
                    return AssistiveTechnologyDescriptionFragment.newInstance(mTechnologyInformation[1]);
                case 1:
                    return AssistiveTechnologyDescriptionFragment.newInstance(mTechnologyInformation[2]);
                case 2:
                    return AssistiveTechnologyDescriptionFragment.newInstance(mTechnologyInformation[3]);
            }
            return AssistiveTechnologyDescriptionFragment.newInstance(mTechnologyInformation[1]);
        }

        @Override
        public int getItemCount() {
            return tabTitles.length;
        }
    }
}


