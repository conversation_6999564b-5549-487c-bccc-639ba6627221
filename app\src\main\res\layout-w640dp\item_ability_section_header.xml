<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:paddingStart="16dp"
    android:paddingEnd="16dp"
    android:paddingTop="12dp"
    android:paddingBottom="4dp">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/abilitySectionName"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_height="wrap_content"
        tools:text="Section Name"
        android:textAppearance="@style/AT4SEND.AppTheme.TextAppearance.Title"
        android:textSize="22sp"
        android:textStyle="bold"/>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_weight="2"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginStart="16dp">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:text="Easy"
            android:textSize="16sp"
            android:textStyle="bold"
            android:gravity="start"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:text="Difficult"
            android:textSize="16sp"
            android:textStyle="bold"
            android:gravity="center"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:text="Impossible"
            android:textSize="16sp"
            android:textStyle="bold"
            android:gravity="end"/>

    </LinearLayout>

</LinearLayout>
