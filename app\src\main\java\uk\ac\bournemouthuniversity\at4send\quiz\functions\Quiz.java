package uk.ac.bournemouthuniversity.at4send.quiz.functions;

import java.util.List;

import uk.ac.bournemouthuniversity.at4send.quiz.helpers.QuizSession;
import uk.ac.bournemouthuniversity.at4send.quiz.models.Answer;
import uk.ac.bournemouthuniversity.at4send.quiz.models.Question;

public interface Quiz {

    public abstract void reset();

    public abstract QuizSession getSession();

    public abstract void setSession(QuizSession session);

    public abstract Question getQuestion();

    public abstract boolean checkAnswer(Question question, Answer answer,
                                        boolean firstAttempt);

    public abstract boolean checkAnswers(Question question,
                                         List<Answer> answer, boolean firstAttempt);

    public abstract boolean isNewRecord();

    public abstract int getRecord();

    public abstract void setRecord(int record);

    public abstract boolean isExited();

    public abstract void setExited(boolean exited);

}
