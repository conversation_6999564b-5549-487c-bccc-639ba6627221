<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/md_grey_50"
    android:id="@+id/mainView"
    tools:context=".recommendations.NewAssessmentActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:elevation="0dp">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            app:contentInsetStartWithNavigation="0dp"
            app:titleTextAppearance="@style/AT4SEND.AppTheme.TextAppearance.Title"
            app:title="Administration"
            app:theme="@style/AT4SEND.BlueTheme.NoActionBar.Toolbar">

        </androidx.appcompat.widget.Toolbar>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

        <androidx.core.widget.NestedScrollView
            android:id="@+id/mainScrollView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.gabrielepmattia.materialfields.fields.FieldInputTextNoIcon
                    android:id="@+id/fieldTechnologyName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:title="Name"
                    app:placeholder="Please Enter..."
                    app:required="true"
                    />

                <com.gabrielepmattia.materialfields.fields.FieldInputTextNoIcon
                    android:id="@+id/fieldTechnologyDescription"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:title="Description"
                    app:placeholder="Please Enter..."
                    app:required="true"
                    />

                <com.gabrielepmattia.materialfields.fields.FieldGenericNoIcon
                    android:id="@+id/fieldTechnologyManufacturer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:title="Manufacturer"
                    app:placeholder="Please Select..."
                    app:required="true"
                    />

                <com.gabrielepmattia.materialfields.fields.FieldGenericNoIcon
                    android:id="@+id/fieldTechnologyWebsite"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:title="Product Website"
                    app:placeholder="Please Enter..."
                    app:required="true"
                    />


                <com.gabrielepmattia.materialfields.fields.FieldGenericNoIcon
                    android:id="@+id/fieldTechnologyRRP"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:title="Recommended Retail Price"
                    app:value="£0.00"
                    app:required="true"
                    android:visibility="gone"
                    />


                <com.gabrielepmattia.materialfields.texts.Header
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:text="Ability Mappings" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="16dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="8dp">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/ability_mapping_description"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="14sp"
                        android:text="@string/ability_mapping_helper_description"
                        android:textColor="@color/grey600" />

                </LinearLayout>

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardBackgroundColor="@color/md_white_1000"
                    app:cardCornerRadius="0dp"
                    app:strokeWidth="1dp"
                    app:strokeColor="@color/md_grey_200"
                    app:cardElevation="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">
                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/mainRecyclerViewAbilities"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            tools:listitem="@layout/item_ability_rating_scale"/>
                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/md_grey_200"
                    android:layout_marginTop="-2dp"/>

                <com.gabrielepmattia.materialfields.texts.Header
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:text="Condition Mappings" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/condition_mapping_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="8dp"
                    android:textSize="14sp"
                    android:text="@string/condition_mapping_helper_description"
                    android:textColor="@color/grey600" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardBackgroundColor="@color/md_white_1000"
                    app:cardCornerRadius="0dp"
                    app:strokeWidth="1dp"
                    app:strokeColor="@color/md_grey_200"
                    app:cardElevation="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/mainRecyclerViewConditions"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            tools:listitem="@layout/item_condition_selection"/>
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/md_grey_200"
                    android:layout_marginTop="-2dp"/>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="center_vertical"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:paddingTop="12dp"
                    android:paddingBottom="12dp"
                    android:background="@color/md_white_1000">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/submitTechnology"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:letterSpacing="0"
                        android:textStyle="bold"
                        style="@style/Widget.MaterialComponents.Button.UnelevatedButton"
                        android:text="ADD ASSISTIVE TECHNOLOGY" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/returnHome"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:letterSpacing="0"
                        style="@style/Widget.MaterialComponents.Button.UnelevatedButton"
                        android:text="RETURN TO ADMINISTRATION MENU"
                        app:backgroundTint="@color/md_orange_500"/>
                </LinearLayout>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>