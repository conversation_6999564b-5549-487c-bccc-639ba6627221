package uk.ac.bournemouthuniversity.at4send.admin;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import uk.ac.bournemouthuniversity.at4send.BaseActivity;
import androidx.appcompat.widget.Toolbar;

import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.widget.TextView;

import com.afollestad.materialdialogs.MaterialDialog;

import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.extensions.utils.Network;
import uk.ac.bournemouthuniversity.at4send.technologies.AssistiveTechnologyListActivity;
import uk.ac.bournemouthuniversity.at4send.utils.ReadAloudUtils;
import uk.ac.bournemouthuniversity.at4send.utils.TextToSpeechManager;

public class AdminHomeActivity extends BaseActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_admin_home);

        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        ActionBar ab = getSupportActionBar();

        if(ab != null){
            ab.setDisplayHomeAsUpEnabled(true);
            //ab.setHomeAsUpIndicator(R.drawable.ic_close);
        }

        // Add read-aloud button to instructions text
        TextView instructionsText = findViewById(R.id.admin_instructions);
        if (instructionsText != null) {
            ReadAloudUtils.addReadAloudButton(this, instructionsText);
        }
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        int itemId = item.getItemId();

        if(itemId == android.R.id.home){
            super.onBackPressed();
            findViewById(R.id.returnHome).setOnClickListener(v -> finish());
            return true;
        }

        return super.onOptionsItemSelected(item);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Stop TTS when activity is destroyed
        TextToSpeechManager.getInstance(this).stop();
    }

    public void newAssistiveTechnology(View view){
        if(!Network.isNetworkAvailable(this)){
            new MaterialDialog(AdminHomeActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                    .title(null, "Network Access Required")
                    .message(null, "An active network connection is required for this function. Please connect to the internet and try again.", null)
                    .cancelable(false)
                    .positiveButton(null, "Dismiss", materialDialog -> {
                        materialDialog.dismiss();
                        finish();
                        return null;
                    }).show();
            return;
        }

        Intent i = new Intent(this, NewAssistiveTechnologyActivity.class);
        startActivity(i);
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_left);
    }

    public void editAssistiveTechnology(View view){
        if(!Network.isNetworkAvailable(this)){
            new MaterialDialog(AdminHomeActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                    .title(null, "Network Access Required")
                    .message(null, "An active network connection is required for this function. Please connect to the internet and try again.", null)
                    .cancelable(false)
                    .positiveButton(null, "Dismiss", materialDialog -> {
                        materialDialog.dismiss();
                        finish();
                        return null;
                    }).show();
            return;
        }

        Intent i = new Intent(this, AssistiveTechnologyListActivity.class);
        i.putExtra(AssistiveTechnologyListActivity.EXTRA_ACTION_TYPE, AssistiveTechnologyListActivity.ACTION_EDIT);
        startActivity(i);
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_left);
    }

    public void deleteAssistiveTechnology(View view){
        if(!Network.isNetworkAvailable(this)){
            new MaterialDialog(AdminHomeActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                    .title(null, "Network Access Required")
                    .message(null, "An active network connection is required for this function. Please connect to the internet and try again.", null)
                    .cancelable(false)
                    .positiveButton(null, "Dismiss", materialDialog -> {
                        materialDialog.dismiss();
                        finish();
                        return null;
                    }).show();
            return;
        }

        Intent i = new Intent(this, AssistiveTechnologyListActivity.class);
        i.putExtra(AssistiveTechnologyListActivity.EXTRA_ACTION_TYPE, AssistiveTechnologyListActivity.ACTION_DELETE);
        startActivity(i);
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_left);
    }

    public void returnHome(View view){
        finish();
    }
}
