package uk.ac.bournemouthuniversity.at4send.adapters.training;

import android.content.Context;
import android.net.Uri;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.browser.customtabs.CustomTabsIntent;
import androidx.browser.customtabs.CustomTabColorSchemeParams;
import androidx.core.content.res.ResourcesCompat;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.models.training.AssistiveTechnologyArticle;

public class AssistiveTechnologyArticleAdapter extends RecyclerView.Adapter<AssistiveTechnologyArticleAdapter.ViewHolder> {
    private static final String TAG = "AssistiveTechnologyArticleAdapter";

    private List<AssistiveTechnologyArticle> articleList;
    private Context context;

    public class ViewHolder extends RecyclerView.ViewHolder {
        AppCompatTextView articleTitle, articleDescription, articleAuthor;
        LinearLayout itemView;
        public boolean selected = false;

        public ViewHolder(View view) {
            super(view);
            itemView = view.findViewById(R.id.itemView);
            articleTitle = view.findViewById(R.id.articleTitle);
            articleDescription = view.findViewById(R.id.articleShortDescription);
            articleAuthor = view.findViewById(R.id.articleAuthor);
        }
    }

    public AssistiveTechnologyArticleAdapter(Context context, List<AssistiveTechnologyArticle> technologyList) {
        this.articleList = technologyList;
        this.context = context;
    }

    @NonNull
    @Override
    public AssistiveTechnologyArticleAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_learn_article, parent, false);

        return new AssistiveTechnologyArticleAdapter.ViewHolder(itemView);
    }

    @Override
    public void onBindViewHolder(@NonNull final AssistiveTechnologyArticleAdapter.ViewHolder holder, final int position) {
        final AssistiveTechnologyArticle item = articleList.get(position);

        holder.articleTitle.setText(item.getArticleTitle());
        holder.articleDescription.setText(item.getArticleShortDescription());
        holder.articleAuthor.setText(item.getArticleAuthor());

        holder.itemView.setOnClickListener(v -> {
            CustomTabsIntent intent = new CustomTabsIntent.Builder()
                .setColorSchemeParams(CustomTabsIntent.COLOR_SCHEME_DARK, new CustomTabColorSchemeParams.Builder()
                    .setToolbarColor(ResourcesCompat.getColor(context.getResources(), R.color.md_blue_500, null))
                    .build())
                .build();
            intent.launchUrl(context, Uri.parse(item.getArticleLink()));
        });
    }

    @Override
    public int getItemCount() {
        return articleList.size();
    }

}


