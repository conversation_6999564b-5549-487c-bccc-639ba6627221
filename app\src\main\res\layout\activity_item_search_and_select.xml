<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/mainView"
    android:background="@color/md_grey_50"
    tools:context=".common.ItemSearchAndSelectActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:elevation="0dp">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            app:contentInsetStartWithNavigation="0dp"
            app:titleTextAppearance="@style/AT4SEND.AppTheme.TextAppearance.Title"
            app:title="Administration"
            app:theme="@style/AT4SEND.BlueTheme.NoActionBar.Toolbar">

        </androidx.appcompat.widget.Toolbar>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:fillViewport="true"
            app:layout_constraintBottom_toTopOf="@id/bottomView"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/filterTextContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardBackgroundColor="@color/md_white_1000"
                    app:cardCornerRadius="0dp"
                    android:layout_marginBottom="20dp"
                    android:visibility="gone">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:gravity="center_vertical"
                            android:paddingStart="16dp"
                            android:paddingEnd="16dp"
                            android:paddingTop="8dp"
                            android:paddingBottom="8dp">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/filterText"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Below is the available Assistive Technologies from:"
                                android:textSize="16sp"/>

                        </LinearLayout>

                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardBackgroundColor="@color/md_white_1000"
                    app:cardCornerRadius="0dp"
                    app:strokeWidth="1dp"
                    app:strokeColor="@color/md_grey_200"
                    app:cardElevation="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/mainRecyclerView"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            tools:listitem="@layout/item_selection"/>
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/bottomView"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardBackgroundColor="@color/md_white_1000"
            app:cardCornerRadius="0dp"
            app:strokeWidth="1dp"
            app:strokeColor="@color/md_grey_200"
            app:cardElevation="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center_vertical"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingTop="12dp"
                android:paddingBottom="12dp">

                <com.google.android.material.button.MaterialButton
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:letterSpacing="0"
                    android:textStyle="bold"
                    style="@style/Widget.MaterialComponents.Button.UnelevatedButton"
                    android:text="Confirm Selection(s)"
                    android:onClick="confirmSelections"
                    app:backgroundTint="@color/md_blue_600"/>

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>