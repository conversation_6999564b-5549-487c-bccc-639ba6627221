package uk.ac.bournemouthuniversity.at4send.extensions.helpers;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.SharedPreferences;

import androidx.security.crypto.EncryptedSharedPreferences;
import androidx.security.crypto.MasterKey;

import com.google.firebase.crashlytics.FirebaseCrashlytics;

import java.io.IOException;
import java.security.GeneralSecurityException;

public class PreferenceManager {

    public SharedPreferences pref, encryptedPreferences;
    private SharedPreferences.Editor editor, encryptedEditor;

    @SuppressLint("CommitPrefEdits")
    public PreferenceManager(Context context) {
        pref = PreferenceManager.getDefaultSharedPreferences(context);
        encryptedPreferences = PreferenceManager.getEncryptedSharedPreferences(context);
        editor = pref.edit();
        encryptedEditor = encryptedPreferences.edit();
    }

    private static SharedPreferences getDefaultSharedPreferences(Context ctx) {
        return androidx.preference.PreferenceManager.getDefaultSharedPreferences(ctx);
    }

    private static SharedPreferences getEncryptedSharedPreferences(Context ctx) {
        try {

            MasterKey.Builder masterKeyBuilder = new MasterKey.Builder(ctx);
            masterKeyBuilder.setKeyScheme(MasterKey.KeyScheme.AES256_GCM);

            return EncryptedSharedPreferences.create(
                    ctx,
                    "AT4SENDSharedPreferencesFile",
                    masterKeyBuilder.build(),
                    EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
                    EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
            );
        }catch(GeneralSecurityException | IOException e){
            FirebaseCrashlytics.getInstance().recordException(e);
            return null;
        }
    }

    public boolean getSharedBooleanPreference(String name, boolean deflt, boolean encypted){
        if(encypted){
            return encryptedPreferences.getBoolean(name, deflt);
        }
        return pref.getBoolean(name, deflt);
    }

    public void setSharedBooleanPreference(String name, boolean value, boolean encypted){
        if(encypted){
            encryptedEditor.putBoolean(name, value);
            encryptedEditor.apply();
            return;
        }
        editor.putBoolean(name, value);
        editor.apply();
    }

    public String getSharedStringPreference(String name, boolean encypted){
        if(encypted){
            return encryptedPreferences.getString(name, null);
        }
        return pref.getString(name, null);
    }

    public void setSharedStringPreference(String name, String value, boolean encypted){
        if(encypted){
            encryptedEditor.putString(name, value);
            encryptedEditor.apply();
            return;
        }
        editor.putString(name, value);
        editor.apply();
    }

    public int getSharedIntegerPreference(String name, boolean encypted){
        if(encypted){
            return encryptedPreferences.getInt(name, -1);
        }
        return pref.getInt(name, -1);
    }

    public void setSharedIntegerPreference(String name, int value, boolean encypted){
        if(encypted){
            encryptedEditor.putInt(name, value);
            encryptedEditor.apply();
            return;
        }
        editor.putInt(name, value);
        editor.apply();
    }

    public void clear(){
        editor.clear();
        editor.apply();
        encryptedEditor.clear();
        encryptedEditor.apply();
    }
}
