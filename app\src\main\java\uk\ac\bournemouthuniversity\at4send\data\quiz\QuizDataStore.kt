package uk.ac.bournemouthuniversity.at4send.data.quiz

import androidx.core.util.Pair
import uk.ac.bournemouthuniversity.at4send.quiz.models.Answer
import uk.ac.bournemouthuniversity.at4send.quiz.models.Question
import uk.ac.bournemouthuniversity.at4send.quiz.models.QuizQuestionBank

object QuizDataStore {

    private val switchesQuiz: QuizQuestionBank = QuizQuestionBank(
        listOf<Pair<Pair<String, String>, List<Answer>>>(
            Pair(Pair("Which of the following is not a type of switch?", ""), listOf(
                Answer("A - Sound", false),
                Answer("B - Light", false),
                Answer("C - Solid", true),
                Answer("D - Saucer", false)
            )),
            Pair(Pair("Which of the following devices can be accessed by an Adaptive Switch?", ""), listOf(
                Answer("A - Powered Wheelchairs", true),
                Answer("B - Bikes", false),
                Answer("C - Cars", false),
                Answer("D - Manual Wheelchairs", false)
            )),
            Pair(Pair("Complete the following statement about Pillow Switches, 'switches that have a soft foam surface serve ...':", ""), listOf(
                Answer("A - ... to increase the comfort for a user.", false),
                Answer("B - ... as the activation surface.", true),
                Answer("C - ... to make them easy to press.", false),
                Answer("D - ... to reduce the noise of the switch.", false)
            )),
            Pair(Pair("Which of the following can be used to activate a Wheelchair Switch?", ""), listOf(
                Answer("A - Elbows", false),
                Answer("B - Tongue", false),
                Answer("C - Vibration", true),
                Answer("D - Eyes", false)
            )),
            Pair(Pair("Which users are Adaptive Switches designed for?", ""), listOf(
                Answer("A - People with cognitive impairments", false),
                Answer("B - People with motor impairments", true),
                Answer("C - Carers", false),
                Answer("D - Teachers", false)
            )),
            Pair(Pair("Which of the following is not a benefit of an Adaptive Switch?", ""), listOf(
                Answer("A - Improved brain development for children", false),
                Answer("B - Better access to technology and computers", false),
                Answer("C - Increased independence", false),
                Answer("D - Compatible with all devices", true)
            ))
        ),
        listOf("Switches")
    )

    private val altKeyboardQuiz: QuizQuestionBank = QuizQuestionBank(
        listOf<Pair<Pair<String, String>, List<Answer>>>(
            Pair(Pair("Which of the following is not a type of alternative keyboard?", ""), listOf(
                Answer("A - Compact", false),
                Answer("B - High Contrast", false),
                Answer("C - Small Key", true),
                Answer("D - Ergonomic", false)
            )),
            Pair(Pair("Why are standard computer keyboards more suited to right hand users?", ""), listOf(
                Answer("A - The Numeric Keypad is on the right hand side", true),
                Answer("B - There is no difference for right hand or left hand users", false),
                Answer("C - The navigation keys are located on the right hand side", false),
                Answer("D - The arrow keys are located on the right hand side", false)
            )),
            Pair(Pair("When using an eye tracking system to control an onscreen keyboard, how can the keys be selected?", ""), listOf(
                Answer("A - Blinking", false),
                Answer("B - Focussing", true),
                Answer("C - Winking", false),
                Answer("D - Looking down", false)
            )),
            Pair(Pair("Which type of alternative keyboard reduces the likelihood of Repetitive Strain Injuries?", ""), listOf(
                Answer("A - Larger Key", false),
                Answer("B - Compact", false),
                Answer("C - Ergonomic", true),
                Answer("D - High Contrast", false)
            )),
            Pair(Pair("Other than reducing accidental key presses, what is another benefit of keyguards?", ""), listOf(
                Answer("A - The size of the keys is increased", false),
                Answer("B - The text on the keys is larger", false),
                Answer("C - The keyguard is a permanently attached to the keyboard", false),
                Answer("D - You can rest arms and hands on the keyboard without pressing the keys", true)
            ))
        ),
        listOf("Alternative Keyboards")
    )

    private val altMiceQuiz: QuizQuestionBank = QuizQuestionBank(
        listOf<Pair<Pair<String, String>, List<Answer>>>(
            Pair(Pair("Which of the following is a type of alternative mice?", ""), listOf(
                Answer("A - Horizontal", false),
                Answer("B - Vertical", true),
                Answer("C - Diagonal", false),
                Answer("D - Circle", false)
            )),
            Pair(Pair("Which is ideally used to control bar mice?", ""), listOf(
                Answer("A - Thumbs", true),
                Answer("B - Fingers", false),
                Answer("C - Palms", false),
                Answer("D - Elbows", false)
            )),
            Pair(Pair("Which movement does using a trackball mouse help reduce?", ""), listOf(
                Answer("A - Arm", false),
                Answer("B - Finger", false),
                Answer("C - Wrist", true),
                Answer("D - Shoulder", false)
            )),
            Pair(Pair("If connected to a laptop, what can alternative mice sometimes cause?", ""), listOf(
                Answer("A - Increased disk usage", false),
                Answer("B - There are no adverse effects", false),
                Answer("C - Reduced laptop performance", false),
                Answer("D - Excessive battery drain", true)
            )),
            Pair(Pair("What can be configured on most alternative mice?", ""), listOf(
                Answer("A - Colour", false),
                Answer("B - Sensitivity", true),
                Answer("C - The type of joystick", false),
                Answer("D - Size", false)
            ))
        ),
        listOf("Alternative Mice")
    )


    private val aacDevicesQuiz: QuizQuestionBank = QuizQuestionBank(
        listOf<Pair<Pair<String, String>, List<Answer>>>(
            Pair(Pair("Which is not a type of AAC device?", ""), listOf(
                Answer("A - High-Tech", false),
                Answer("B - No-Tech", false),
                Answer("C - Lite-Tech", false),
                Answer("D - Medium-Tech", true)
            )),
            Pair(Pair("Which is a limitation of AAC devices?", ""), listOf(
                Answer("A - Can be time-consuming", true),
                Answer("B - Can only record a single message", false),
                Answer("C - Are not wearable", false),
                Answer("D - Cannot learn a user’s speech", false)
            )),
            Pair(Pair("What are Progressive Communicators?", ""), listOf(
                Answer("A - Gradually increases the speed of the speech", false),
                Answer("B - Can be worn by the user", false),
                Answer("C - Learns a user’s speech over time", true),
                Answer("D - Specially designed for the classroom environment", false)
            )),
            Pair(Pair("What do No-Tech AAC devices consist of?", ""), listOf(
                Answer("A - One recorded message", false),
                Answer("B - Symbols on a board", true),
                Answer("C - Real time speech synthesis", false),
                Answer("D - Two recorded messages", false)
            )),
            Pair(Pair("Which type of AAC device is good for developing language skills?", ""), listOf(
                Answer("A - Two Message Communicators", false),
                Answer("B - Wearable Communicators", false),
                Answer("C - One Message Communicators", false),
                Answer("D - Progressive Communicators", true)
            ))
        ),
        listOf("Augmentative and Alternative Communication (AAC) Devices")
    )


    private val ecsQuiz: QuizQuestionBank = QuizQuestionBank(
        listOf<Pair<Pair<String, String>, List<Answer>>>(
            Pair(Pair("For Ultrasound Environmental Control Systems to function, where does the input device and control box need to be located?", ""), listOf(
                Answer("A - Anywhere in the same building", false),
                Answer("B - On the same wireless network", false),
                Answer("C - In the same room", true),
                Answer("D - In adjacent rooms", false)
            )),
            Pair(Pair("Which type of Environmental Control Systems are inexpensive and easy to install?", ""), listOf(
                Answer("A - AC Power", true),
                Answer("B - Radio Control", false),
                Answer("C - Ultrasound", false),
                Answer("D - Infrared", false)
            )),
            Pair(Pair("How many signals are used in an Infrared Environmental Control System?", ""), listOf(
                Answer("A - One", false),
                Answer("B - Two", true),
                Answer("C - Three", false),
                Answer("D - Four", false)
            )),
            Pair(Pair("Which is a category of Environmental Control Systems?", ""), listOf(
                Answer("A - Light-based", false),
                Answer("B - Sound-based", false),
                Answer("C - Electronics-based", false),
                Answer("D - Computer-based", true)
            )),
            Pair(Pair("Which is the main benefit of Environmental Control Systems?", ""), listOf(
                Answer("A - Improves independence", true),
                Answer("B - Reduces energy consumption", false),
                Answer("C - Specially designed for schools", false),
                Answer("D - Operates on battery power", false)
            ))
        ),
        listOf("Environmental Control Systems")
    )



    private val etQuiz: QuizQuestionBank = QuizQuestionBank(
        listOf<Pair<Pair<String, String>, List<Answer>>>(
            Pair(Pair("What are eye movements converted into by eye tracking?", ""), listOf(
                Answer("A - Mouse movements and commands", true),
                Answer("B - Keyboard presses", false),
                Answer("C - Screen scrolling", false),
                Answer("D - Switching on the computer", false)
            )),
            Pair(Pair("Which condition is less suitable for eye tracking?", ""), listOf(
                Answer("A - Cerebral Palsy", false),
                Answer("B - Learning disabilities", true),
                Answer("C - Muscular Dystrophy", false),
                Answer("D - Motor Neurone Disease", false)
            )),
            Pair(Pair("What can counteract involuntary head movements in eye tracking tablet computers?", ""), listOf(
                Answer("A - Gyroscope", false),
                Answer("B - Step sensor", false),
                Answer("C - Shake detector", true),
                Answer("D - Camera", false)
            )),
            Pair(Pair("What does not affect the eye tracking’s ability to detect eye movements?", ""), listOf(
                Answer("A - Glasses", false),
                Answer("B - Contact lenses", false),
                Answer("C - Pupil colour", false),
                Answer("D - Background objects", true)
            )),
            Pair(Pair("What can repeat the words or sentences that are input by the user?", ""), listOf(
                Answer("A - Synthesised voice", true),
                Answer("B - Camera", false),
                Answer("C - Computer", false),
                Answer("D - Light sensor", false)
            ))
        ),
        listOf("Eye Tracking")
    )




    private val screenReadersQuiz: QuizQuestionBank = QuizQuestionBank(
        listOf<Pair<Pair<String, String>, List<Answer>>>(
            Pair(Pair("What does the choice or screen reader not depend on?", ""), listOf(
                Answer("A - Computer type", false),
                Answer("B - Mobile phone type", false),
                Answer("C - Web browser", false),
                Answer("D - Age of user", true)
            )),
            Pair(Pair("What do some screen readers also consist of?", ""), listOf(
                Answer("A - Braille display", true),
                Answer("B - Battery", false),
                Answer("C - Microphone", false),
                Answer("D - Camera", false)
            )),
            Pair(Pair("Select two ways which screen readers can be customised to suit the user’s abilities", ""), listOf(
                Answer("A - Decreasing the speech speed", true),
                Answer("B - Decreasing the display brightness", false),
                Answer("C - Changing the language", true),
                Answer("D - Changing shortcut commands", false)
            )),
            Pair(Pair("What can be a limitation of screen readers?", ""), listOf(
                Answer("A - There are no short cut commands set up", false),
                Answer("B - Users cannot see the spelling of words", true),
                Answer("C - There are not compatible with social networking", false),
                Answer("D - They cannot be used to play games", false)
            )),
            Pair(Pair("Which is not a built-in screen reader?", ""), listOf(
                Answer("A - TalkBack", false),
                Answer("B - VoiceOver", false),
                Answer("C - JAWS", false),
                Answer("D - VoiceShortcuts", true)
            ))
        ),
        listOf("Screen Readers")
    )

    private val tabletQuiz: QuizQuestionBank = QuizQuestionBank(
        listOf<Pair<Pair<String, String>, List<Answer>>>(
            Pair(Pair("Select the two most popular mobile operating systems", ""), listOf(
                Answer("A - iOS", true),
                Answer("B - Android", true),
                Answer("C - Windows Phone", false),
                Answer("D - HTC", false)
            )),
            Pair(Pair("What can be a limitation of tablet computers?", ""), listOf(
                Answer("A - Less storage space", true),
                Answer("B - Higher costs", false),
                Answer("C - Less portable", false),
                Answer("D - No camera function", false)
            )),
            Pair(Pair("What can reduce the size of the visible page on the tablet computer?", ""), listOf(
                Answer("A - On-screen keyboard", true),
                Answer("B - Camera", false),
                Answer("C - Tablet cover", false),
                Answer("D - Accessibility tools", false)
            )),
            Pair(Pair("What can pupils with learning disabilities prefer with tablet computers?", ""), listOf(
                Answer("A - Camera", false),
                Answer("B - Touchscreen", true),
                Answer("C - Onscreen keyboard", false),
                Answer("D - Large fonts", false)
            )),
            Pair(Pair("How can further tools be added onto tablet computers?", ""), listOf(
                Answer("A - This is not possible", false),
                Answer("B - From a memory stick", false),
                Answer("C - From an app store", true),
                Answer("D - From another tablet computer", false)
            ))
        ),
        listOf("Tablet Computers")
    )

    private val androidQuiz: QuizQuestionBank = QuizQuestionBank(
        listOf<Pair<Pair<String, String>, List<Answer>>>(
            Pair(Pair("Select two interaction controls for Android", ""), listOf(
                Answer("A - Watch out", false),
                Answer("B - Lookout", true),
                Answer("C - Action Blocks", true),
                Answer("D - Access control", false)
            )),
            Pair(Pair("What does the TalkBack screen reader allow the device to be controlled by?", ""), listOf(
                Answer("A - Touch", true),
                Answer("B - Joystick", false),
                Answer("C - Eye movements", false),
                Answer("D - Head movements", false)
            )),
            Pair(Pair("What can function be used to filter sounds in the surrounding environment?", ""), listOf(
                Answer("A - Live Captions", false),
                Answer("B - Live Transcribe", false),
                Answer("C - Sound Amplifier", true),
                Answer("D - Microphone", false)
            )),
            Pair(Pair("What is a limitation of the TalkBack Braille keyboard?", ""), listOf(
                Answer("A - It is not compatible with tablets", false),
                Answer("B - It is not a standard feature of Android and has to be purchased", false),
                Answer("C - It cannot be used with other accessibility features", false),
                Answer("D - Only Unified English Braille is supported", true)
            )),
            Pair(Pair("Select two benefits of Android accessibility features", ""), listOf(
                Answer("A - Suitable for users with limited concentration", false),
                Answer("B - Increases the functionality of the device", true),
                Answer("C - Makes applications easier to use", true),
                Answer("D - Increases the performance of the device", false)
            ))
        ),
        listOf("Android Accessibility Features")
    )



    private val curriculumQuiz: QuizQuestionBank = QuizQuestionBank(
        listOf<Pair<Pair<String, String>, List<Answer>>>(
            Pair(Pair("Select two people who can use curriculum software", ""), listOf(
                Answer("A - Speech and Language Therapists", true),
                Answer("B - Physiotherapists", false),
                Answer("C - Teachers", true),
                Answer("D - Administration staff", false)
            )),
            Pair(Pair("As well as mathematics what can numeracy software be used for?", ""), listOf(
                Answer("A - Languages", false),
                Answer("B - Science", true),
                Answer("C - Religious Education", false),
                Answer("D - History", false)
            )),
            Pair(Pair("Which conditions may not benefit from curriculum software?", ""), listOf(
                Answer("A - Dyslexia", false),
                Answer("B - Dyspraxia", false),
                Answer("C - Attention Deficit Hyperactivity Disorder", false),
                Answer("D - Multiple Sclerosis", true)
            )),
            Pair(Pair("By what percentage can visual illustrations improve the learning process?", ""), listOf(
                Answer("A - 100%", false),
                Answer("B - 200%", false),
                Answer("C - 300%", false),
                Answer("D - 400%", true)
            )),
            Pair(Pair("Which accessibility feature is commonly compatible with curriculum software?", ""), listOf(
                Answer("A - Switch Access", true),
                Answer("B - Access Control", false),
                Answer("C - Touchscreen", false),
                Answer("D - Mouse", false)
            ))
        ),
        listOf("Curriculum Software")
    )



    private val dyslexiaQuiz: QuizQuestionBank = QuizQuestionBank(
        listOf<Pair<Pair<String, String>, List<Answer>>>(
            Pair(Pair("What is a disadvantage of speech detection?", ""), listOf(
                Answer("A - It is costly to install", false),
                Answer("B - It will never be 100% accurate", true),
                Answer("C - It cannot be used to write emails", false),
                Answer("D - Corrections cannot be made to the dictated text", false)
            )),
            Pair(Pair("Which tool is designed specifically for people with dyslexia?", ""), listOf(
                Answer("A - Speech recognition software", false),
                Answer("B - Hand reading pens", false),
                Answer("C - Mind mapping software", true),
                Answer("D - Text to Speech software", false)
            )),
            Pair(Pair("What can computer based learning programs help people with dyslexia to increase?", ""), listOf(
                Answer("A - Numeracy", true),
                Answer("B - Speech clarity", false),
                Answer("C - Communication", false),
                Answer("D - Memory", false)
            )),
            Pair(Pair("What can a smartpen upload text to?", ""), listOf(
                Answer("A - The text is only stored on the smart pen", false),
                Answer("B - Tablet computers", true),
                Answer("C - Another smartphone", false),
                Answer("D - Speech recognition software", false)
            )),
            Pair(Pair("Which two tools can allow text to be read aloud?", ""), listOf(
                Answer("A - Text to Speech software", true),
                Answer("B - Scanning software", true),
                Answer("C - Smartpens", false),
                Answer("D - Mind mapping software", false)
            ))
        ),
        listOf("Dyslexia Software")
    )




    private val iOSQuiz: QuizQuestionBank = QuizQuestionBank(
        listOf<Pair<Pair<String, String>, List<Answer>>>(
            Pair(Pair("What does VoiceOver provide?", ""), listOf(
                Answer("A - Speech detection", false),
                Answer("B - Audible descriptions", true),
                Answer("C - Compatibility with switches", false),
                Answer("D - Voice control", false)
            )),
            Pair(Pair("What do voice control commands not activate?", ""), listOf(
                Answer("A - Power button", true),
                Answer("B - Performing gestures", false),
                Answer("C - Dictating text", false),
                Answer("D - Editing text", false)
            )),
            Pair(Pair("Select two functions that switch control can perform", ""), listOf(
                Answer("A - Selection", true),
                Answer("B - Drawing freehand", true),
                Answer("C - Controlling Android devices", false),
                Answer("D - Switching on the device", false)
            )),
            Pair(Pair("How can VoiceOver be customised to suit User's needs?", ""), listOf(
                Answer("A - Adjust speaking pitch", true),
                Answer("B - Change language", false),
                Answer("C - Change display size", false),
                Answer("D - Provide subtitles", false)
            )),
            Pair(Pair("If a user would like to magnify part of the screen, which feature would be used?", ""), listOf(
                Answer("A - Partial Zoom", false),
                Answer("B - Custom Zoom", false),
                Answer("C - Window Zoom", true),
                Answer("D - Full Screen Zoom", false)
            ))
        ),
        listOf("iOS Accessibility Features")
    )

    private val KTSSQuiz: QuizQuestionBank = QuizQuestionBank(
        listOf<Pair<Pair<String, String>, List<Answer>>>(
            Pair(Pair("What is a limitation of typing skills software?", ""), listOf(
                Answer("A - Some people can only type with one hand", true),
                Answer("B - Does not improve posture", false),
                Answer("C - Decreases typing speed", false),
                Answer("D - Not suitable for children", false)
            )),
            Pair(Pair("Which part of the brain is associated with learning to touch type?", ""), listOf(
                Answer("A - Learning skills centre", false),
                Answer("B - Physical skills centre", true),
                Answer("C - Brain skills centre", false),
                Answer("D - Typing skills centre", false)
            )),
            Pair(Pair("Select two features that are commonly found in typing skills software", ""), listOf(
                Answer("A - Memory tests", false),
                Answer("B - Onscreen keyboard", false),
                Answer("C - Typing games", true),
                Answer("D - Video tutorials", true)
            )),
            Pair(Pair("Which condition may be less suitable for touch typing?", ""), listOf(
                Answer("A - Cerebral Palsy", true),
                Answer("B - Dyslexia", false),
                Answer("C - Dyspraxia", false),
                Answer("D - Autism", false)
            )),
            Pair(Pair("Which is not an advantage of learning typing skills?", ""), listOf(
                Answer("A - Typing faster", false),
                Answer("B - Typing more accurately", false),
                Answer("C - The brain can concentrate on the quality of the writing content", false),
                Answer("D - Improved curriculum knowledge", true)
            ))
        ),
        listOf("Keyboard/Typing Skills Software")
    )


    private val visualQuiz: QuizQuestionBank = QuizQuestionBank(
        listOf<Pair<Pair<String, String>, List<Answer>>>(
            Pair(Pair("How can intelligent personal assistants benefit people with visual impairments?", ""), listOf(
                Answer("A - Provide speech dictation", false),
                Answer("B - Perform certain tasks on the device", true),
                Answer("C - Increase the screen size", false),
                Answer("D - Change the language of written text", false)
            )),
            Pair(Pair("What is a limitation of visual impairment software?", ""), listOf(
                Answer("A - Not compatible with most applications", false),
                Answer("B - Decreases academic performance", false),
                Answer("C - Reduces confidence", false),
                Answer("D - Often has a high cost", true)
            )),
            Pair(Pair("Select two intelligent personal assistants", ""), listOf(
                Answer("A - Android Assistant", false),
                Answer("B - Siri", true),
                Answer("C - VoiceOver", false),
                Answer("D - Google Assistant", true)
            )),
            Pair(Pair("What can be a usability issue with visual impairment software?", ""), listOf(
                Answer("A - High cost", false),
                Answer("B - Lack of spoken feedback", true),
                Answer("C - No keyboard commands", false),
                Answer("D - No gesture recognition", false)
            )),
            Pair(Pair("Select two ways that a device can be made easier to use for people with visual impairments", ""), listOf(
                Answer("A - Changing the colour scheme", true),
                Answer("B - Using a different operating system", false),
                Answer("C - Having a device with a larger screen", true),
                Answer("D - Disabling the intelligent personal assistant", false)
            ))
        ),
        listOf("Visual Impairment Software")
    )

    private val windowsQuiz: QuizQuestionBank = QuizQuestionBank(
        listOf<Pair<Pair<String, String>, List<Answer>>>(
            Pair(Pair("Which two keys can be controlled by Sticky Keys?", ""), listOf(
                Answer("A - Shift", true),
                Answer("B - Control", true),
                Answer("C - Delete", false),
                Answer("D - Caps Lock", false)
            )),
            Pair(Pair("If a user wants repeated key strokes to be ignored, which accessibility feature should be used?", ""), listOf(
                Answer("A - Sticky Keys", false),
                Answer("B - Toggle Keys", false),
                Answer("C - Filter Keys", true),
                Answer("D - Immersive Reader", false)
            )),
            Pair(Pair("What can Microsoft Office Lens App not capture information from?", ""), listOf(
                Answer("A - Handwritten memos", false),
                Answer("B - Whiteboards", false),
                Answer("C - Pages from a book", false),
                Answer("D - Voice recordings", true)
            )),
            Pair(Pair("What is the resource where Windows Accessibility Tools are listed?", ""), listOf(
                Answer("A - Microsoft Accessibility centre", false),
                Answer("B - Microsoft Educator Centre", true),
                Answer("C - Microsoft Office", false),
                Answer("D - Contacting Customer Support", false)
            )),
            Pair(Pair("Which accessibility tool is provided as standard in Windows?", ""), listOf(
                Answer("A - Only keyboard accessibility features", false),
                Answer("B - All accessibility tools", true),
                Answer("C - Speech Detect", false),
                Answer("D - Immersive Reader", false)
            ))
        ),
        listOf("Windows Accessibility Features")
    )

    private val quizzes: HashMap<String, List<Question>> = hashMapOf(
        "Switches" to switchesQuiz.quizQuestionBank,
        "Alternative Keyboards" to altKeyboardQuiz.quizQuestionBank,
        "Alternative Mice" to altMiceQuiz.quizQuestionBank,
        "Augmentative and Alternative Communication (AAC) Devices" to aacDevicesQuiz.quizQuestionBank,
        "Environmental Control Systems" to ecsQuiz.quizQuestionBank,
        "Eye Tracking" to etQuiz.quizQuestionBank,
        "Screen Readers" to screenReadersQuiz.quizQuestionBank,
        "Tablet Computers" to tabletQuiz.quizQuestionBank,
        "Android Accessibility Features" to androidQuiz.quizQuestionBank,
        "Curriculum Software" to curriculumQuiz.quizQuestionBank,
        "Dyslexia Software" to dyslexiaQuiz.quizQuestionBank,
        "iOS Accessibility Features" to iOSQuiz.quizQuestionBank,
        "Keyboard/Typing Skills Software" to KTSSQuiz.quizQuestionBank,
        "Visual Impairment Software" to visualQuiz.quizQuestionBank,
        "Windows Accessibility Features" to windowsQuiz.quizQuestionBank
    )

    fun prepareQuiz(name: String): List<Question> {
        return quizzes[name]?.let { questions ->
            if (questions.isEmpty()) {
                throw IllegalStateException("Quiz '$name' has no questions")
            }
            questions
        } ?: throw NoSuchFieldException("Quiz '$name' does not exist")
    }
}
