package uk.ac.bournemouthuniversity.at4send.fragments;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.afollestad.materialdialogs.MaterialDialog;

import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.BaseFragment;
import uk.ac.bournemouthuniversity.at4send.admin.AdminHomeActivity;
import uk.ac.bournemouthuniversity.at4send.extensions.utils.Network;
import uk.ac.bournemouthuniversity.at4send.recommendations.AbilityBasedAssessmentActivity;
import uk.ac.bournemouthuniversity.at4send.recommendations.ConditionBaseAssessmentActivity;
// import uk.ac.bournemouthuniversity.at4send.recommendations.NewAssessmentActivity;

public class HomeFragment extends BaseFragment {

    public HomeFragment() {
        // Required empty public constructor
    }

    public static HomeFragment newInstance() {
        return new HomeFragment();
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View view = inflater.inflate(R.layout.fragment_home, container, false);

        view.findViewById(R.id.administrationMenu).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                administrationMenu();
            }
        });

        view.findViewById(R.id.abilityBasedAssessment).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                abilityBasedAssessment();
            }
        });

        view.findViewById(R.id.conditionBasedAssessment).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                conditionBasedAssessment();
            }
        });

        return view;
    }

    // Remove these methods as they're no longer needed
    /*
    private void newAssessment() {
        if(!Network.isNetworkAvailable(requireActivity())){
            new MaterialDialog(requireContext(), MaterialDialog.getDEFAULT_BEHAVIOR())
                    .title(null, "Network Access Required")
                    .message(null, "An active network connection is required for this function. Please connect to the internet and try again.", null)
                    .cancelable(false)
                    .positiveButton(null, "Dismiss", materialDialog -> {
                        materialDialog.dismiss();
                        requireActivity().finish();
                        return null;
                    }).show();
            return;
        }
        Intent i = new Intent(requireActivity(), NewAssessmentActivity.class);
        startActivity(i);
    }
    */


    private void administrationMenu() {
        if(!Network.isNetworkAvailable(requireActivity())){
            new MaterialDialog(requireContext(), MaterialDialog.getDEFAULT_BEHAVIOR())
                    .title(null, "Network Access Required")
                    .message(null, "An active network connection is required for this function. Please connect to the internet and try again.", null)
                    .cancelable(false)
                    .positiveButton(null, "Dismiss", materialDialog -> {
                        materialDialog.dismiss();
                        requireActivity().finish();
                        return null;
                    }).show();
            return;
        }

        Intent i = new Intent(requireActivity(), AdminHomeActivity.class);
        startActivity(i);
    }

    private void abilityBasedAssessment() {
        if(!Network.isNetworkAvailable(requireActivity())){
            new MaterialDialog(requireContext(), MaterialDialog.getDEFAULT_BEHAVIOR())
                    .title(null, "Network Access Required")
                    .message(null, "An active network connection is required for this function. Please connect to the internet and try again.", null)
                    .cancelable(false)
                    .positiveButton(null, "Dismiss", materialDialog -> {
                        materialDialog.dismiss();
                        requireActivity().finish();
                        return null;
                    }).show();
            return;
        }
        Intent i = new Intent(requireActivity(), AbilityBasedAssessmentActivity.class);
        startActivity(i);
    }

    private void conditionBasedAssessment() {
        if(!Network.isNetworkAvailable(requireActivity())){
            new MaterialDialog(requireContext(), MaterialDialog.getDEFAULT_BEHAVIOR())
                    .title(null, "Network Access Required")
                    .message(null, "An active network connection is required for this function. Please connect to the internet and try again.", null)
                    .cancelable(false)
                    .positiveButton(null, "Dismiss", materialDialog -> {
                        materialDialog.dismiss();
                        requireActivity().finish();
                        return null;
                    }).show();
            return;
        }
        Intent i = new Intent(requireActivity(), ConditionBaseAssessmentActivity.class);
        startActivity(i);
    }
}
