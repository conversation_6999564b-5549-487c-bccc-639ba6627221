<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mainView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/md_grey_50"
    tools:context=".technologies.BrowseAssistiveTechnologiesActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:elevation="0dp">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            app:contentInsetStartWithNavigation="0dp"
            app:titleTextAppearance="@style/AT4SEND.AppTheme.TextAppearance.Title"
            app:title="Browse Assistive Technologies"
            app:theme="@style/AT4SEND.BlueTheme.NoActionBar.Toolbar">

        </androidx.appcompat.widget.Toolbar>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:fillViewport="true"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/bottomView">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardBackgroundColor="@color/md_white_1000"
                    app:cardCornerRadius="0dp"
                    app:strokeWidth="1dp"
                    app:strokeColor="@color/md_grey_200"
                    app:cardElevation="0dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:gravity="center_vertical"
                            android:paddingStart="16dp"
                            android:paddingEnd="16dp"
                            android:paddingTop="8dp"
                            android:paddingBottom="8dp">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="To browse for Assistive Technologies, enter and select a manufacturer and/or product below:"
                                android:id="@+id/browse_instructions"
                                android:textSize="16sp"/>
                        </LinearLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="@color/md_grey_200"/>

                        <com.gabrielepmattia.materialfields.fields.FieldGeneric
                            android:id="@+id/manufacturerSelection"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:title="Manufacturer"
                            app:placeholder="Select Manufacturer"
                            app:drawable="@drawable/ic_large_dropdown"
                            android:background="?attr/selectableItemBackground"
                            android:clickable="true"
                            android:focusable="true"
                            android:padding="16dp"/>

                        <com.gabrielepmattia.materialfields.fields.FieldGeneric
                            android:id="@+id/technologySelection"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:title="Product"
                            app:placeholder="Select Product"
                            app:drawable="@drawable/ic_large_dropdown"
                            android:background="?attr/selectableItemBackground"
                            android:clickable="true"
                            android:focusable="true"
                            android:padding="8dp"/>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:gravity="center_horizontal"
                            android:paddingStart="16dp"
                            android:paddingEnd="16dp"
                            android:paddingTop="12dp"
                            android:paddingBottom="12dp">

                            <com.google.android.material.button.MaterialButton
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:letterSpacing="0"
                                android:textStyle="bold"
                                style="@style/Widget.MaterialComponents.Button.UnelevatedButton"
                                android:text="Search ASSISTIVE TECHNOLOGIES"
                                android:onClick="searchAssistiveTechnologies"/>
                        </LinearLayout>
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:cardBackgroundColor="@color/md_white_1000"
                    app:cardCornerRadius="0dp"
                    app:strokeWidth="1dp"
                    app:strokeColor="@color/md_grey_200"
                    app:cardElevation="0dp"
                    android:layout_marginTop="20dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:gravity="center_vertical"
                            android:paddingStart="16dp"
                            android:paddingEnd="16dp"
                            android:paddingTop="12dp"
                            android:paddingBottom="12dp">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Alternatively, to view all available Assistive Technologies click the button below:"
                                android:id="@+id/browse_all_instructions"
                                android:textSize="16sp"/>

                            <com.google.android.material.button.MaterialButton
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:letterSpacing="0"
                                android:textStyle="bold"
                                android:layout_marginTop="4dp"
                                style="@style/Widget.MaterialComponents.Button.UnelevatedButton"
                                android:text="BROWSE All Assistive Technologies"
                                android:onClick="listAssistiveTechnologies"/>


                        </LinearLayout>


                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>



            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/bottomView"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardBackgroundColor="@color/md_white_1000"
            app:cardCornerRadius="0dp"
            app:strokeWidth="1dp"
            app:strokeColor="@color/md_grey_200"
            app:cardElevation="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center_vertical"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingTop="12dp"
                android:paddingBottom="12dp">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/returnHome"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:letterSpacing="0"
                    style="@style/Widget.MaterialComponents.Button.UnelevatedButton"
                    android:text="RETURN TO ASSESSMENT MENU"
                    android:onClick="returnHome"
                    app:backgroundTint="@color/md_orange_500"/>

            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
