package uk.ac.bournemouthuniversity.at4send.technologies;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.ColorStateList;
import android.graphics.drawable.Drawable;
import android.graphics.pdf.PdfDocument;
import android.net.Uri;
import android.os.Bundle;
import android.print.PrintAttributes;
import android.print.pdf.PrintedPdfDocument;
import android.provider.Settings;
import android.text.Html;
import android.text.InputType;
import android.util.Log;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AlertDialog;
import uk.ac.bournemouthuniversity.at4send.BaseActivity;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.core.content.FileProvider;
import androidx.core.content.res.ResourcesCompat;
import androidx.core.text.HtmlCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.afollestad.materialdialogs.MaterialDialog;
import com.afollestad.materialdialogs.input.DialogInputExtKt;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.snackbar.Snackbar;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.karumi.dexter.Dexter;
import com.karumi.dexter.MultiplePermissionsReport;
import com.karumi.dexter.PermissionToken;
import com.karumi.dexter.listener.PermissionRequest;
import com.karumi.dexter.listener.multi.MultiplePermissionsListener;

import org.jetbrains.annotations.NotNull;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Locale;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import uk.ac.bournemouthuniversity.at4send.HomeActivity;
import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.adapters.AssistiveTechnologyAdapter;
import uk.ac.bournemouthuniversity.at4send.admin.EditAssistiveTechnologyActivity;
import uk.ac.bournemouthuniversity.at4send.api.AT4SENDAPIClient;
import uk.ac.bournemouthuniversity.at4send.api.AT4SENDAPIInterface;
import uk.ac.bournemouthuniversity.at4send.extensions.helpers.SnackbarHelper;
import uk.ac.bournemouthuniversity.at4send.extensions.ui.Dialogs;
import uk.ac.bournemouthuniversity.at4send.extensions.utils.ArrayListAnySize;
import uk.ac.bournemouthuniversity.at4send.extensions.utils.Network;
import uk.ac.bournemouthuniversity.at4send.models.AssistiveTechnology;
import uk.ac.bournemouthuniversity.at4send.models.response.StatusResponse;
import uk.ac.bournemouthuniversity.at4send.models.response.TechnologiesResponse;
import uk.ac.bournemouthuniversity.at4send.utils.ContrastUtils;
import uk.ac.bournemouthuniversity.at4send.utils.ReadAloudUtils;
import uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils;

import androidx.activity.OnBackPressedCallback;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import android.transition.Slide;
import android.app.Activity;
import android.widget.TextView;

public class AssistiveTechnologyListActivity extends BaseActivity {
    private static final String TAG = "AssistiveTechnologies";
    private static final String SAVED_LIST_POSITION = "saved_list_position";
    private int savedListPosition = 0;
    private boolean isReturningFromDetail = false;  // Add this flag

    public static final int ACTION_VIEW = 1;
    public static final int ACTION_EDIT = 3;
    public static final int ACTION_DELETE = 4;

    public static final String EXTRA_ACTION_TYPE = "actionType";

    public static final String EXTRA_MANUFACTURER_ID = "manufacturerID";
    public static final String EXTRA_MANUFACTURER_NAME = "manufacturerName";

    public static final String EXTRA_CONDITION_IDS = "conditionID";
    public static final String EXTRA_CONDITION_NAME = "conditionName";

    public static final String EXTRA_ABILITY_IDS = "abilityIDs";
    public static final String EXTRA_ABILITY_RATINGS = "abilityRatings";

    private int mActionType = ACTION_VIEW;

    private List<Integer> mAbilityIDs = new ArrayList<>();
    private List<Integer> mAbilityRatings = new ArrayList<>();

    private ArrayList<Integer> mConditionIDs = new ArrayList<>();
    private String mConditionName  = "";
    private int mManufacturerID = 0;
    private String mManufacturerName  = "";

    private List<AssistiveTechnology> technologyList = new ArrayListAnySize<>();
    private AssistiveTechnologyAdapter mAdapter;
    private RecyclerView mMainRecyclerView;

    private LinearLayout mNoResultsView;
    private LinearLayout mLoaderView;
    private MaterialCardView mResultsCardView;
    private AppCompatTextView mResultsExplanatoryText, mResultsExplanatoryHeading;

    private boolean saveExportEnabled = false;

    private FirebaseCrashlytics crashlytics = FirebaseCrashlytics.getInstance();

    private final ActivityResultLauncher<Intent> activityLauncher = registerForActivityResult(
        new ActivityResultContracts.StartActivityForResult(),
        result -> {
            if (result.getResultCode() == Activity.RESULT_OK && result.getData() != null) {
                // Handle your result here
            }
        }
    );

    private final ActivityResultLauncher<Intent> settingsLauncher = registerForActivityResult(
        new ActivityResultContracts.StartActivityForResult(),
        result -> {
            // Handle the result here
        }
    );

    @SuppressLint("MissingInflatedId")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_assistive_technology_list);
        
        Bundle extras = getIntent().getExtras();

        if(extras != null){
            mManufacturerID = extras.getInt(EXTRA_MANUFACTURER_ID);
            mManufacturerName = extras.getString(EXTRA_MANUFACTURER_NAME);

            if(extras.getIntegerArrayList(EXTRA_CONDITION_IDS) != null) {
                mConditionIDs = extras.getIntegerArrayList(EXTRA_CONDITION_IDS);
                mConditionName = extras.getString(EXTRA_CONDITION_NAME);

                saveExportEnabled = true;
                invalidateOptionsMenu();
            }

            if(extras.getIntegerArrayList(EXTRA_ABILITY_IDS) != null && extras.getIntegerArrayList(EXTRA_ABILITY_RATINGS) != null){
                mAbilityIDs = extras.getIntegerArrayList(EXTRA_ABILITY_IDS);
                mAbilityRatings = extras.getIntegerArrayList(EXTRA_ABILITY_RATINGS);

                saveExportEnabled = true;
                invalidateOptionsMenu();
            }

            mActionType = extras.getInt(EXTRA_ACTION_TYPE, ACTION_VIEW);
        }

        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);

        ActionBar ab = getSupportActionBar();

        if(ab != null){
            ab.setDisplayHomeAsUpEnabled(true);
        }

        CoordinatorLayout mainView = findViewById(R.id.mainView);

        // Add back button callback
        getOnBackPressedDispatcher().addCallback(this, new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                finish();
            }
        });

        mAdapter = new AssistiveTechnologyAdapter(this, technologyList, item -> {
            switch (mActionType) {
                case ACTION_VIEW:
                    // Save position before starting new activity
                    LinearLayoutManager layoutManager = (LinearLayoutManager) mMainRecyclerView.getLayoutManager();
                    savedListPosition = layoutManager.findFirstVisibleItemPosition();
                    isReturningFromDetail = true;  // Set flag before leaving
                    
                    Intent i = new Intent(this, ViewAssistiveTechnologyActivity.class);
                    i.putExtra(ViewAssistiveTechnologyActivity.EXTRA_TECHNOLOGY_ID, item.getTechnologyID());
                    startActivity(i);
                    break;
                case ACTION_EDIT:
                    Intent i1 = new Intent(this, EditAssistiveTechnologyActivity.class);
                    i1.putExtra(EditAssistiveTechnologyActivity.EXTRA_TECHNOLOGY_ID, item.getTechnologyID());
                    startActivity(i1);
                    break;
                case ACTION_DELETE:
                    break;
            }
        }, (mActionType == ACTION_DELETE) ? AssistiveTechnologyAdapter.DISPLAY_SELECTION : AssistiveTechnologyAdapter.DISPLAY_LIST);

        mMainRecyclerView = findViewById(R.id.mainRecyclerView);
        mMainRecyclerView.setAdapter(mAdapter);
        mMainRecyclerView.setLayoutManager(new LinearLayoutManager(this, RecyclerView.VERTICAL, false));

        mLoaderView = findViewById(R.id.loader);
        mNoResultsView = findViewById(R.id.noResults);
        mResultsCardView = findViewById(R.id.results);
        mResultsExplanatoryText = findViewById(R.id.listExplanatoryText);
        mResultsExplanatoryHeading = findViewById(R.id.listExplanatoryTextHeading);

        MaterialButton returnHome = findViewById(R.id.returnHome);
        MaterialButton deleteButton = findViewById(R.id.deleteTechnologies);

        deleteButton.setOnClickListener(v -> {
            List<Integer> toDelete = new ArrayList<>();
            for (int i = 0; i < mMainRecyclerView.getChildCount(); i++) {
                AssistiveTechnologyAdapter.ViewHolder holder = (AssistiveTechnologyAdapter.ViewHolder) mMainRecyclerView.findViewHolderForAdapterPosition(i);
                if(holder != null) {
                    if (holder.selected) {
                        toDelete.add(technologyList.get(i).getTechnologyID());
                    }
                }
            }

            if(toDelete.size() != 0) {
                new MaterialDialog(AssistiveTechnologyListActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                        .message(R.string.delete_assistive_technology_warning, null, null)
                        .positiveButton(null, "Yes, Delete", materialDialog -> {
                            materialDialog.dismiss();
                            deleteTechnologies(toDelete);
                            return null;
                        })
                        .negativeButton(null, "No, Cancel", materialDialog -> {
                            materialDialog.dismiss();
                            return null;
                        }).show();
            }else{
                Snackbar sB = Snackbar.make(mainView, "Please select an Assistive Technology to delete.", Snackbar.LENGTH_SHORT);
                SnackbarHelper.configSnackbar(this, sB);
                sB.show();
            }
        });



        if((mActionType == ACTION_DELETE || mActionType == ACTION_EDIT) && ab != null){
            switch (mActionType){
                case ACTION_DELETE:
                    ab.setTitle("Delete Assistive Technologies");
                    break;
                case ACTION_EDIT:
                    ab.setTitle("Edit Assistive Technologies");
                    break;
            }
        }

        returnHome.setOnClickListener(v -> {
            Intent i = new Intent(this, HomeActivity.class);
            i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK|Intent.FLAG_ACTIVITY_CLEAR_TASK);
            startActivity(i);
            finish();
        });


        if(mActionType == ACTION_DELETE){
            mResultsExplanatoryHeading.setText("Below is a list of all Assistive Technologies in the EduAbility database. Select Assistive Technology(s) to Delete:");
            deleteButton.setVisibility(View.VISIBLE);
            returnHome.setText("RETURN TO ADMINISTRATION MENU");
            returnHome.setOnClickListener(v -> finish());
        }else if(mActionType == ACTION_EDIT){
            mResultsExplanatoryHeading.setText("Below is a list of all Assistive Technologies in the EduAbility database. Select an Assistive Technology to Edit:");
            returnHome.setText("RETURN TO ADMINISTRATION MENU");
            returnHome.setOnClickListener(v -> finish());
        }

        if(saveExportEnabled){
            MaterialButton exportButton = findViewById(R.id.exportTechnologies);
            exportButton.setVisibility(View.VISIBLE);
            exportButton.setOnClickListener(v -> exportRecommendations());
        }

        // Apply high contrast if enabled
        if (ContrastUtils.isHighContrastEnabled(this)) {
            applyHighContrastToResults();
        }

        // Add read-aloud buttons to explanatory texts
        if (mResultsExplanatoryHeading != null) {
            ReadAloudUtils.addReadAloudButton(this, mResultsExplanatoryHeading);
        }

    //    if (mConditionName != "") {
    //         ReadAloudUtils.addReadAloudButton(this, mConditionName);
    //     }
    }

    @Override
    protected void onResume() {
        super.onResume();
        
        if(!Network.isNetworkAvailable(this)){
            new MaterialDialog(AssistiveTechnologyListActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                    .title(null, "Network Access Required")
                    .message(null, "An active network connection is required for this function. Please connect to the internet and try again.", null)
                    .cancelable(false)
                    .positiveButton(null, "Dismiss", materialDialog -> {
                        materialDialog.dismiss();
                        finish();
                        return null;
                    }).show();
            return;
        }

        if (!isReturningFromDetail) {
            // Only populate list if we're not returning from detail view
            populateTechnologiesList();
        } else {
            // Restore position after returning from detail view
            if (mMainRecyclerView != null && savedListPosition > 0) {
                mMainRecyclerView.post(() -> {
                    LinearLayoutManager layoutManager = (LinearLayoutManager) mMainRecyclerView.getLayoutManager();
                    if (layoutManager != null) {
                        layoutManager.scrollToPositionWithOffset(savedListPosition, 0);
                    }
                });
            }
            isReturningFromDetail = false;  // Reset flag
        }

        // Reapply high contrast when returning to activity
        if (ContrastUtils.isHighContrastEnabled(this)) {
            applyHighContrastToResults();
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        MenuInflater inflater = getMenuInflater();
        if(saveExportEnabled) {
            //inflater.inflate(R.menu.menu_save_assistive_technology_recommendation, menu);
            return true;
        }
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        int itemId = item.getItemId();

        if(itemId == android.R.id.home){
            finish();
            return true;
        }else if(itemId == R.id.action_save){
            //exportRecommendations();
            return true;
        }

        return super.onOptionsItemSelected(item);
    }

    private void populateTechnologiesList(){
        MaterialDialog progressDialog = Dialogs.generateProgressDialog(this, "Loading...");
        progressDialog.show();

        mLoaderView.setVisibility(View.VISIBLE);

        technologyList.clear();
        mAdapter.notifyDataSetChanged();

        AT4SENDAPIInterface apiService =
                AT4SENDAPIClient.getClient().create(AT4SENDAPIInterface.class);

        Call<TechnologiesResponse> call;

        if(mConditionIDs.size() != 0){
            call = apiService.getTechnologiesForConditions(true, mConditionIDs);
            mResultsExplanatoryText.setText(
                HtmlCompat.fromHtml("<b>Condition:</b> " + mConditionName, 
                HtmlCompat.FROM_HTML_MODE_COMPACT)
            );
        }else if(mManufacturerID != 0) {
            call = apiService.getTechnologies(true, mManufacturerID);
            mResultsExplanatoryText.setText(
                HtmlCompat.fromHtml("<b>Manufacturer:</b> " + mManufacturerName, 
                HtmlCompat.FROM_HTML_MODE_COMPACT)
            );
        }else if(mAbilityRatings.size() != 0 && mAbilityIDs.size() != 0){
            call = apiService.getTechnologiesForAbilities(true, mAbilityIDs, mAbilityRatings);
            mResultsExplanatoryText.setVisibility(View.GONE);
        }else{
            call = apiService.getTechnologies(true);
            mResultsExplanatoryText.setVisibility(View.GONE);
        }

        call.enqueue(new Callback<TechnologiesResponse>() {
            @Override
            public void onResponse(@NotNull Call<TechnologiesResponse>call, @NotNull Response<TechnologiesResponse> response) {
                if(response.body() != null) {
                    List<AssistiveTechnology> technologies = response.body().getAssistiveTechnologies();

                    if (technologies != null) {
                        Log.d(TAG, "Number of technologies received: " + technologies.size());
                        Log.d(TAG, "Result count: " + response.body().getCount());

                        technologyList.addAll(technologies);
                        mAdapter.notifyDataSetChanged();

                        if (technologyList.size() == 0) {
                            mNoResultsView.setVisibility(View.VISIBLE);
                        } else {
                            mResultsCardView.setVisibility(View.VISIBLE);
                        }

                        mLoaderView.setVisibility(View.GONE);
                    }
                }else{
                    new MaterialDialog(AssistiveTechnologyListActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                            .title(null, "Error Occurred")
                            .message(null, "An error occurred whilst retrieving the technology list. Please try again later.", null)
                            .cancelable(false)
                            .positiveButton(null, "Dismiss", materialDialog -> {
                                materialDialog.dismiss();
                                finish();
                                return null;
                            }).show();
                }

                progressDialog.dismiss();
            }

            @Override
            public void onFailure(@NotNull Call<TechnologiesResponse>call, @NotNull Throwable t) {
                // Log error here since request failed
                Log.e(TAG, t.toString());
                crashlytics.recordException(t);

                progressDialog.dismiss();

                new MaterialDialog(AssistiveTechnologyListActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                        .title(null, "Error Occurred")
                        .message(null, "An error occurred whilst retrieving the technologies list. Please try again later.", null)
                        .cancelable(false)
                        .positiveButton(null, "Dismiss", materialDialog -> {
                            materialDialog.dismiss();
                            finish();
                            return null;
                        }).show();
            }
        });

        mAdapter.notifyDataSetChanged();
    }

    @Override
    public void finish() {
        super.finish();
        if(mActionType == ACTION_DELETE || mActionType == ACTION_EDIT) {
            getWindow().setEnterTransition(new Slide());
            getWindow().setExitTransition(new Slide());
        }
    }

    public void returnHome(View view){
        Intent i = new Intent(this, HomeActivity.class);
        i.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP|Intent.FLAG_ACTIVITY_NEW_TASK);
        startActivity(i);
        finish();
    }

    private void deleteTechnologies(List<Integer> deletingIDs){
        MaterialDialog progressDialog = Dialogs.generateProgressDialog(this, "Submitting...");
        progressDialog.show();

        AT4SENDAPIInterface apiService =
                AT4SENDAPIClient.getClient().create(AT4SENDAPIInterface.class);

        Call<StatusResponse> call = apiService.deleteTechnologies(true, deletingIDs);
        call.enqueue(new Callback<StatusResponse>() {
            @Override
            public void onResponse(@NotNull Call<StatusResponse>call, @NotNull Response<StatusResponse> response) {
                if(response.body() != null) {
                    String status = response.body().getStatus();

                    if (status.equalsIgnoreCase("success")) {
                        Drawable drawable = ResourcesCompat.getDrawable(getResources(), R.drawable.ic_checkbox_marked, null);
                        if(drawable != null) {
                            drawable.setTintList(ColorStateList.valueOf(ResourcesCompat.getColor(getResources(), R.color.md_green_600, null)));
                        }
                        new MaterialDialog(AssistiveTechnologyListActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                                .title(null, "Success")
                                .icon(null, drawable)
                                .message(null, response.body().getMessage(), null)
                                .cancelable(false)
                                .positiveButton(null, "Finish", materialDialog -> {
                                    materialDialog.dismiss();
                                    finish();
                                    return null;
                                }).show();
                    }else{
                        new MaterialDialog(AssistiveTechnologyListActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                                .title(null, "Error Occurred")
                                .message(null, String.format("%s%s%s", "An error occurred whilst deleting Assistive Technologies: ", "\""+response.body().getMessage()+"\"", "Please try again later."), null)
                                .cancelable(false)
                                .positiveButton(null, "Dismiss", materialDialog -> {
                                    materialDialog.dismiss();
                                    return null;
                                }).show();
                    }
                }else{
                    new MaterialDialog(AssistiveTechnologyListActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                            .title(null, "Error Occurred")
                            .message(null, "An error occurred whilst deleting Assistive Technologies. Please try again later.", null)
                            .cancelable(false)
                            .positiveButton(null, "Dismiss", materialDialog -> {
                                materialDialog.dismiss();
                                return null;
                            }).show();
                }

                progressDialog.dismiss();
            }

            @Override
            public void onFailure(@NotNull Call<StatusResponse>call, @NotNull Throwable t) {
                // Log error here since request failed
                Log.e(TAG, t.toString());
                crashlytics.recordException(t);

                progressDialog.dismiss();

                new MaterialDialog(AssistiveTechnologyListActivity.this, MaterialDialog.getDEFAULT_BEHAVIOR())
                        .title(null, "Error Occurred")
                        .message(null, "An error occurred whilst deleting Assistive Technologies. Please try again later.", null)
                        .cancelable(false)
                        .positiveButton(null, "Dismiss", materialDialog -> {
                            materialDialog.dismiss();
                            return null;
                        }).show();
            }
        });
    }

    private void exportRecommendations(){
        Dexter.withContext(this)
                .withPermissions(Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.WRITE_EXTERNAL_STORAGE)
                .withListener(new MultiplePermissionsListener() {

                    @Override
                    public void onPermissionsChecked(MultiplePermissionsReport multiplePermissionsReport) {
                        if(multiplePermissionsReport.areAllPermissionsGranted()){
                            createRecommendationPDFDocument();
                        }else if(multiplePermissionsReport.isAnyPermissionPermanentlyDenied()) {
                            showSettingsDialog();
                        }else{
                            shouldShowRequestPermissionRationale(Manifest.permission.WRITE_EXTERNAL_STORAGE);
                        }
                    }

                    @Override
                    public void onPermissionRationaleShouldBeShown(List<PermissionRequest> list, PermissionToken permissionToken) {
                        permissionToken.continuePermissionRequest();
                    }
                }).withErrorListener(error -> {
                    Snackbar sB = Snackbar.make(findViewById(R.id.mainView), "An error occurred!", Snackbar.LENGTH_SHORT);
                    SnackbarHelper.configSnackbar(this, sB);
                    sB.show();
                }).check();
    }

    private void showSettingsDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("Need Permissions");
        builder.setMessage("EduAbility needs permission to access your phone's storage to use this feature. You can grant them in app settings.");
        builder.setPositiveButton("GOTO SETTINGS", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.cancel();
                openSettings();
            }
        });
        builder.setNegativeButton("Cancel", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.cancel();
            }
        });
        builder.show();

    }

    // navigating user to app settings
    private void openSettings() {
        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        Uri uri = Uri.fromParts("package", getPackageName(), null);
        intent.setData(uri);
        settingsLauncher.launch(intent);
    }

    private void createRecommendationPDFDocument() {
        PrintAttributes printAttributes = new PrintAttributes.Builder()
                .setColorMode(PrintAttributes.COLOR_MODE_MONOCHROME)
                .setMediaSize(PrintAttributes.MediaSize.ISO_A4)
                .setResolution(new PrintAttributes.Resolution("normal", PRINT_SERVICE, 300, 300))
                .setMinMargins(PrintAttributes.Margins.NO_MARGINS)
                .build();

        // open a new document
        PrintedPdfDocument document = new PrintedPdfDocument(AssistiveTechnologyListActivity.this,
                printAttributes);

        // create a new page from the PageInfo
        PdfDocument.Page page = document.startPage(1);

        // draw something on the page
        //FIXME: i/183 multipage support
        View content = mMainRecyclerView;

        PrintAttributes.MediaSize pageSize = printAttributes.getMediaSize();
        PrintAttributes.Resolution resolution = printAttributes.getResolution();
        int hdpi = resolution.getHorizontalDpi();
        int vdpi = resolution.getVerticalDpi();
        int availableWidth = pageSize.getWidthMils() * hdpi / 1000;

        content.setLayoutParams(new FrameLayout.LayoutParams(availableWidth, ConstraintLayout.LayoutParams.WRAP_CONTENT));

        int measureWidth = View.MeasureSpec.makeMeasureSpec(availableWidth, View.MeasureSpec.EXACTLY);
        content.measure(measureWidth, View.MeasureSpec.UNSPECIFIED);

        content.layout(0, 0, content.getMeasuredWidth(), content.getMeasuredHeight());
        page.getCanvas().scale(72f / hdpi, 72f / vdpi);
        content.draw(page.getCanvas());

        // finish the page
        document.finishPage(page);

        // write the PDF document to a file
        File pdfDirPath = new File(getFilesDir(), "exported-pdfs");
        if(pdfDirPath.exists() || pdfDirPath.mkdirs()) {
            SimpleDateFormat sDF = new SimpleDateFormat("yyyy-MM-dd - HH:mm:ss", Locale.getDefault());

            final String[] fileName = {"Recommendation-"+sDF.format(Calendar.getInstance().getTime())};

            MaterialDialog dialog = DialogInputExtKt.input(
                    new MaterialDialog(this, MaterialDialog.getDEFAULT_BEHAVIOR()).title(null, "Export As...").message(null, "Name this recommendation file:", null),
                    "Recommendation Name...", null,
                    fileName[0], null,
                    InputType.TYPE_CLASS_TEXT, 55, true, false, null).positiveButton(null, "Save", dialog1 -> {
                        fileName[0] = DialogInputExtKt.getInputField(dialog1).getText().toString();

                        try {
                            File file = new File(pdfDirPath, fileName[0] +".pdf");

                            Uri contentUri = FileProvider.getUriForFile(AssistiveTechnologyListActivity.this, "uk.co.verscreative.at4send", file);

                            OutputStream os = new BufferedOutputStream(new FileOutputStream(file));
                            document.writeTo(os);
                            document.close();
                            os.close();
                            shareDocument(contentUri);
                        } catch (IOException e) {
                            throw new RuntimeException("Error generating file", e);
                        }

                        //close the document
                        document.close();

                        return null;
            }).negativeButton(null, "Cancel", dialog1 -> {
                document.close();
                return null;
            });

            DialogInputExtKt.getInputField(dialog).setSelectAllOnFocus(true);
            dialog.show();
        }else{
            document.close();
        }

    }

    private void shareDocument(Uri uri) {
        Intent shareIntent = new Intent(Intent.ACTION_VIEW, uri);
        startActivity(shareIntent);
    }

    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        if (mMainRecyclerView != null && mMainRecyclerView.getLayoutManager() != null) {
            savedListPosition = ((LinearLayoutManager) mMainRecyclerView.getLayoutManager()).findFirstVisibleItemPosition();
        }
        outState.putInt(SAVED_LIST_POSITION, savedListPosition);
    }

    @Override
    protected void onRestoreInstanceState(@NonNull Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);
        savedListPosition = savedInstanceState.getInt(SAVED_LIST_POSITION, 0);
    }

    /**
     * Applies high contrast settings to the results UI
     */
    private void applyHighContrastToResults() {
        // Apply contrast to the main view
        View mainView = findViewById(R.id.mainView);
        if (mainView != null) {
            ContrastUtils.applyContrastToViewHierarchy(mainView);
        }
        
        // Apply contrast to results explanatory text
        if (mResultsExplanatoryText != null) {
            mResultsExplanatoryText.setTextColor(TextSizeUtils.getTextColor(this).getDefaultColor());
        }
        
        if (mResultsExplanatoryHeading != null) {
            mResultsExplanatoryHeading.setTextColor(TextSizeUtils.getTextColor(this).getDefaultColor());
        }
        
        // Apply contrast to the results card
        if (mResultsCardView != null) {
            mResultsCardView.setCardBackgroundColor(TextSizeUtils.getBackgroundColor(this).getDefaultColor());
        }
        
        // Apply contrast to the RecyclerView items
        if (mMainRecyclerView != null) {
            // Apply to visible items
            for (int i = 0; i < mMainRecyclerView.getChildCount(); i++) {
                View itemView = mMainRecyclerView.getChildAt(i);
                if (itemView != null) {
                    ContrastUtils.applyContrastToViewHierarchy(itemView);
                    
                    // Find specific views within the item
                    TextView nameView = itemView.findViewById(R.id.assistivename);
                    if (nameView != null) {
                        nameView.setTextColor(TextSizeUtils.getTextColor(this).getDefaultColor());
                    }
                    
                    TextView descView = itemView.findViewById(R.id.assistiveTechnologyShortDescription);
                    if (descView != null) {
                        descView.setTextColor(TextSizeUtils.getTextColor(this).getDefaultColor());
                    }
                }
            }
            
            // Force adapter to refresh all items
            if (mAdapter != null) {
                mAdapter.notifyDataSetChanged();
            }
        }
        
        // Apply contrast to buttons
        MaterialButton returnHome = findViewById(R.id.returnHome);
        if (returnHome != null) {
            returnHome.setTextColor(TextSizeUtils.getTextColor(this).getDefaultColor());
            returnHome.setBackgroundTintList(TextSizeUtils.getBackgroundColor(this));
        }
        
        MaterialButton deleteButton = findViewById(R.id.deleteTechnologies);
        if (deleteButton != null) {
            deleteButton.setTextColor(TextSizeUtils.getTextColor(this).getDefaultColor());
            deleteButton.setBackgroundTintList(TextSizeUtils.getBackgroundColor(this));
        }
        
        MaterialButton exportButton = findViewById(R.id.exportTechnologies);
        if (exportButton != null) {
            exportButton.setTextColor(TextSizeUtils.getTextColor(this).getDefaultColor());
            exportButton.setBackgroundTintList(TextSizeUtils.getBackgroundColor(this));
        }

        // Apply contrast to read-aloud buttons
        ViewGroup contentView = findViewById(R.id.mainView);
        if (contentView != null) {
            for (int i = 0; i < contentView.getChildCount(); i++) {
                View child = contentView.getChildAt(i);
                if (child instanceof ViewGroup && child.findViewById(R.id.read_aloud_button) != null) {
                    ImageButton readAloudButton = child.findViewById(R.id.read_aloud_button);
                    readAloudButton.setColorFilter(
                        TextSizeUtils.getTextColor(this).getDefaultColor(),
                        android.graphics.PorterDuff.Mode.SRC_IN
                    );
                }
            }
        }
    }
}
