package uk.ac.bournemouthuniversity.at4send.models;

import com.google.gson.annotations.SerializedName;

public class Manufacturer {

    @SerializedName("manufacturer_id")
    private int manufacturerID;

    @SerializedName("manufacturer_name")
    private String manufacturerName;

    @SerializedName("manufacturer_description")
    private String manufacturerDescription;

    @SerializedName("manufacturer_website")
    private String manufacturerWebsite;

    public Manufacturer(int manufacturerID, String manufacturerName, String manufacturerDescription, String manufacturerWebsite) {
        this.manufacturerID = manufacturerID;
        this.manufacturerName = manufacturerName;
        this.manufacturerDescription = manufacturerDescription;
        this.manufacturerWebsite = manufacturerWebsite;
    }

    public int getManufacturerID() {
        return manufacturerID;
    }

    public void setManufacturerID(int manufacturerID) {
        this.manufacturerID = manufacturerID;
    }

    public String getManufacturerName() {
        return manufacturerName;
    }

    public void setManufacturerName(String manufacturerName) {
        this.manufacturerName = manufacturerName;
    }

    public String getManufacturerDescription() {
        return manufacturerDescription;
    }

    public void setManufacturerDescription(String manufacturerDescription) {
        this.manufacturerDescription = manufacturerDescription;
    }

    public String getManufacturerWebsite() {
        return manufacturerWebsite;
    }

    public void setManufacturerWebsite(String manufacturerWebsite) {
        this.manufacturerWebsite = manufacturerWebsite;
    }
}
