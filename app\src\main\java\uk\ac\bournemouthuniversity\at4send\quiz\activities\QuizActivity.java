package uk.ac.bournemouthuniversity.at4send.quiz.activities;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import androidx.activity.OnBackPressedCallback;

import android.content.Intent;
import android.content.res.ColorStateList;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListAdapter;
import android.widget.ListView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import android.os.Looper;

import com.afollestad.materialdialogs.MaterialDialog;
import com.bumptech.glide.Glide;
import com.google.android.material.button.MaterialButton;

import java.util.Collections;
import java.util.List;
import java.util.Vector;

import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.BaseActivity;
import uk.ac.bournemouthuniversity.at4send.quiz.adapters.AnswerAdapter;
import uk.ac.bournemouthuniversity.at4send.quiz.helpers.QuizHolder;
import uk.ac.bournemouthuniversity.at4send.quiz.helpers.QuizSession;
import uk.ac.bournemouthuniversity.at4send.quiz.helpers.SessionUtils;
import uk.ac.bournemouthuniversity.at4send.quiz.models.Answer;
import uk.ac.bournemouthuniversity.at4send.quiz.models.Question;
import uk.ac.bournemouthuniversity.at4send.utils.ContrastUtils;
import uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils;
import uk.ac.bournemouthuniversity.at4send.quiz.functions.Quiz;
import uk.ac.bournemouthuniversity.at4send.utils.ReadAloudUtils;

public class QuizActivity extends BaseActivity {

    public static final String RECORD_PREF_KEY = "RECORD";

    public static final String SESSION_PREF_KEY = "SESSION";

    protected ImageView questionImageView;
    protected TextView questionTextView;
    protected RelativeLayout lastQuestionNeutralView;
    protected RelativeLayout lastQuestionOkView;
    protected RelativeLayout lastQuestionWrongView;
    protected RelativeLayout consecutiveView;
    protected RelativeLayout recordView;

    protected TextView consecutiveTextView;
    protected TextView recordTextView;

    protected ListView mListView;
    protected ListAdapter adapter;
    protected Question question;
    protected boolean doubleBackToExitPressedOnce;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_quiz);
        initViews();

        getOnBackPressedDispatcher().addCallback(this, new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                if (doubleBackToExitPressedOnce) {
                    QuizHolder.getInstance().setExited(true);
                    finish();
                    return;
                }

                doubleBackToExitPressedOnce = true;
                Toast.makeText(QuizActivity.this, "Please click BACK again to exit", Toast.LENGTH_SHORT).show();

                new Handler(Looper.getMainLooper()).postDelayed(() -> 
                    doubleBackToExitPressedOnce = false
                , 3000);
            }
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        // Refresh UI when returning to activity in case accessibility settings changed
        updateUIForAccessibility();

        if (!QuizHolder.isInit()) {
            // If there's no quiz instance, just return to prevent crashes
            finish();
            return;
        }

        Quiz quiz = QuizHolder.getInstance();
        if (quiz == null || quiz.isExited()) {
            finish();
            return;
        }

        try {
            loadSession();
            loadRecord();
            if (this.question == null) {
                displayNextQuestion();
            }
        } catch (Exception e) {
            finish();
        }
    }

    @Override
    public void onPause() {
        super.onPause();

        Quiz quiz = QuizHolder.getInstance();
        if (quiz == null) {
            return;
        }

        try {
            QuizSession session = quiz.getSession();
            if (session != null) {
                SessionUtils.setSession(this, session);
            }

            int record = quiz.getRecord();
            SessionUtils.setRecord(this, record);
        } catch (Exception e) {
            // Log error if needed but don't crash
        }
    }

    public void initViews() {
        questionImageView = (ImageView) findViewById(R.id.questionImage);
        questionTextView = (TextView) findViewById(R.id.question);
        lastQuestionNeutralView = (RelativeLayout) findViewById(R.id.lastQuestionNeutral);
        lastQuestionOkView = (RelativeLayout) findViewById(R.id.lastQuestionOk);
        lastQuestionWrongView = (RelativeLayout) findViewById(R.id.lastQuestionWrong);
        consecutiveView = (RelativeLayout) findViewById(R.id.consecutiveQuestions);
        recordView = (RelativeLayout) findViewById(R.id.record);
        lastQuestionNeutralView.setVisibility(View.VISIBLE);
        lastQuestionOkView.setVisibility(View.GONE);
        lastQuestionWrongView.setVisibility(View.GONE);


        consecutiveTextView = (TextView) findViewById(R.id.consecutiveQuestionsText);
        recordTextView = (TextView) findViewById(R.id.recordText);

        mListView = (ListView) findViewById(R.id.answer_list);

        initFeedbackToastMessages();

        // add read aloud buttons to question text
        ReadAloudUtils.addReadAloudButton(this, questionTextView);
        // ReadAloudUtils.addReadAloudButton(this, consecutiveTextView);
        // ReadAloudUtils.addReadAloudButton(this, recordTextView);

        ((MaterialButton) findViewById(R.id.returnHome)).setOnClickListener(v -> {
            MaterialDialog dialog = new MaterialDialog(this, MaterialDialog.getDEFAULT_BEHAVIOR())
                    .title(null, "Confirmation")
                    .message(null, "Are you sure you want to return to the Quiz menu, all your progress on this quiz will be lost.", null)
                    .positiveButton(null, "No, Cancel", materialDialog -> {
                        materialDialog.dismiss();
                        return null;
                    })
                    .negativeButton(null, "Yes, Return", materialDialog -> {
                        materialDialog.dismiss();
                        QuizHolder.getInstance().setExited(true);
                        finish();
                        return null;
                    });
            if (ContrastUtils.isHighContrastEnabled(this)) {
                ContrastUtils.applyContrastToDialog(dialog);
            }
            
            dialog.show();
        });
    }

    public void initFeedbackToastMessages() {
        setOnClickToast(lastQuestionNeutralView,"This bubble shows whether you answered the last question correctly or wrongly. If the bubble is grey it means that you did not answer any question yet.");
        setOnClickToast(lastQuestionOkView,"The green bubble shows that your last answer was CORRECT. Great.");
        setOnClickToast(lastQuestionWrongView,"The red bubble shows that your last answer was WRONG. Pity.");
        setOnClickToast(consecutiveView,"The yellow bubble shows how many CONSECUTIVE correct answers you just gave.Go Go Go.");
        setOnClickToast(recordView,"The blue bubble shows the RECORD of consecutive correct answers. Beat it!");
    }

    public void setOnClickToast(View view, final String message) {
        view.setOnClickListener(arg0 -> Toast.makeText(getApplicationContext(), message, Toast.LENGTH_LONG).show());
    }

    public void displayNextQuestion() {
        if (!QuizHolder.isInit()) {
            finish();
            return;
        }

        // Use a background thread to prepare the question
        new Thread(() -> {
            final Question nextQuestion = QuizHolder.getInstance().getQuestion();

            runOnUiThread(() -> {
                if (nextQuestion == null) {
                    endQuiz();
                    return;
                }

                this.question = nextQuestion;
                try {
                    displayQuestion(question);
                    showFeedback(QuizHolder.getInstance().getSession(), QuizHolder.getInstance().getRecord());
                } catch (Exception e) {
                    MaterialDialog dialog = new MaterialDialog(this, MaterialDialog.getDEFAULT_BEHAVIOR())
                            .title(null, "Error Occurred")
                            .message(null, "An error occurred while displaying the question. Please try again.", null)
                            .cancelable(false)
                            .positiveButton(null, "Dismiss", materialDialog -> {
                                materialDialog.dismiss();
                                finish();
                                return null;
                            });
                            
                    if (ContrastUtils.isHighContrastEnabled(this)) {
                        ContrastUtils.applyContrastToDialog(dialog);
                    }
                    
                    dialog.show();
                }
            });
        }).start();
    }

    public void endQuiz() {
        Intent endQuiz = new Intent(this, QuizEndActivity.class);
        startActivity(endQuiz);
    }

    public void displayQuestion(final Question question) {
        // Ensure we're on the main thread
        if (Looper.myLooper() != Looper.getMainLooper()) {
            runOnUiThread(() -> displayQuestion(question));
            return;
        }

        String questionText = question.getQuestionText();

        // Load image in background if present
        if(!question.getImageUri().isEmpty()) {
            Glide.with(this)
                .load(question.getImageUri())
                .into(questionImageView);
            questionImageView.setVisibility(View.VISIBLE);
        } else {
            questionImageView.setVisibility(View.GONE);
        }

        // Update question text
        questionTextView.post(() -> {
            styleQuestionText(questionText);
            questionTextView.setText(questionText);
        });

        View.OnClickListener listener = v -> {
            final Button button = (Button) v;
            if (!question.hasMultipleCorrectAnswers()) {
                Answer answer = new Answer(button.getText().toString(),
                        true);
                boolean checkAnswer = checkAnswers(Collections.singletonList(answer));
                if (!checkAnswer) {
                    button.setEnabled(false);
                    button.setTextColor(ContextCompat.getColor(this, R.color.md_red_600));

                    //color correct button green
                    ListView listView = (ListView) v.getParent().getParent();
                    int childCount = listView.getChildCount();
                    List<Answer> correctAnswers = question.getCorrectAnswers();

                    for (int i = 0; i < childCount; i++) {
                        View linearLayoutView = listView.getChildAt(i);
                        LinearLayout linearLayout = (LinearLayout) linearLayoutView;


                        Button otherButton = (Button) linearLayout.getChildAt(0);

                        for (Answer correctAnswer : correctAnswers){
                            if (otherButton.getText().equals(correctAnswer.getText())){
                                otherButton.setTextColor(ContextCompat.getColor(this, R.color.md_green_600));
                            }
                        }
                    }

                    Handler handler = new Handler(Looper.getMainLooper());
                    handler.postDelayed(() -> button.setSelected(true), 1500);
                } else {
                    button.setTextColor(ContextCompat.getColor(this, R.color.md_green_600));
                }
            }

            else {
                button.setSelected(!button.isSelected());

                if(button.isSelected()) {
                    button.setTextColor(ContextCompat.getColor(this, R.color.md_blue_600));
                    button.setBackgroundTintList(ColorStateList.valueOf(ContextCompat.getColor(this, R.color.md_blue_50)));
                }else{
                    button.setTextColor(ContextCompat.getColor(this, R.color.grey900));
                    button.setBackgroundTintList(ColorStateList.valueOf(ContextCompat.getColor(this, R.color.grey100)));
                }

                ListView listView = (ListView) v.getParent().getParent();
                int childCount = listView.getChildCount();
                List<Answer> answers = new Vector<>();
                List<Button> answersButtons = new Vector<>();
                for (int i = 0; i < childCount; i++) {
                    View linearLayoutView = listView.getChildAt(i);
                    LinearLayout linearLayout = (LinearLayout) linearLayoutView;

                    Button otherButton = (Button) linearLayout.getChildAt(0);

                    if (otherButton.isSelected()) {
                        Answer answer = new Answer(otherButton.getText().toString(), true);
                        answers.add(answer);
                        answersButtons.add(otherButton);
                    }
                }
                if (question.getNumberOfCorrectAnswers() == answers.size()) {
                    boolean checkAnswer = checkAnswers(answers);
                    if (!checkAnswer) {
                        for (final Button answerButton : answersButtons) {
                            answerButton.setEnabled(false);
                            answerButton.setSelected(false);
                            answerButton.setTextColor(ContextCompat.getColor(this, R.color.answer_incorrect));

                            Handler handler = new Handler(Looper.getMainLooper());
                            handler.postDelayed(() -> answerButton.setEnabled(true), 1000);
                        }
                    } else {
                        for (final Button answerButton : answersButtons) {
                            answerButton.setTextColor(ContextCompat.getColor(this, R.color.answer_correct));
                        }
                    }
                }
            }

        };

        // unsort answers
        List<Answer> answers = question.getAnswers();
        //Collections.shuffle(answers);

        adapter = new AnswerAdapter(this, R.id.answer, answers, listener);

        mListView.setAdapter(adapter);
        mListView.setDividerHeight(0);
    }

    private void styleQuestionText(String questionText) {

        questionTextView.setText(questionText);
        /*
        int screenSize = getResources().getConfiguration().screenLayout &
                Configuration.SCREENLAYOUT_SIZE_MASK;

        int maxCharsForLine;
        switch(screenSize) {
            case Configuration.SCREENLAYOUT_SIZE_LARGE:
                maxCharsForLine = 35;
                break;
            case Configuration.SCREENLAYOUT_SIZE_SMALL:
                maxCharsForLine = 20;
                break;
            default:
                maxCharsForLine = 25;
        }

        String formattedText = TextUtils.formatText(questionText, maxCharsForLine);
        questionTextView.setText(formattedText);

        int numberOfLines = TextUtils.numberOfLines(formattedText);
        int length = questionText.length();

        if (length<=maxCharsForLine) {
            if (length<=17) {
                questionTextView.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimension(R.dimen.question_big_size));
            }
            else {
                questionTextView.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimension(R.dimen.question_medium_size));

            }
        }
        else {
            if (numberOfLines==1) {
                questionTextView.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimension(R.dimen.question_medium_size));
            }
            else {
                if (numberOfLines==2) {
                    questionTextView.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimension(R.dimen.question_small_size));
                }
                else {
                    questionTextView.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimension(R.dimen.question_very_small_size));
                }
            }
        }
         */
    }

    public boolean checkAnswers(List<Answer> answers) {
        boolean checkAnswer = QuizHolder.getInstance()
                .checkAnswers(question, answers,true);
        if (checkAnswer) {
            showFeedback(checkAnswer, "Correct");
            displayNextQuestion();
        } else {
            showFeedback(checkAnswer, "Wrong");
            displayNextQuestion();
        }
        return checkAnswer;
    }

    public void showFeedback(boolean b, String string) {
        if (b) {
            lastQuestionNeutralView.setVisibility(View.GONE);
            lastQuestionOkView.setVisibility(View.VISIBLE);
            lastQuestionWrongView.setVisibility(View.GONE);
        } else {
            lastQuestionNeutralView.setVisibility(View.GONE);
            lastQuestionOkView.setVisibility(View.GONE);
            lastQuestionWrongView.setVisibility(View.VISIBLE);
        }
    }

    public void showFeedback(QuizSession session, int record) {

        Integer totalAttempts = session.getTotalAttempts();
        Integer consecutiveAttempts = session.getConsecutiveAttempts();
        if (totalAttempts == 0) {
            lastQuestionNeutralView.setVisibility(View.VISIBLE);
            lastQuestionOkView.setVisibility(View.GONE);
            lastQuestionWrongView.setVisibility(View.GONE);
        } else {
            if (consecutiveAttempts > 0) {
                lastQuestionNeutralView.setVisibility(View.GONE);
                lastQuestionOkView.setVisibility(View.VISIBLE);
                lastQuestionWrongView.setVisibility(View.GONE);
            } else {
                lastQuestionNeutralView.setVisibility(View.GONE);
                lastQuestionOkView.setVisibility(View.GONE);
                lastQuestionWrongView.setVisibility(View.VISIBLE);
            }

        }

        consecutiveTextView.setText(Integer.toString(consecutiveAttempts));
        recordTextView.setText(Integer.toString(record));

    }

    protected void loadRecord() {
        Quiz quiz = QuizHolder.getInstance();
        if (quiz == null) {
            return;
        }
        
        int record = quiz.getRecord();
        SessionUtils.setRecord(this, record);
    }

    protected void loadSession() {
        Quiz quiz = QuizHolder.getInstance();
        if (quiz == null || quiz.getSession() == null) {
            return;
        }
        
        QuizSession session = quiz.getSession();
        SessionUtils.setSession(this, session);
    }

    private void styleAnswerButton(Button button, String text) {
        button.setText(text);
        
        // Apply high contrast if enabled
        if (TextSizeUtils.isHighContrastEnabled(this)) {
            button.setTextColor(ContextCompat.getColor(this, R.color.grey900));
            button.setBackgroundTintList(ColorStateList.valueOf(ContextCompat.getColor(this, R.color.white)));
        } else {
            button.setTextColor(ContextCompat.getColor(this, R.color.grey600));
            button.setBackgroundTintList(ColorStateList.valueOf(ContextCompat.getColor(this, R.color.grey100)));
        }
    }

    protected void updateUIForAccessibility() {
        // Apply high contrast colors if enabled
        boolean highContrast = TextSizeUtils.isHighContrastEnabled(this);
        
        questionTextView.setTextColor(TextSizeUtils.getTextColor(this));
        View rootView = findViewById(R.id.root_layout);
        rootView.setBackgroundTintList(TextSizeUtils.getBackgroundColor(this));
        
        // Refresh answer buttons
        ListView answerList = findViewById(R.id.answer_list);
        if (answerList != null) {
            for (int i = 0; i < answerList.getChildCount(); i++) {
                View child = answerList.getChildAt(i);
                if (child instanceof Button) {
                    styleAnswerButton((Button)child, ((Button)child).getText().toString());
                }
            }
        }
    }
}
