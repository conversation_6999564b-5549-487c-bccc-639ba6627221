package uk.ac.bournemouthuniversity.at4send.adapters.training;

import android.content.Context;
import android.graphics.Color;
import android.content.res.ColorStateList;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ImageButton;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import kotlin.Pair;
import uk.ac.bournemouthuniversity.at4send.R;
import uk.ac.bournemouthuniversity.at4send.utils.ContrastUtils;
import uk.ac.bournemouthuniversity.at4send.utils.TextSizeUtils;
import uk.ac.bournemouthuniversity.at4send.utils.ReadAloudUtils;

public class TrainingHelpSlideAdapter extends RecyclerView.Adapter<TrainingHelpSlideAdapter.SliderViewHolder> {

    private final List<Pair<String, String>> data;

    public TrainingHelpSlideAdapter(List<Pair<String, String>> data) {
        this.data = data;
    }

    @NonNull
    @Override
    public TrainingHelpSlideAdapter.SliderViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.training_help_slide, parent, false);
        return new SliderViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull TrainingHelpSlideAdapter.SliderViewHolder holder, int position) {
        Pair<String, String> item = data.get(position);
        holder.slideTitle.setText(item.getFirst());
        holder.slideContentText.setText(item.getSecond());
        
        // Apply text size scaling
        applyTextSizeScaling(holder);
        
        // Apply high contrast if enabled
        if (ContrastUtils.isHighContrastEnabled(holder.itemView.getContext())) {
            applyHighContrastToSlide(holder);
        } else {
            // Ensure normal mode is applied if high contrast is disabled
            resetNormalContrast(holder);
        }

        // Make both title and content read-aloud buttons visible
        View titleParent = (View) holder.slideTitle.getParent();
        View contentParent = (View) holder.slideContentText.getParent();
        
        if (titleParent != null) {
            View titleReadAloudBtn = ((ViewGroup) titleParent).findViewById(R.id.read_aloud_button);
            if (titleReadAloudBtn != null) {
                titleReadAloudBtn.setVisibility(View.VISIBLE);
                if (titleReadAloudBtn instanceof ImageButton) {
                    ((ImageButton) titleReadAloudBtn).setImageTintList(ColorStateList.valueOf(
                        ContextCompat.getColor(holder.itemView.getContext(), com.mikepenz.materialize.R.color.colorPrimary)
                    ));
                }
            }
        }
        
        if (contentParent != null) {
            View contentReadAloudBtn = ((ViewGroup) contentParent).findViewById(R.id.read_aloud_button);
            if (contentReadAloudBtn != null) {
                contentReadAloudBtn.setVisibility(View.VISIBLE);
                if (contentReadAloudBtn instanceof ImageButton) {
                    ((ImageButton) contentReadAloudBtn).setImageTintList(ColorStateList.valueOf(
                        ContextCompat.getColor(holder.itemView.getContext(), com.mikepenz.materialize.R.color.colorPrimary)
                    ));
                }
            }
        }
    }

    /**
     * Applies text size scaling based on user preferences
     */
    private void applyTextSizeScaling(SliderViewHolder holder) {
        Context context = holder.itemView.getContext();
        float scale = TextSizeUtils.getTextSizeScale(context);
        
        // Get the base text sizes (in sp)
        float baseTitleSize = 24f; // Default title size
        float baseContentSize = 15f; // Default content size
        
        // Apply the scale factor
        float scaledTitleSize = baseTitleSize * scale;
        float scaledContentSize = baseContentSize * scale;
        
        // Set the scaled text sizes
        holder.slideTitle.setTextSize(TypedValue.COMPLEX_UNIT_SP, scaledTitleSize);
        holder.slideContentText.setTextSize(TypedValue.COMPLEX_UNIT_SP, scaledContentSize);
    }

    /**
     * Applies high contrast settings to the slide elements
     */
    private void applyHighContrastToSlide(SliderViewHolder holder) {
        Context context = holder.itemView.getContext();
        
        // Apply contrast to the root layout
        View rootView = holder.itemView.findViewById(R.id.slideRootLayout);
        if (rootView != null) {
            rootView.setBackgroundTintList(TextSizeUtils.getBackgroundColor(context));
        }
        
        // Apply contrast to the slide content container
        View slideContent = holder.itemView.findViewById(R.id.slideContent);
        if (slideContent != null) {
            // Make content background transparent to let root background show through
            slideContent.setBackgroundColor(android.graphics.Color.TRANSPARENT);
        }
        
        // Apply text color to title and content
        holder.slideTitle.setTextColor(TextSizeUtils.getTextColor(context).getDefaultColor());
        holder.slideContentText.setTextColor(TextSizeUtils.getTextColor(context).getDefaultColor());
        
        // Apply tint to the help icon
        ImageView helpIcon = holder.itemView.findViewById(R.id.slideHelpIcon);
        if (helpIcon != null) {
            helpIcon.setColorFilter(
                TextSizeUtils.getTextColor(context).getDefaultColor(),
                android.graphics.PorterDuff.Mode.SRC_IN
            );
        }
        
        // Hide background image in high contrast mode for better readability
        ImageView backgroundImage = holder.itemView.findViewById(R.id.slideBackground);
        if (backgroundImage != null) {
            backgroundImage.setVisibility(View.GONE);
        }

        // Apply contrast to read-aloud buttons
        View titleParent = (View) holder.slideTitle.getParent();
        View contentParent = (View) holder.slideContentText.getParent();
        
        if (titleParent != null) {
            View titleReadAloudBtn = ((ViewGroup) titleParent).findViewById(R.id.read_aloud_button);
            if (titleReadAloudBtn instanceof ImageButton) {
                ((ImageButton) titleReadAloudBtn).setImageTintList(ColorStateList.valueOf(
                    TextSizeUtils.getTextColor(context).getDefaultColor()
                ));
            }
        }
        
        if (contentParent != null) {
            View contentReadAloudBtn = ((ViewGroup) contentParent).findViewById(R.id.read_aloud_button);
            if (contentReadAloudBtn instanceof ImageButton) {
                ((ImageButton) contentReadAloudBtn).setImageTintList(ColorStateList.valueOf(
                    TextSizeUtils.getTextColor(context).getDefaultColor()
                ));
            }
        }
    }

    /**
     * Resets the view to normal contrast mode
     */
    private void resetNormalContrast(SliderViewHolder holder) {
        Context context = holder.itemView.getContext();
        
        // Reset background
        View rootView = holder.itemView.findViewById(R.id.slideRootLayout);
        if (rootView != null) {
            rootView.setBackgroundColor(context.getResources().getColor(android.R.color.white));
        }
        
        // Show background image
        ImageView backgroundImage = holder.itemView.findViewById(R.id.slideBackground);
        if (backgroundImage != null) {
            backgroundImage.setVisibility(View.VISIBLE);
        }
        
        // Reset text colors - using standard Android colors as fallback
        holder.slideTitle.setTextColor(context.getResources().getColor(android.R.color.black));
        holder.slideContentText.setTextColor(context.getResources().getColor(android.R.color.secondary_text_light));
        
        // Reset help icon tint - using a hardcoded color as last resort
        ImageView helpIcon = holder.itemView.findViewById(R.id.slideHelpIcon);
        if (helpIcon != null) {
            helpIcon.setColorFilter(
                android.graphics.Color.rgb(238, 238, 238),
                android.graphics.PorterDuff.Mode.SRC_IN
            );
        }
    }

    @Override
    public int getItemCount() {
        return data.size();
    }

    public class SliderViewHolder extends RecyclerView.ViewHolder {
        public AppCompatTextView slideTitle, slideContentText;
        public SliderViewHolder(View view) {
            super(view);
            slideTitle = view.findViewById(R.id.slideTitle);
            slideContentText = view.findViewById(R.id.slideContentText);
            
            // Add read-aloud buttons in the ViewHolder constructor
            Context context = view.getContext();
            ImageButton titleReadAloudBtn = ReadAloudUtils.addReadAloudButton(context, slideTitle);
            ImageButton contentReadAloudBtn = ReadAloudUtils.addReadAloudButton(context, slideContentText);
            
            // Ensure buttons are visible immediately
            if (titleReadAloudBtn != null) {
                titleReadAloudBtn.setVisibility(View.VISIBLE);
                titleReadAloudBtn.setImageTintList(ColorStateList.valueOf(
                    ContextCompat.getColor(context, com.mikepenz.materialize.R.color.colorPrimary)
                ));
            }
            
            if (contentReadAloudBtn != null) {
                contentReadAloudBtn.setVisibility(View.VISIBLE);
                contentReadAloudBtn.setImageTintList(ColorStateList.valueOf(
                    ContextCompat.getColor(context, com.mikepenz.materialize.R.color.colorPrimary)
                ));
            }
        }
    }
}
