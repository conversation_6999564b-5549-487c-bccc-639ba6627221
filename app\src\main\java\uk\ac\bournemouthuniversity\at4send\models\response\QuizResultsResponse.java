package uk.ac.bournemouthuniversity.at4send.models.response;

import com.google.gson.annotations.SerializedName;

import java.util.List;

import uk.ac.bournemouthuniversity.at4send.models.training.QuizResult;

public class QuizResultsResponse {

    @SerializedName("results")
    private List<QuizResult> quizResults;

    @SerializedName("count")
    private Integer count;

    public QuizResultsResponse(List<QuizResult> quizResults, Integer count) {
        this.quizResults = quizResults;
        this.count = count;
    }

    public List<QuizResult> getQuizResults() {
        return quizResults;
    }

    public void setQuizResults(List<QuizResult> quizResults) {
        this.quizResults = quizResults;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }
}

