package uk.ac.bournemouthuniversity.at4send.models.training;

import org.jetbrains.annotations.NotNull;

import java.io.Serializable;

public class AssistiveTechnologyTraining implements Serializable {

    private String technologyName;
    private String technologyDescription;
    private String technologyBenefits;
    private String technologyLimitations;
    private String iconUrl;

    public AssistiveTechnologyTraining(String technologyName, String technologyDescription, String technologyBenefits, String technologyLimitations, String iconUrl) {
        this.technologyName = technologyName;
        this.technologyDescription = technologyDescription;
        this.technologyBenefits = technologyBenefits;
        this.technologyLimitations = technologyLimitations;
        this.iconUrl = iconUrl;
    }

    public String getTechnologyName() {
        return technologyName;
    }

    public void setTechnologyName(String technologyName) {
        this.technologyName = technologyName;
    }

    public String getTechnologyDescription() {
        return technologyDescription;
    }

    public void setTechnologyDescription(String technologyDescription) {
        this.technologyDescription = technologyDescription;
    }

    public String getTechnologyBenefits() {
        return technologyBenefits;
    }

    public void setTechnologyBenefits(String technologyBenefits) {
        this.technologyBenefits = technologyBenefits;
    }

    public String getTechnologyLimitations() {
        return technologyLimitations;
    }

    public void setTechnologyLimitations(String technologyLimitations) {
        this.technologyLimitations = technologyLimitations;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    @NotNull
    @Override
    public String toString() {
        return technologyName + "__" + technologyDescription + "__" + technologyBenefits + "__" +
                technologyLimitations + "__" + iconUrl;
    }
}
